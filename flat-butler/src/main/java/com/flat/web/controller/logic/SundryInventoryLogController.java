package com.flat.web.controller.logic;

import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.sundry.SundryInventoryLogQueryReq;
import com.flat.logic.dto.resp.sundry.SundryInventoryLogListResp;
import com.flat.logic.service.sundry.SundryInventoryLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/sundryInventoryLog")
@Tag(name = "物品库产品进库与出库记录管理")
public class SundryInventoryLogController {

    @Resource
    private SundryInventoryLogService sundryInventoryLogService;

    @Operation(summary = "查询物品库产品进库与出库记录列表（分页）")
    @GetMapping("/listPage")
    public R<TablePage<SundryInventoryLogListResp>> listPage(SundryInventoryLogQueryReq req) {
        return R.successByData(sundryInventoryLogService.listPage(req));
    }

    @Operation(summary = "获取物品库产品进库与出库记录详细信息")
    @GetMapping(value = "/{id}")
    public R<SundryInventoryLogListResp> detail(@PathVariable("id") Long id) {
        return R.successByData(sundryInventoryLogService.detail(id));
    }
}