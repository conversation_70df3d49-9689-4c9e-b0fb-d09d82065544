package com.flat.web.controller.logic;

import com.flat.common.annotation.RepeatSubmit;
import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.VerifyReq;
import com.flat.logic.dto.req.make.RoomMakeQueryReq;
import com.flat.logic.dto.resp.make.RoomMakeDetailResp;
import com.flat.logic.dto.resp.make.RoomMakeListResp;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.service.make.RoomMakeService;
import com.flat.security.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/roomMake")
@Tag(name = "公寓看房预约管理")
public class RoomMakeController {

    @Resource
    private RoomMakeService roomMakeService;

    @Operation(summary = "管家：查询公寓看房预约列表")
    @GetMapping("/listPage")
    public R<TablePage<RoomMakeListResp>> listPage(RoomMakeQueryReq req) {
        MaiUser user = SecurityUtils.getLoginUser();
        req.setButlerId(user.getUserId());
        return R.successByData(roomMakeService.queryRespPage(req));
    }

    @Operation(summary = "获取公寓看房预约详细信息")
    @GetMapping(value = "/{id}")
    public R<RoomMakeDetailResp> detail(@PathVariable("id") Long id) {
        return R.successByData(roomMakeService.queryRespDetail(id));
    }

    @RepeatSubmit
    @Operation(summary = "管家：公寓看房预约审核")
    @PostMapping("/verify")
    public R<Object> verify(@Validated @RequestBody VerifyReq req) {
        MaiUser user = SecurityUtils.getLoginUser();
        roomMakeService.verify(req, user);
        return R.success();
    }

}
