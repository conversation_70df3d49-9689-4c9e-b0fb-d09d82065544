package com.flat.web.controller.logic;

import com.flat.common.annotation.RepeatSubmit;
import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.lease.*;
import com.flat.logic.dto.resp.lease.ContractDetailResp;
import com.flat.logic.dto.resp.lease.ContractListResp;
import com.flat.logic.service.lease.ContractService;
import com.flat.security.model.LoginUser;
import com.flat.security.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 合同管理
 */
@RestController
@RequestMapping("/contract")
@Tag(name = "合同管理", description = "合同管理相关接口")
public class ContractController {

    @Resource
    private ContractService contractService;

    //审核合同
    @RepeatSubmit
    @Operation(summary = "审核合同", description = "审核合同")
    @PostMapping("/verify")
    public R<Object> verify(@Validated @RequestBody ContractVerifyReq req) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        contractService.verify(req, loginUser);
        return R.success();
    }

    /**
     * 查询合同
     */
    @Operation(summary = "查询合同", description = "获取合同列表")
    @GetMapping("/listPage")
    public R<TablePage<ContractListResp>> listPage(ContractQueryReq req) {
        return R.successByData(contractService.queryRespPage(req));
    }

    /**
     * 根据ID查询合同详情
     */
    @Operation(summary = "获取合同详情", description = "根据合同ID获取详细信息")
    @GetMapping("/{id}")
    public R<ContractDetailResp> getById(
            @Parameter(description = "合同ID", required = true)
            @PathVariable Long id
    ) {
        return R.successByData(contractService.queryRespDetail(id));
    }
}
