package com.flat.web.controller.logic;

import java.util.List;

import com.flat.logic.dto.resp.flat.RoomDetailResp;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.flat.common.annotation.Anonymous;
import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.flat.BlockFloorQueryReq;
import com.flat.logic.dto.req.flat.RoomQueryReq;
import com.flat.logic.dto.req.flat.RoomStateQueryReq;
import com.flat.logic.dto.req.flat.RoomTreeSelectReq;
import com.flat.logic.dto.resp.NormalTreeResp;
import com.flat.logic.dto.resp.flat.RoomResp;
import com.flat.logic.dto.resp.flat.RoomStateFloorResp;
import com.flat.logic.service.flat.RoomService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

/**
 * 公寓房间
 */
@RestController
@RequestMapping("/room")
@Tag(name = "公寓房间")
public class RoomController {

    @Resource
    private RoomService roomService;

    
    @Anonymous
    @Operation(summary = "获取所有楼栋号")
    @GetMapping("/blocks")
    public R<List<String>> getAllBlockNos() {
        return R.successByData(roomService.getAllBlockNos());
    }

    @Anonymous
    @Operation(summary = "根据楼栋号获取楼层列表")
    @GetMapping("/floors")
    public R<List<Integer>> getFloorsByBlock(BlockFloorQueryReq req) {
        return R.successByData(roomService.getFloorsByBlock(req));
    }

    /**
     * 查询公寓房间列表
     */
    @Anonymous
    @Operation(summary = "查询公寓房间列表")
    @GetMapping("/listPage")
    public R<TablePage<RoomResp>> listPage(RoomQueryReq req) {
        return R.successByData(roomService.queryRespPage(req));
    }

    /**
     * 获取公寓房间详细信息
     */
    @Anonymous
    @Operation(summary = "获取公寓房间详细信息")
    @GetMapping(value = "/{id}")
    public R<RoomDetailResp> getInfo(@PathVariable("id") Long id) {
        return R.successByData(roomService.queryRespDetail(id));
    }

    /**
     * 首页搜索查询公寓房间列表
     */
    @Anonymous
    @Operation(summary = "首页搜索查询公寓房间列表")
    @GetMapping("/searchList")
    public R<TablePage<RoomResp>> searchList(@RequestParam("content") String content) {
        return R.successByData(roomService.queryNotFreePage(content));
    }

    /**
     * 查询公寓房间树(楼号/楼层/房间号三级结构)
     */
    @Operation(summary = "查询公寓房间树(楼号/楼层/房间号三级结构)")
    @PostMapping("/selectTree")
    public R<List<NormalTreeResp>> selectTree(@Validated @RequestBody RoomTreeSelectReq req) {
        return R.successByData(roomService.selectTree(req));
    }

    @Anonymous
    @Operation(summary = "查询公寓房间状态")
    @GetMapping("/states")
    public R<List<RoomStateFloorResp>> states(RoomStateQueryReq req) {
        return R.successByData(roomService.queryStates(req));
    }
}
