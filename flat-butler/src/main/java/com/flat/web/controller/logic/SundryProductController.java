package com.flat.web.controller.logic;

import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.sundry.SundryProductQueryReq;
import com.flat.logic.dto.resp.sundry.SundryProductDetailResp;
import com.flat.logic.dto.resp.sundry.SundryProductListResp;
import com.flat.logic.service.sundry.SundryProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sundryProduct")
@Tag(name = "物品库产品管理")
public class SundryProductController {

    @Resource
    private SundryProductService sundryProductService;

    @Operation(summary = "查询物品库产品列表（分页）")
    @GetMapping("/listPage")
    public R<TablePage<SundryProductListResp>> listPage(SundryProductQueryReq req) {
        return R.successByData(sundryProductService.queryRespPage(req));
    }

    @Operation(summary = "查询物品库产品列表（不分页）")
    @GetMapping("/list")
    public R<List<SundryProductListResp>> list(SundryProductQueryReq req) {
        return R.successByData(sundryProductService.queryRespList(req));
    }

    @Operation(summary = "获取物品库产品详细信息")
    @GetMapping(value = "/{id}")
    public R<SundryProductDetailResp> detail(@PathVariable("id") Long id) {
        return R.successByData(sundryProductService.detail(id));
    }


}