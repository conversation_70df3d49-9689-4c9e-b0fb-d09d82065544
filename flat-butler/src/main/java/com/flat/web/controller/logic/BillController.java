package com.flat.web.controller.logic;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.finance.BillQueryReq;
import com.flat.logic.dto.resp.finance.BillResp;
import com.flat.logic.service.finance.BillService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

@Tag(name = "账单管理")
@RestController
@RequestMapping("/bill")
public class BillController {

    @Resource
    private BillService billService;

    @GetMapping("/list")
    @Operation(summary = "获取账单列表")
    public R<TablePage<BillResp>> listPage(BillQueryReq req) {
        return R.successByData(billService.queryRespPage(req));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取账单详情")
    public R<BillResp> getDetail(@PathVariable Long id) {
        return R.successByData(billService.queryResp(id));
    }

}
