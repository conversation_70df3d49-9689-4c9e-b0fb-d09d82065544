package com.flat.web.controller.logic;

import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.sundry.SundryCategoryQueryReq;
import com.flat.logic.entity.sundry.SundryCategory;
import com.flat.logic.service.sundry.SundryCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sundryCategory")
@Tag(name = "物品库类别管理")
public class SundryCategoryController {

    @Resource
    private SundryCategoryService sundryCategoryService;

    @Operation(summary = "查询物品库类别列表（分页）")
    @GetMapping("/listPage")
    public R<TablePage<SundryCategory>> listPage(SundryCategoryQueryReq req) {
        return R.successByData(sundryCategoryService.listPage(req));
    }

    @Operation(summary = "查询物品库类别列表")
    @GetMapping("/list")
    public R<List<SundryCategory>> list(SundryCategoryQueryReq req) {
        return R.successByData(sundryCategoryService.list(req));
    }

    @Operation(summary = "获取物品库类别详细信息")
    @GetMapping(value = "/{id}")
    public R<SundryCategory> detail(@PathVariable("id") Long id) {
        return R.successByData(sundryCategoryService.detail(id));
    }

}
