package com.flat.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.flat.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "部门信息")
public class SysDept extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    @TableId
    private Long deptId;

    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID")
    private Long parentId;

    /**
     * 祖级列表
     */
    @Schema(description = "祖级列表")
    private String ancestors;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 1, max = 30, message = "部门名称长度不能超过30个字符")
    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deptName;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer orderNum;

    /**
     * 负责人
     */
    @Schema(description = "负责人")
    private String leader;

    /**
     * 联系电话
     */
    @Size(min = 1, max = 11, message = "联系电话长度不能超过11个字符")
    @Schema(description = "联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(min = 1, max = 50, message = "邮箱长度不能超过50个字符")
    @Schema(description = "邮箱")
    private String email;

    /**
     * 部门状态:0正常,1停用
     */
    @Schema(description = "部门状态：0=正常，1=停用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Schema(description = "删除标志：0=存在，2=已删除")
    private String delFlag;

    /**
     * 父部门名称
     */
    @Schema(description = "父部门名称")
    private String parentName;

    /**
     * 子部门
     */
    @Schema(description = "子部门列表")
    private List<SysDept> children = new ArrayList<>();

}
