package com.flat.web.controller.logic;

import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.make.RepairTypeQueryReq;
import com.flat.logic.entity.make.FlatRepairType;
import com.flat.logic.service.make.RepairTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "公寓报修类型管理")
@RestController
@RequestMapping("/repairType")
public class RepairTypeController {

    @Resource
    private RepairTypeService flatRepairsTypeService;

    /**
     * 查询公寓报修类型列表
     */
    @Operation(summary = "查询公寓报修类型列表")
    @GetMapping("/listPage")
    public R<TablePage<FlatRepairType>> listPage(RepairTypeQueryReq req) {
        return R.successByData(flatRepairsTypeService.queryPage(req));
    }
}
