package com.flat.web.controller.logic;

import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.flat.common.core.domain.R;
import com.flat.logic.dto.resp.statistics.SimpleLeaseStatisticsResp;
import com.flat.logic.service.statistics.StatisticsService;
import com.flat.security.model.LoginUser;
import com.flat.security.utils.SecurityUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 统计数据控制器
 */
@RestController
@RequestMapping("/statistics")
@Slf4j
@Tag(name = "统计数据接口")
public class StatisticsController {

    @Resource
    private StatisticsService statisticsService;

    /**
     * 根据用户ID查询租赁记录统计信息
     *
     * @return 租赁记录统计信息列表
     */
    @GetMapping("/leaseSimple")
    @Operation(summary = "根据用户ID查询租赁记录统计信息")
    public R<List<SimpleLeaseStatisticsResp>> getLeaseStatisticsByCusId() {
        LoginUser user = SecurityUtils.getLoginUser();
        List<SimpleLeaseStatisticsResp> statisticsList = statisticsService.getLeaseStatisticsByCusId(user.getUserId());
        return R.successByData(statisticsList);
    }
}
