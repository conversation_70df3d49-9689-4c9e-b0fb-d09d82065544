package com.flat.web.controller.logic;

import com.flat.common.annotation.Anonymous;
import com.flat.common.core.domain.R;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.flat.FlatQueryReq;
import com.flat.logic.entity.flat.Flat;
import com.flat.logic.service.flat.FlatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公寓
 */
@RestController
@RequestMapping("/flat")
@Tag(name = "公寓")
public class FlatController {

    @Resource
    private FlatService flatService;

    /**
     * 查询公寓列表
     */
    @Anonymous
    @Operation(summary = "查询公寓列表（分页）")
    @GetMapping("/listPage")
    public R<TablePage<Flat>> listPage(FlatQueryReq req) {
        return R.successByData(flatService.queryPage(req));
    }

    /**
     * 查询公寓列表:无分页
     */
    @Anonymous
    @Operation(summary = "查询公寓列表")
    @GetMapping("/list")
    public R<List<Flat>> list(FlatQueryReq req) {
        return R.successByData(flatService.queryList(req));
    }

    /**
     * 获取公寓详细信息
     */
    @Anonymous
    @Operation(summary = "获取公寓详细信息")
    @GetMapping(value = "/{id}")
    public R<Flat> detail(@PathVariable("id") Long id) {
        return R.successByData(flatService.queryDetail(id));
    }

}
