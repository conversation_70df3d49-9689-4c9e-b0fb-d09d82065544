package com.hean.nucleus.core;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.util.List;

@Data
public class PageView<T> {
    private int pageNum;
    private int pageSize;
    private int size;
    private long total;
    private long startRow;
    private long endRow;
    private int pages;
    private int prePage;
    private int nextPage;
    private boolean isFirstPage;
    private boolean isLastPage;
    private boolean hasPreviousPage;
    private boolean hasNextPage;

    private List<T> list;

    public static <M, T> PageView<T> from(PageInfo<M> info, List<T> list) {
        PageView<T> pageView = new PageView<>();

        pageView.setPageNum(info.getPageNum());
        pageView.setPageSize(info.getPageSize());
        pageView.setSize(info.getSize());
        pageView.setStartRow(info.getStartRow());
        pageView.setEndRow(info.getEndRow());
        pageView.setPages(info.getPages());
        pageView.setPrePage(info.getPrePage());
        pageView.setNextPage(info.getNextPage());
        pageView.setFirstPage(info.isIsFirstPage());
        pageView.setLastPage(info.isIsLastPage());
        pageView.setHasPreviousPage(info.isHasPreviousPage());
        pageView.setHasNextPage(info.isHasNextPage());
        pageView.setTotal(info.getTotal());

        pageView.setList(list);

        return pageView;
    }

    public static <T> PageView<T> empty() {
        PageView<T> pageView = new PageView<>();

        pageView.setPageNum(0);
        pageView.setPageSize(0);
        pageView.setSize(0);
        pageView.setStartRow(0);
        pageView.setEndRow(0);
        pageView.setPages(0);
        pageView.setPrePage(0);
        pageView.setNextPage(0);
        pageView.setFirstPage(false);
        pageView.setLastPage(false);
        pageView.setHasPreviousPage(false);
        pageView.setHasNextPage(false);
        pageView.setTotal(0L);

        pageView.setList(null);

        return pageView;
    }
}
