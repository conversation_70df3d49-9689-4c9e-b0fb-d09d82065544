package com.crosspaste.app

import com.crosspaste.config.ConfigManager
import com.crosspaste.utils.getFileUtils

class DesktopAppControl(private val configManager: ConfigManager) : AppControl {

    private val fileUtils = getFileUtils()

    override fun isFavoriteEnabled(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun isEncryptionEnabled(): <PERSON><PERSON>an {
        return true
    }

    override fun isDeviceConnectionEnabled(deviceCount: Int): <PERSON>olean {
        return true
    }

    override fun isSyncControlEnabled(notify: <PERSON>olean): <PERSON>olean {
        return true
    }

    override suspend fun isSendEnabled(): <PERSON><PERSON><PERSON> {
        return true
    }

    override suspend fun isReceiveEnabled(): <PERSON><PERSON><PERSON> {
        return true
    }

    override suspend fun completeSendOperation() {
        // do nothing
    }

    override suspend fun completeReceiveOperation() {
        // do nothing
    }

    override fun isFileSizeSyncEnabled(size: Long): <PERSON><PERSON>an {
        return !configManager.getCurrentConfig().enabledSyncFileSizeLimit ||
            fileUtils.bytesSize(configManager.getCurrentConfig().maxSyncFileSize) > size
    }
}
