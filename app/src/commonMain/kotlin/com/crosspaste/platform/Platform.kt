package com.crosspaste.platform

import kotlinx.serialization.Serializable

@Serializable
data class Platform(
    val name: String,
    val arch: String,
    val bitMode: Int,
    val version: String,
) {

    companion object {
        const val WINDOWS = "Windows"

        const val MACOS = "Macos"

        const val LINUX = "Linux"

        const val IPHONE = "iPhone"

        const val IPAD = "iPad"

        const val ANDROID = "Android"

        const val UNKNOWN_OS = "Unknown"
    }

    fun isWindows(): <PERSON><PERSON><PERSON> {
        return name == WINDOWS
    }

    fun isMacos(): <PERSON><PERSON><PERSON> {
        return name == MACOS
    }

    fun isLinux(): Boolean {
        return name == LINUX
    }

    fun isDesktop(): Boolean {
        return isWindows() || isMacos() || isLinux()
    }

    fun isIphone(): Boolean {
        return name == IPHONE
    }

    fun isIpad(): Bo<PERSON>an {
        return name == IPAD
    }

    fun isAndroid(): <PERSON><PERSON>an {
        return name == ANDROID
    }

    fun is64bit(): Boolean {
        return bitMode == 64
    }
}
