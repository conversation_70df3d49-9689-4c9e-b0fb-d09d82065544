package com.flat.quartz.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "任务编辑请求")
public class JobEditReq {

    @Schema(description = "任务ID", required = true, example = "1")
    @NotBlank(message = "任务ID不能为空")
    private Long jobId;

    @Schema(description = "任务名称", required = true, example = "数据备份任务")
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 1, max = 64, message = "任务名称不能超过64个字符")
    private String jobName;

    @Schema(description = "任务组名", required = true, example = "系统任务")
    @NotBlank(message = "任务组名不能为空")
    private String jobGroup;

    @Schema(description = "调用目标字符串", required = true, example = "backupTask.execute()")
    @NotBlank(message = "调用目标字符串不能为空")
    @Size(min = 1, max = 500, message = "调用目标字符串长度不能超过500个字符")
    private String invokeTarget;

    @Schema(description = "Cron执行表达式", required = true, example = "0 0 2 * * ?")
    @NotBlank(message = "Cron执行表达式不能为空")
    @Size(min = 1, max = 255, message = "Cron执行表达式不能超过255个字符")
    private String cronExpression;

    @Schema(description = "计划执行错误策略", example = "1")
    private String misfirePolicy;

    @Schema(description = "是否并发执行（0允许 1禁止）", example = "0")
    private String concurrent;

    @Schema(description = "状态（0正常 1暂停）", example = "0")
    private String status;

}
