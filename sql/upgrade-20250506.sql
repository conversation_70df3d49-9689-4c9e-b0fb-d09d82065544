ALTER TABLE `flat`.`sundry_product`
    ADD COLUMN `alarm_threshold` int NOT NULL DEFAULT 0 COMMENT '告警阈值，低于此值时告警' AFTER `total_inventory`;

ALTER TABLE `flat`.`coupon`
    ADD COLUMN `verify_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态[0=待审核;1=撤销;2=业主拒绝;3=业主确认]' AFTER `amount`,
    ADD COLUMN `owner_verify_id` bigint NULL COMMENT '业主审核人ID' AFTER `verify_status`,
    ADD COLUMN `owner_verify_time` datetime NULL DEFAULT NULL COMMENT '业主审核时间' AFTER `owner_verify_id`,
    ADD COLUMN `verify_refuse` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核拒绝原因' AFTER `owner_verify_time`;

ALTER TABLE `flat`.`sundry_inventory_log`
    ADD COLUMN `action_time` datetime NULL COMMENT '出/入库时间' AFTER `remark`;

