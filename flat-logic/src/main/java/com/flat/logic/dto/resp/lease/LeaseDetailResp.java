package com.flat.logic.dto.resp.lease;

import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatPayPlan;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "公寓租赁记录列表响应类")
public class LeaseDetailResp extends FlatLease {

    @Schema(description = "房源")
    private FlatRoom room;

    @Schema(description = "企业名称")
    private String companyName;

    @Schema(description = "用户")
    private CusUser cusUser;

    @Schema(description = "合同")
    private FlatContract contract;

    @Schema(description = "租金支付计划")
    private List<FlatPayPlan> rentPlans;

    @Schema(description = "能耗费支付计划")
    private List<FlatPayPlan> energyPlans;

    @Schema(description = "商铺物业费支付计划")
    private List<FlatPayPlan> shopPropertyPlans;

    @Schema(description = "同住人列表")
    private List<LiveResp> lives;

    @Schema(description = "消费总金额")
    private BigDecimal totalMoney;

    @Schema(description = "已开票金额")
    private BigDecimal invoicedMoney;

    @Schema(description = "用户身份证正面")
    private String cusUserIdCardFrontUrl;

    @Schema(description = "用户身份证反面")
    private String cusUserIdCardReverseUrl;

}
