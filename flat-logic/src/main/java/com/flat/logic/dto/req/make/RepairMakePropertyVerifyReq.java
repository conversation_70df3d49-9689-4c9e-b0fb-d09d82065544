package com.flat.logic.dto.req.make;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.flat.logic.dto.req.VerifyReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class RepairMakePropertyVerifyReq extends VerifyReq {

    @Schema(description = "处理人ID")
    @NotNull(message = "处理人ID不能为空")
    private Long staffId;

    @Schema(description = "维修完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "维修完成时间不能为空")
    private LocalDateTime maintainTime;

    @Schema(description = "维修后图片地址")
    @NotBlank(message = "维修后图片地址不能为空")
    private String maintainUrl;

}
