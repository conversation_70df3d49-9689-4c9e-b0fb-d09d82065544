package com.flat.logic.dto.req.make;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 公共设施时间段查询请求类
 */
@Data
@Schema(description = "公共设施时间段查询请求类")
public class PublicPeriodQueryReq {

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "公共设施id")
    private Long publicId;

    /**
     * 预约日期
     */
    @Schema(description = "预约日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate makeDate;
    
}
