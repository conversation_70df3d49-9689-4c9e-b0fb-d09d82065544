package com.flat.logic.dto.resp.flat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "房间状态响应实体类")
public class RoomStateFloorResp {

    @Schema(description = "栋/幢/楼号")
    private String block;

    @Schema(description = "楼层")
    private Integer floor;

    @Schema(description = "朝南")
    private List<RoomStateItemResp> south;

    @Schema(description = "朝北")
    private List<RoomStateItemResp> north;

}
