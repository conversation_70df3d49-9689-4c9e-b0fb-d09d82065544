package com.flat.logic.dto.req.flat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RoomQueryReq {

    @Schema(description = "企业ID")
    private Long companyId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "用户真实姓名")
    private String cusRealName;

    @Schema(description = "用户手机号")
    private String cusPhoneNumber;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "栋/幢/楼号")
    private String blockNo;

    @Schema(description = "房型")
    private String houseType;

    @Schema(description = "房间状态[0=空闲;1=已出租;2=维修中;3=脏房]")
    private Integer status;

    @Schema(description = "房源类型[0=新上架;1=猜你喜欢;2=旧房源;3=未知]")
    private Integer roomType;

}
