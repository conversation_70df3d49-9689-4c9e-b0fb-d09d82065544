package com.flat.logic.dto.resp.flat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "房间状态响应实体类")
public class RoomStateItemResp {

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "房屋种类[0=住房;1=商铺]")
    private Integer kind;

    @Schema(description = "房间号")
    private String number;

    @Schema(description = "房间状态[0=空闲;1=已出租;2=维修中;3=脏房;4=占用]")
    private Integer status;

    @Schema(description = "租户")
    private String tenant;
    
    @Schema(description = "合同签约企业名称")
    private String companyName;

    //租期剩余天数
    @Schema(description = "租期剩余天数")
    private Integer remainingDays;

    // 租金是否欠费[0=否;1=是]
    @Schema(description = "租金是否欠费[0=否;1=是]")
    private Integer rentArrears;

    // 能耗费是否欠费[0=否;1=是]
    @Schema(description = "能耗费是否欠费[0=否;1=是]")
    private Integer energyArrears;

    // 物业费是否欠费[0=否;1=是]
    @Schema(description = "物业费是否欠费[0=否;1=是]")
    private Integer propertyArrears;

    // 水费是否欠费[0=否;1=是]
    @Schema(description = "水费是否欠费[0=否;1=是]")
    private Integer waterArrears;

    // 电费是否欠费[0=否;1=是]
    @Schema(description = "电费是否欠费[0=否;1=是]")
    private Integer electricArrears;

}
