package com.flat.logic.dto.resp.flat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "房间状态响应实体类")
public class RoomStateItemResp {

    @Schema(description = "房屋种类[0=住房;1=商铺]")
    private Integer kind;

    @Schema(description = "房间号")
    private String number;

    @Schema(description = "房间状态[0=空闲;1=已出租;2=维修中;3=脏房;4=占用]")
    private Integer status;

    @Schema(description = "租户")
    private String tenant;
    
    @Schema(description = "合同签约企业名称")
    private String companyName;

}
