package com.flat.logic.dto.req.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 房间账单发票统计查询请求
 */
@Data
@Schema(description = "房间账单发票统计查询请求")
public class RoomInvoiceStatisticsQueryReq {

    @Schema(description = "房间ID")
    private Long roomId;
    
    @Schema(description = "栋/幢/楼号")
    private String blockNo;
    
    @Schema(description = "楼层")
    private Integer floorNo;
    
    @Schema(description = "房间号")
    private String roomNo;
    
    @Schema(description = "房间名称")
    private String roomName;
    
}
