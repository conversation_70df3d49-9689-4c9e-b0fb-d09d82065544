package com.flat.logic.dto.req.sundry;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 物品库产品进库与出库记录查询请求参数
 */
@Data
@Schema(description = "物品库产品进库与出库记录查询请求参数")
public class SundryInventoryLogQueryReq {

    @Schema(description = "物品库类别ID")
    private Long categoryId;

    @Schema(description = "物品库产品ID")
    private Long productId;

    @Schema(description = "物品库产品名称")
    private String productName;

    @Schema(description = "库存变化类型[1=入库;2=出库]")
    private Integer type;

    @Schema(description = "开始创建时间")
    private String beginCreateTime;

    @Schema(description = "结束创建时间")
    private String endCreateTime;

    @Schema(description = "开始出/入库时间")
    private String beginActionTime;

    @Schema(description = "结束出/入库时间")
    private String endActionTime;

}
