package com.flat.logic.dto.req.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 账单查询请求类，用户申请发票时查询账单列表
 * 查询条件为：
 * 1. 账单状态为已支付
 * 2. 支付时间为上个月及以前
 * 3. 支付目标为水费、电费、租金和能耗费
 * 4. 账单还未开票
 */
@Data
@Schema(description = "账单查询请求类")
public class InvoiceApplyBillQueryReq {

    /**
     * 账单编号
     */
    @Schema(description = "账单编号")
    private String orderNo;

    /**
     * 公寓ID
     */
    @Schema(description = "公寓ID")
    private Long flatId;

    /**
     * 房源ID
     */
    @Schema(description = "房源ID")
    private Long roomId;

    /**
     * 房源名称
     */
    @Schema(description = "房源名称")
    private String roomName;

    @Schema(description = "支付目标[water=水费;electric=电费;plan=付款计划]")
    private String payTarget;

    @Schema(description = "支付计划类型[1=租金计划;2=能耗费计划]，pay_target=plan时有效")
    private Integer payPlanType;

}
