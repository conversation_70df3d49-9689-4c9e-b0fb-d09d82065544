package com.flat.logic.dto.resp.flat;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 房源财务统计响应实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "房源财务统计响应实体类")
public class RoomFinanceResp extends RoomResp {

    @Schema(description = "已支付账单总额")
    private BigDecimal paidBillAmount;

    @Schema(description = "已开票总额")
    private BigDecimal invoicedAmount;

    @Schema(description = "未开票总额")
    private BigDecimal uninvoicedAmount;

}
