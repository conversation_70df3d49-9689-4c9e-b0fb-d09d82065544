package com.flat.logic.dto.req.sundry;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 物品库产品修改请求参数
 */
@Data
@Schema(description = "物品库产品修改请求参数")
public class SundryProductEditReq {

    @Schema(description = "物品库产品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物品库产品ID不能为空")
    private Long id;

    @Schema(description = "物品库类别ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物品库类别ID不能为空")
    private Long categoryId;

    @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "产品编号不能为空")
    private String number;

    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "产品名称不能为空")
    private String name;

    @Schema(description = "产品单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "产品单位不能为空")
    private String unit;

    @Schema(description = "采购单价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "采购单价不能为空")
    private BigDecimal unitPrice;

    @Schema(description = "日消耗量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "日消耗量不能为空")
    @Min(value = 1, message = "日消耗量不能小于1")
    private Integer dayConsumeAmount;

    @Schema(description = "告警阈值，低于此值时告警", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "告警阈值不能为空")
    @Min(value = 1, message = "告警阈值不能小于1")
    private Integer alarmThreshold;

}