package com.flat.logic.dto.resp.sundry;

import com.flat.logic.entity.sundry.SundryProduct;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物品库产品响应类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物品库产品响应类")
public class SundryProductListResp extends SundryProduct {

    @Schema(description = "物品库类别名称")
    private String categoryName;

    @Schema(description = "库存可用天数")
    private Integer availableDays;

    @Schema(description = "是否库存告警[0=正常;1=告警]")
    private Integer alarmStatus;
}
