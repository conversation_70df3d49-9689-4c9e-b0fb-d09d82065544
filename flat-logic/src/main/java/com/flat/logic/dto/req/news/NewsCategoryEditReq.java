package com.flat.logic.dto.req.news;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新闻分类编辑请求
 */
@Data
@Schema(description = "新闻分类编辑请求")
public class NewsCategoryEditReq implements Serializable {

    @Schema(description = "新闻分类ID")
    @NotNull(message = "新闻分类ID不能为空")
    private Long id;

    @Schema(description = "新闻分类名称")
    @NotBlank(message = "新闻分类名称不能为空")
    private String name;

} 