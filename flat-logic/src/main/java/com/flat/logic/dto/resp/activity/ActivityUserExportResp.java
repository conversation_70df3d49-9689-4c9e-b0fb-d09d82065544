package com.flat.logic.dto.resp.activity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 活动报名人员导出响应对象
 */
@Data
@Schema(description = "活动报名人员导出响应对象")
public class ActivityUserExportResp {

    @ExcelIgnore
    @Schema(description = "主键ID")
    private Long id;
    
    @ExcelProperty(index = 0, value = "活动名称")
    @Schema(description = "活动名称")
    private String activityName;
    
    @ExcelProperty(index = 1, value = "活动开始时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activityBeginTime;
    
    @ExcelProperty(index = 2, value = "活动结束时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activityEndTime;
    
    @ExcelProperty(index = 3, value = "活动状态")
    @Schema(description = "活动状态")
    private String processStatusName;
    
    @ExcelIgnore
    @Schema(description = "过程状态[0=未开始;1=进行中;2=已结束]")
    private Integer processStatus;
    
    @ExcelProperty(index = 4, value = "用户姓名")
    @Schema(description = "用户姓名")
    private String realName;
    
    @ExcelProperty(index = 5, value = "用户手机号")
    @Schema(description = "用户手机号")
    private String phoneNumber;
    
    @ExcelProperty(index = 6, value = "报名费类型")
    @Schema(description = "报名费类型")
    private String entryFeeFlagName;
    
    @ExcelIgnore
    @Schema(description = "是否需要缴报名费[0=免费;1=需要报名费]")
    private Integer entryFeeFlag;
    
    @ExcelProperty(index = 7, value = "报名费(元)")
    @Schema(description = "报名费")
    private BigDecimal entryFee;
    
    @ExcelProperty(index = 8, value = "支付状态")
    @Schema(description = "支付状态")
    private String payStatusName;
    
    @ExcelIgnore
    @Schema(description = "是否已付费[0=未付费;1=已付费]")
    private Integer payStatus;
    
    @ExcelProperty(index = 9, value = "支付方式")
    @Schema(description = "支付方式")
    private String payModeName;
    
    @ExcelIgnore
    @Schema(description = "收付款方式[0=线上支付;1=线下支付]")
    private Integer payMode;
    
    @ExcelProperty(index = 10, value = "支付时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;
    
    @ExcelProperty(index = 11, value = "报名时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
} 