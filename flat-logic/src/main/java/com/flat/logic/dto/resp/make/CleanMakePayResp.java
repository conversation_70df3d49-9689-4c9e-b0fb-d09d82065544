package com.flat.logic.dto.resp.make;

import java.io.Serializable;

import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 保洁预约支付响应
 */
@Data
@Schema(description = "保洁预约支付响应")
public class CleanMakePayResp implements Serializable {

    @Schema(description = "是否需要支付，true表示需要调用微信支付，false表示已完成支付（如使用优惠券全额抵扣）")
    private Boolean needPay;

    @Schema(description = "微信支付参数，当needPay=true时有效")
    private WxPayMpOrderResult wxPayResult;
    
    /**
     * 创建一个需要支付的响应
     * @param wxPayResult 微信支付参数
     * @return 支付响应
     */
    public static CleanMakePayResp needPay(WxPayMpOrderResult wxPayResult) {
        CleanMakePayResp resp = new CleanMakePayResp();
        resp.setNeedPay(true);
        resp.setWxPayResult(wxPayResult);
        return resp;
    }
    
    /**
     * 创建一个不需要支付的响应（如使用优惠券全额抵扣）
     * @return 支付响应
     */
    public static CleanMakePayResp noPay() {
        CleanMakePayResp resp = new CleanMakePayResp();
        resp.setNeedPay(false);
        return resp;
    }
} 