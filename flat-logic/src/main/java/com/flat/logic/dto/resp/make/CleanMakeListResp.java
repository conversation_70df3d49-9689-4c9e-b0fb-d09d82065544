package com.flat.logic.dto.resp.make;

import com.flat.logic.entity.make.FlatCleanMake;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "公寓保洁列表类")
public class CleanMakeListResp extends FlatCleanMake {

    @Schema(description = "公寓名称")
    private String flatName;

    @Schema(description = "房源名称")
    private String roomName;

    @Schema(description = "保洁类型名称")
    private String cleanTypeName;

    @Schema(description = "用户真实姓名")
    private String cusRealName;

    @Schema(description = "用户手机号")
    private String cusPhoneNumber;

    @Schema(description = "管家名称")
    private String butlerUsername;

    @Schema(description = "物业名称")
    private String propertyUsername;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "处理人名称")
    private String staffName;

}
