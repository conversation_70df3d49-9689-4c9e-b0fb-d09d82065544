package com.flat.logic.dto.resp.activity;

import com.flat.logic.entity.activity.Activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 活动响应对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "活动响应对象")
public class ActivityResp extends Activity {
    
    /**
     * 已报名人数
     */
    @Schema(description = "已报名人数")
    private Integer joinCount;
    
    /**
     * 已支付人数
     */
    @Schema(description = "已支付人数")
    private Integer paidCount;
    
    /**
     * 过程状态[0=未开始;1=进行中;2=已结束]
     */
    @Schema(description = "过程状态[0=未开始;1=进行中;2=已结束]")
    private Integer processStatus;

    @Schema(description = "小程序码图片url")
    private String qrCodeUrl;

}
