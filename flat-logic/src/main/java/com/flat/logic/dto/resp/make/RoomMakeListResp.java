package com.flat.logic.dto.resp.make;

import com.flat.logic.entity.make.FlatRoomMake;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "预约看房响应实体类")
public class RoomMakeListResp extends FlatRoomMake {

    @Schema(description = "公寓名称")
    private String flatName;

    @Schema(description = "房源名称")
    private String roomName;

    @Schema(description = "用户头像")
    private String cusAvatar;

    @Schema(description = "管家名称")
    private String butlerUsername;

}
