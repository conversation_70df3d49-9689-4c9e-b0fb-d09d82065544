package com.flat.logic.dto.req.activity;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 活动支付请求对象
 */
@Data
@Schema(description = "活动支付请求对象")
public class ActivityPayReq implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 活动报名记录ID
     */
    @NotNull(message = "活动报名记录ID不能为空")
    @Schema(description = "活动报名记录ID")
    private Long activityUserId;

}
