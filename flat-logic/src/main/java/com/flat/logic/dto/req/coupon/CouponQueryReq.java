package com.flat.logic.dto.req.coupon;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 优惠券查询请求
 */
@Data
@Schema(description = "优惠券查询请求")
public class CouponQueryReq implements Serializable {

    @Schema(description = "优惠券领取人")
    private Long cusId;

    @Schema(description = "用户手机号")
    private String phoneNumber;

    @Schema(description = "优惠券类型[1=保洁预约;2=自定义费用]")
    private Integer target;

    @Schema(description = "自定义费用类型ID")
    private Long costTypeId;

    @Schema(description = "优惠券名称")
    private String name;

    @Schema(description = "有效期开始时间-起始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginValidBeginTime;

    @Schema(description = "有效期开始时间-截止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endValidBeginTime;

    @Schema(description = "有效期结束时间-起始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginValidEndTime;

    @Schema(description = "有效期结束时间-截止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endValidEndTime;

    @Schema(description = "最小额度")
    private Integer minAmount;

    @Schema(description = "最大额度")
    private Integer maxAmount;

    @Schema(description = "使用状态[0=未使用;1=已使用]")
    private Integer useStatus;

    @Schema(description = "使用时间-起始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginUseTime;

    @Schema(description = "使用时间-截止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endUseTime;

    @Schema(description = "使用优惠券的账单ID")
    private Long useBillId;

    @Schema(description = "审核状态[0=待审核;1=撤销;2=业主拒绝;3=业主确认]")
    private Integer verifyStatus;

    @Schema(description = "业主审核人ID")
    private Long ownerVerifyId;

    @Schema(description = "开始创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginCreateTime;

    @Schema(description = "结束创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endCreateTime;
}