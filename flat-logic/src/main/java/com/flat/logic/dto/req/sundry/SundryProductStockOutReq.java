package com.flat.logic.dto.req.sundry;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 物品库产品出库请求参数
 */
@Data
@Schema(description = "物品库产品出库请求参数")
public class SundryProductStockOutReq {

    @Schema(description = "物品库产品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物品库产品ID不能为空")
    private Long productId;

    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库数量不能为空")
    private Integer amount;

    @Schema(description = "出库备注")
    private String remark;

    @Schema(description = "出库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime actionTime;
}
