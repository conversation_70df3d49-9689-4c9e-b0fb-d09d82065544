package com.flat.logic.dto.req.coupon;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 优惠券审核请求
 */
@Data
@Schema(description = "优惠券审核请求")
public class CouponVerifyReq implements Serializable {

    @Schema(description = "优惠券ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优惠券ID不能为空")
    private Long id;

    @Schema(description = "审核拒绝原因，拒绝时必填")
    private String refuseReason;
    
}
