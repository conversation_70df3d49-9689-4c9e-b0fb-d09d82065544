package com.flat.logic.dto.resp.lease;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 租赁记录导出响应类
 */
@Data
@Schema(description = "租赁记录导出响应类")
public class LeaseExportResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(index = 0, value = "合同号")
    @Schema(description = "合同号")
    private String contractNumber;

    @ExcelProperty(index = 1, value = "签约主体")
    @Schema(description = "签约主体")
    private String companyName;

    @ExcelProperty(index = 2, value = "房间类型")
    @Schema(description = "房间类型")
    private String roomKindName;

    @ExcelProperty(index = 3, value = "用户")
    @Schema(description = "用户")
    private String cusRealName;

    @ExcelProperty(index = 4, value = "身份证")
    @Schema(description = "身份证")
    private String cusIdCard;

    @ExcelProperty(index = 5, value = "电话")
    @Schema(description = "电话")
    private String cusPhoneNumber;

    @ExcelProperty(index = 6, value = "房间")
    @Schema(description = "房间")
    private String roomName;

    @ExcelProperty(index = 7, value = "合同")
    @Schema(description = "合同")
    private String contractStatusName;

    @ExcelProperty(index = 8, value = "签约方式")
    @Schema(description = "签约方式")
    private String contractSignModeName;

    @ExcelProperty(index = 9, value = "付租周期")
    @Schema(description = "付租周期")
    private String payPeriodName;

    @ExcelProperty(index = 10, value = "月租金")
    @Schema(description = "月租金")
    private BigDecimal contractMonthMoney;

    @ExcelProperty(index = 11, value = "押金")
    @Schema(description = "押金")
    private BigDecimal contractDepositMoney;

    @ExcelProperty(index = 12, value = "合同开始时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractBeginTime;

    @ExcelProperty(index = 13, value = "合同结束时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractEndTime;

    @ExcelProperty(index = 14, value = "租赁状态")
    @Schema(description = "租赁状态")
    private String statusName;

}
