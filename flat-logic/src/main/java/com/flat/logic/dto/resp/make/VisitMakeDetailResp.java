package com.flat.logic.dto.resp.make;

import com.flat.logic.entity.trash.FlatMakeLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "公寓访客响应类")
public class VisitMakeDetailResp extends VisitMakeListResp {

    @Schema(description = "预约流程集合")
    private List<FlatMakeLog> logs;
    
}
