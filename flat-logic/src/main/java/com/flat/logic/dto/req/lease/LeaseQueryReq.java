package com.flat.logic.dto.req.lease;

import org.hibernate.validator.constraints.Range;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 租赁记录查询请求类
 */
@Data
@Schema(description = "租赁记录查询请求类")
public class LeaseQueryReq {

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "合同编号")
    private String contractNumber;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "房源类型[0=公寓;1=商铺]")
    @Range(min = 0, max = 1, message = "房源类型取值范围[0-1]")
    private Integer roomKind;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "用户姓名")
    private String cusRealName;

    @Schema(description = "用户手机号")
    private String cusPhoneNumber;

    @Schema(description = "用户身份证号")
    private String cusIdCard;

    @Schema(description = "企业ID")
    private Long companyId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "租赁状态[0=合同待签署;1=已取消(合同未签署);2=履约中(合同已签署);3=续租已申请;4=已续租;5=转租已申请;6=已转租;7=退租已申请;8=已退租]")
    private Integer status;

    @Schema(description = "最近几天到期")
    private Integer dueDay;

    @Schema(description = "是否作废[0=未作废;1=已作废]")
    private Integer invalidFlag;

}
