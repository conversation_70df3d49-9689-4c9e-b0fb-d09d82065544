package com.flat.logic.dto.req.make;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CleanEvaluateQueryReq {

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "预约保洁ID")
    private Long cleanMakeId;

    @Schema(description = "房屋租赁记录ID")
    private Long leaseId;
    
}
