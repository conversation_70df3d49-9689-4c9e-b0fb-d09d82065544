package com.flat.logic.dto.req.make;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 公共设施预约请求类
 */
@Data
@Schema(description = "公共设施预约请求类")
public class PublicMakeAddReq {

    @Schema(description = "预约者名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "预约者名称不能为空")
    private String makeUserName;

    @Schema(description = "预约者联系方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "预约者联系方式不能为空")
    private String makeUserPhoneNumber;

    @Schema(description = "房源ID")
    @NotNull(message = "房源ID不能为空")
    private Long roomId;

    /**
     * 公共设施ID
     */
    @NotNull(message = "预约公共设施不能为空")
    @Schema(description = "公共设施ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long publicId;

    /**
     * 预约日期
     */
    @NotNull(message = "预约日期不能为空")
    @Schema(description = "预约日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate makeDate;

    /**
     * 预约时间段
     */
    @NotNull(message = "预约时间段不能为空")
    @Schema(description = "预约时间段", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long publicPeriodId;

    /**
     * 预约人数
     */
    @NotNull(message = "预约人数不能为空")
    @Schema(description = "预约人数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer peopleCount;

    /**
     * 预约说明
     */
    @NotBlank(message = "预约说明不能为空")
    @Schema(description = "预约说明")
    private String description;

}
