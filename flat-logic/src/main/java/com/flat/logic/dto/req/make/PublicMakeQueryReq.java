package com.flat.logic.dto.req.make;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PublicMakeQueryReq {

    @Schema(description = "关键词")
    private String keyword;

    @Schema(description = "公寓ID")
    private Long flatId;

    /**
     * 房源ID
     */
    @Schema(description = "房源ID")
    private Long roomId;

    @Schema(description = "房源名称")
    private String roomName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "用户真实姓名")
    private String cusRealName;

    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    private String cusPhoneNumber;

    @Schema(description = "公用设施ID")
    private Long publicId;

    @Schema(description = "公用设施时间段ID")
    private Long publicPeriodId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "审核状态[0=待审核;1=撤销;2=管家拒绝;3=管家确认;4=物业拒绝;5=物业确认]")
    private Integer status;

}
