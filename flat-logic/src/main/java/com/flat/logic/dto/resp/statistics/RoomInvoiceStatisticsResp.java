package com.flat.logic.dto.resp.statistics;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "房间发票统计响应")
public class RoomInvoiceStatisticsResp {

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "房间号")
    private String roomNo;

    @Schema(description = "栏/幢/楼号")
    private String blockNo;

    @Schema(description = "楼层")
    private Integer floorNo;

    @Schema(description = "总金额")
    private BigDecimal totalMoney;

    @Schema(description = "已开票金额")
    private BigDecimal invoicedMoney;

    @Schema(description = "未开票金额")
    private BigDecimal uninvoicedMoney;

}
