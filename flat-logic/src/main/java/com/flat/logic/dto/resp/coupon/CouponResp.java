package com.flat.logic.dto.resp.coupon;

import com.flat.logic.entity.coupon.Coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 优惠券响应对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "优惠券响应对象")
public class CouponResp extends Coupon {

    @Schema(description = "过期状态[0=未生效;1=正常;2=已过期]")
    private Integer expireStatus;

    @Schema(description = "领取用户姓名")
    private String cusRealName;

    @Schema(description = "领取用户手机号")
    private String cusPhoneNumber;

    @Schema(description = "业主审核人姓名")
    private String ownerVerifyName;

}
