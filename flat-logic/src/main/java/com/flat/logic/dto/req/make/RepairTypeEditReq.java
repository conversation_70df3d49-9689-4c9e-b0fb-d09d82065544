package com.flat.logic.dto.req.make;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报修类型修改请求参数")
public class RepairTypeEditReq extends RepairTypeAddReq {

    @Schema(description = "主键ID")
    @NotNull(message = "主键ID不能为空")
    private Long id;
}
