package com.flat.logic.dto.resp.coupon;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 优惠券导出响应对象
 */
@Data
@Schema(description = "优惠券导出响应对象")
public class CouponExportResp {

    @ExcelIgnore
    @Schema(description = "主键ID")
    private Long id;

    @ExcelProperty(index = 0, value = "优惠券名称")
    @Schema(description = "优惠券名称")
    private String name;

    @ExcelProperty(index = 1, value = "优惠券类型")
    @Schema(description = "优惠券类型")
    private String targetName;

    @ExcelProperty(index = 2, value = "自定义费用类型")
    @Schema(description = "自定义费用类型")
    private String costTypeName;

    @ExcelProperty(index = 3, value = "金额(元)")
    @Schema(description = "金额")
    private Integer amount;

    @ExcelProperty(index = 4, value = "有效期开始时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "有效期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validBeginTime;

    @ExcelProperty(index = 5, value = "有效期结束时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "有效期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validEndTime;

    @ExcelProperty(index = 6, value = "状态")
    @Schema(description = "状态")
    private String expireStatusName;

    @ExcelIgnore
    @Schema(description = "过期状态[0=未生效;1=正常;2=已过期]")
    private Integer expireStatus;

    @ExcelProperty(index = 7, value = "使用状态")
    @Schema(description = "使用状态")
    private String useStatusName;

    @ExcelIgnore
    @Schema(description = "使用状态[0=未使用;1=已使用]")
    private Integer useStatus;

    @ExcelProperty(index = 8, value = "审核状态")
    @Schema(description = "审核状态")
    private String verifyStatusName;

    @ExcelIgnore
    @Schema(description = "审核状态[0=待审核;1=撤销;2=业主拒绝;3=业主确认]")
    private Integer verifyStatus;

    @ExcelProperty(index = 9, value = "业主审核人")
    @Schema(description = "业主审核人")
    private String ownerVerifyName;

    @ExcelProperty(index = 10, value = "业主审核时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "业主审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ownerVerifyTime;

    @ExcelProperty(index = 11, value = "审核拒绝原因")
    @Schema(description = "审核拒绝原因")
    private String verifyRefuse;

    @ExcelProperty(index = 12, value = "使用时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime useTime;

    @ExcelProperty(index = 13, value = "账单ID")
    @Schema(description = "账单ID")
    private Long useBillId;

    @ExcelProperty(index = 14, value = "用户姓名")
    @Schema(description = "用户姓名")
    private String cusRealName;

    @ExcelProperty(index = 15, value = "用户手机号")
    @Schema(description = "用户手机号")
    private String cusPhoneNumber;

    @ExcelProperty(index = 16, value = "创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ExcelProperty(index = 17, value = "创建人")
    @Schema(description = "创建人")
    private String createBy;
}