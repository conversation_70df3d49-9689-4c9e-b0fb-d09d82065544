package com.flat.logic.dto.resp.flat;

import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.trash.FlatRoomDoor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门锁响应类")
public class FlatRoomDoorResp extends FlatRoomDoor {
    @Schema(description = "房间信息")
    private FlatRoom room;
}
