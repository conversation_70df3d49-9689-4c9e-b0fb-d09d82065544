package com.flat.logic.dto.resp.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 今日数据统计响应
 */
@Data
@Schema(description = "今日数据统计响应")
public class TodayStatisticsResp {
    
    /**
     * 今日报修预约数量
     */
    @Schema(description = "今日报修预约数量")
    private Long repairCount;

    /**
     * 今日保洁预约数量
     */
    @Schema(description = "今日保洁预约数量")
    private Long cleanCount;

    /**
     * 今日公共设施预约数量
     */
    @Schema(description = "今日公共设施预约数量")
    private Long publicCount;

    /**
     * 今日看房预约数量
     */
    @Schema(description = "今日看房预约数量")
    private Long roomCount;

    /**
     * 今日访客预约数量
     */
    @Schema(description = "今日访客预约数量")
    private Long visitCount;

    /**
     * 今日租赁数量
     */
    @Schema(description = "今日租赁数量")
    private Long leaseCount;
}
