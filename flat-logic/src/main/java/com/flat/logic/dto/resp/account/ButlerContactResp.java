package com.flat.logic.dto.resp.account;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "管家联系响应类")
public class ButlerContactResp {

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "管家名称")
    private String username;

    @Schema(description = "管家地址")
    private String address;

    @Schema(description = "联系电话")
    private String phoneNumber;

}
