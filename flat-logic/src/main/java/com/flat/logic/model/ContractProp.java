package com.flat.logic.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "合同属性类")
@Data
public class ContractProp {

    // 参数分类
    @Schema(description = "参数分类", example = "czf")
    @NotBlank(message = "参数分类不能为空")
    private String category;

    // 参数分类名称
    @Schema(description = "参数分类名称", example = "承租方信息")
    @NotBlank(message = "参数分类名称不能为空")
    private String categoryName;

    // 参数键
    @Schema(description = "参数键", example = "czf_phone")
    @NotBlank(message = "参数键不能为空")
    private String key;

    // 参数名称
    @Schema(description = "参数名称", example = "承租方电话")
    @NotBlank(message = "参数名称不能为空")
    private String name;

    // 参数值类型
    @Schema(description = "参数值类型（<br />" +
            "text=文本，<br />" +
            "number=数字，<br />" +
            "check=选择框，<br />" +
            "cusTrueName=承租方用户姓名，<br />" +
            "cusIdCard=承租方用户身份证号，<br />" +
            "cusPhone=承租方用户电话，<br />" +
            "contactName=紧急联系人姓名，<br />" +
            "contactPhone=紧急联系人电话，<br />" +
            "deliveryAddress=送达地址，<br />" +
            "cusIdCardFront=承租方用户身份证正面，<br />" +
            "cusIdCardReverse=承租方用户身份证反面，<br />" +
            "compName=出租方企业名称，<br />" +
            "signYear=签署年，<br />" +
            "signMonth=签署月，<br />" +
            "signDay=签署日，<br />" +
            "beginYear=合同开始年，<br />" +
            "beginMonth=合同开始月，<br />" +
            "beginDay=合同开始日，<br />" +
            "endYear=合同结束年，<br />" +
            "endMonth=合同结束月，<br />" +
            "endDay=合同结束日，<br />" +
            "deliveryYear=交付年，<br />" +
            "deliveryMonth=交付月，<br />" +
            "deliveryDay=交付日，<br />" +
            "price=租金，<br />" +
            "bigPrice=租金（大写），<br />" +
            "deposit=押金，<br />" +
            "bigDeposit=押金（大写），<br />" +
            "propertyFee=物业费，<br />" +
            "bigPropertyFee=物业费（大写），<br />" +
            "yearPropertyFee=年物业费，<br />" +
            "bigYearPropertyFee=年物业费（大写），<br />" +
            "room=房间，<br />" +
            "liveNumber=居住人数，<br />" +
            "payPeriod=付租周期，<br />" +
            "firstPeriodBeginYear=第一个付款周期开始年，<br />" +
            "firstPeriodBeginMonth=第一个付款周期开始月，<br />" +
            "firstPeriodBeginDay=第一个付款周期开始日，<br />" +
            "firstPeriodEndYear=第一个付款周期结束年，<br />" +
            "firstPeriodEndMonth=第一个付款周期结束月，<br />" +
            "firstPeriodEndDay=第一个付款周期结束日，<br />" +
            "firstPeriodMoney=第一个付款周期费用，<br />" +
            "firstPeriodBigMoney=第一个付款周期费用（大写）<br />" +
            "）", example = "number")
    @NotBlank(message = "参数值类型不能为空")
    private String type;

    // 参数值
    @Schema(description = "参数值，当参数值类型为text、number和check时，此字段必填，check传0/1", example = "3")
    private String value;

}
