package com.flat.logic.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RefundMoney {

    @Schema(description = "租金费用")
    private BigDecimal rentMoney;

    @Schema(description = "能耗费费用")
    private BigDecimal energyMoney;

    @Schema(description = "水费")
    private BigDecimal waterMoney;

    @Schema(description = "电费")
    private BigDecimal electricMoney;

    @Schema(description = "物业费")
    private BigDecimal propertyMoney;

}
