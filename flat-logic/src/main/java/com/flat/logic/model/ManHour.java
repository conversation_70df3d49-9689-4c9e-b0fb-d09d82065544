package com.flat.logic.model;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "工时统计请求类")
public class ManHour {

    @Schema(description = "序号")
    private Long id;

    @Schema(description = "员工姓名")
    private String name;

    @Schema(description = "员工类型")
    private String type;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "保洁次数")
    private Integer cleanNum;

    @Schema(description = "维修次数")
    private Integer repairsNum;
}
