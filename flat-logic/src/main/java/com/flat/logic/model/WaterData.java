package com.flat.logic.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "水司下全部数据")
public class WaterData {
    @Schema(description = "集中器号")
    private String centerId;

    @Schema(description = "采集器号")
    private String collectionId;

    @Schema(description = "设备地址")
    private String iaddr;

    @Schema(description = "采集时间（时间戳）")
    private Long readTime;

    @Schema(description = "累计流量(行度)")
    private double showValue;

    @Schema(description = "阀门状态（0=无阀，1=开，2=关）")
    private int strobeStatue;

    @Schema(description = "设备类型（0-1=水表，4-5=气表，2-3=电表，7-8=热表）")
    private int showType;
}
