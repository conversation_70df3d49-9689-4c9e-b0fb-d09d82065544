package com.flat.logic.model;

//import com.flat.logic.dto.resp.DocTemplatesResp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "合同模板信息")
public class FlowTemplateBasicInfos {

    @Schema(description = "合同模板ID")
    private String flowTemplateName;

    @Schema(description = "合同模板名称")
    private String flowTemplateId;

//    @Schema(description = "合同集合")
//    private List<DocTemplatesResp> docTemplateResps;
}
