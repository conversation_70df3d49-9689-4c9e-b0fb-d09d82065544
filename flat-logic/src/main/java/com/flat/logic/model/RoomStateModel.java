package com.flat.logic.model;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class RoomStateModel {

    private Integer roomKind;

    private Long roomId;

    private String roomName;

    private String blockNo;

    private Integer floorNo;

    private String roomNo;

    private Integer orientation;

    private Integer roomStatus;

    private Long leaseId;

    private Integer leaseStatus;

    private Long cusId;

    private String cusRealName;

    private Long companyId;

    private String companyName;

    private Integer rentArrears;

    private Integer energyArrears;

    private Integer propertyArrears;

    private Integer waterArrears;

    private Integer electricArrears;

    private LocalDateTime contractEndTime;

}
