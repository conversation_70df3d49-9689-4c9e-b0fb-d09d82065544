package com.flat.logic.model.meter;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ElectricMeterCheck {

    @ExcelProperty(index = 0, value = "房源序号")
    private Long roomId;

    @ExcelProperty(index = 1, value = "房源名称")
    private String roomName;

    @ExcelProperty(index = 2, value = "电费户号")
    private String electricAccountNo;

    @ExcelProperty(index = 3, value = "上次抄表数")
    private BigDecimal previousValue;

    @ExcelProperty(index = 4, value = "上次抄表时间")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate previousReadDate;

    @ExcelProperty(index = 5, value = "本次抄表数")
    private BigDecimal currentValue;

    @ExcelProperty(index = 6, value = "本次抄表时间")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate readDate;

}
