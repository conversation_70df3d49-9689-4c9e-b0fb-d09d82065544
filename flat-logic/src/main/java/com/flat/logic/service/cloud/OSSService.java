package com.flat.logic.service.cloud;

import cn.hutool.core.util.URLUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.ObjectMetadata;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.file.FileUploadUtils;
import com.flat.common.utils.file.FileUtils;
import com.flat.logic.dto.resp.FileUploadResp;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

@Service
public class OSSService {

    @Value("${aliyun.oss.folder}")
    private String aliyunOssFolder;

    @Value("${aliyun.oss.bucket}")
    private String aliyunOssBucket;

    @Value("${aliyun.oss.endpoint}")
    private String aliyunOssEndpoint;

    @Value("${aliyun.oss.fileUrl}")
    private String aliyunOssFileUrl;

    @Value("${aliyun.oss.accessKeyId}")
    private String aliyunOssAccessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String aliyunOssAccessKeySecret;

    private OSS ossClient;

    private void initOSSClient() {
        synchronized (OSSService.class) {
            if(ossClient == null) {
                synchronized (OSSService.class) {
                    ossClient = OSSClientBuilder.create()
                            .endpoint(aliyunOssEndpoint)
                            .credentialsProvider(new DefaultCredentialProvider(aliyunOssAccessKeyId, aliyunOssAccessKeySecret))
                            .build();
                }
            }
        }
    }

    public FileUploadResp uploadFile(MultipartFile file) {
        initOSSClient();

        String key = FileUploadUtils.generateFilename(aliyunOssFolder, file);
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(file.getContentType());
        metadata.setContentLength(file.getSize());

        try {
            ossClient.putObject(aliyunOssBucket, key, file.getInputStream(), metadata);
        } catch (Exception e) {
            throw new ServiceException("文件上传失败", e);
        }

        FileUploadResp resp = new FileUploadResp();
        resp.setOriginalFilename(file.getOriginalFilename());
        resp.setNewFileName(FileUtils.getName(key));
        resp.setFileName(key);
        resp.setUrl(aliyunOssFileUrl + "/" + key);
        return resp;
    }

    public byte[] downloadFile(String path) {
        initOSSClient();

        try {
            return ossClient.getObject(aliyunOssBucket, path).getObjectContent().readAllBytes();
        } catch (Exception e) {
            throw new ServiceException("文件下载失败", e);
        }
    }

    public String joinUrl(String path) {
        if (path == null) {
            return null;
        }
        return URLUtil.completeUrl(aliyunOssFileUrl, path);
    }

    public FileUploadResp uploadFile(byte[] bytes, String contentType, String extension) {
        initOSSClient();

        String key = FileUploadUtils.generateFilename(aliyunOssFolder, extension);
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(contentType);
        metadata.setContentLength(bytes.length);

        try(InputStream inputStream = new ByteArrayInputStream(bytes)) {
            ossClient.putObject(aliyunOssBucket, key, inputStream, metadata);
        } catch (Exception e) {
            throw new ServiceException("文件上传失败", e);
        }

        FileUploadResp resp = new FileUploadResp();
        resp.setOriginalFilename(FileUtils.getName(key));
        resp.setNewFileName(FileUtils.getName(key));
        resp.setFileName(key);
        resp.setUrl(joinUrl(key));
        return resp;
    }
}
