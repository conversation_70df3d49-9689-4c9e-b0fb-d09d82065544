package com.flat.logic.service.coupon;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.coupon.CouponTemplateAddReq;
import com.flat.logic.dto.req.coupon.CouponTemplateEditReq;
import com.flat.logic.dto.req.coupon.CouponTemplateQueryReq;
import com.flat.logic.dto.resp.coupon.CouponTemplateResp;
import com.flat.logic.entity.coupon.CouponTemplate;
import com.flat.logic.mapper.coupon.CouponTemplateMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 优惠券模板服务
 */
@Service
public class CouponTemplateService {

    @Resource
    private CouponTemplateMapper couponTemplateMapper;

    /**
     * 添加优惠券模板
     */
    public void add(CouponTemplateAddReq req, SysUser sysUser) {
        // 检查同名模板是否存在
        if (couponTemplateMapper.selectCount(q -> q
                .eq(CouponTemplate::getName, req.getName())
                .eq(CouponTemplate::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("优惠券模板名称已存在");
        }

        // 检查有效期
        if (req.getValidBeginTime().isAfter(req.getValidEndTime())) {
            throw new ServiceException("有效期开始时间不能晚于结束时间");
        }
        
        // 检查自定义费用类型
        if (req.getTarget() == 2 && req.getCostTypeId() == null) {
            throw new ServiceException("自定义费用类型不能为空");
        }

        CouponTemplate couponTemplate = new CouponTemplate();
        BeanUtils.copyProperties(req, couponTemplate);
        couponTemplate.setCreateBy(sysUser.getUsername());
        couponTemplate.setCreateTime(LocalDateTime.now());
        couponTemplate.setDelStatus(0);
        couponTemplateMapper.insert(couponTemplate);
    }

    /**
     * 修改优惠券模板
     */
    public void edit(CouponTemplateEditReq req, SysUser sysUser) {
        // 检查同名模板是否存在（排除自身）
        if (couponTemplateMapper.selectCount(q -> q
                .eq(CouponTemplate::getName, req.getName())
                .ne(CouponTemplate::getId, req.getId())
                .eq(CouponTemplate::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("优惠券模板名称已存在");
        }

        CouponTemplate couponTemplate = couponTemplateMapper.selectById(req.getId());
        if (couponTemplate == null || couponTemplate.getDelStatus() == 1) {
            throw new ServiceException("优惠券模板不存在");
        }

        // 检查有效期
        if (req.getValidBeginTime().isAfter(req.getValidEndTime())) {
            throw new ServiceException("有效期开始时间不能晚于结束时间");
        }
        
        // 检查自定义费用类型
        if (req.getTarget() == 2 && req.getCostTypeId() == null) {
            throw new ServiceException("自定义费用类型不能为空");
        }

        BeanUtils.copyProperties(req, couponTemplate);
        couponTemplate.setUpdateBy(sysUser.getUsername());
        couponTemplate.setUpdateTime(LocalDateTime.now());
        couponTemplateMapper.updateById(couponTemplate);
    }

    /**
     * 删除优惠券模板
     */
    public void remove(Long id, SysUser sysUser) {
        couponTemplateMapper.update(q -> q
                .set(CouponTemplate::getDelStatus, 1)
                .set(CouponTemplate::getUpdateTime, LocalDateTime.now())
                .set(CouponTemplate::getUpdateBy, sysUser.getUsername())
                .eq(CouponTemplate::getId, id)
                .eq(CouponTemplate::getDelStatus, 0)
        );
    }

    /**
     * 查询优惠券模板详情
     */
    public CouponTemplateResp queryDetail(Long id) {
        CouponTemplate couponTemplate = couponTemplateMapper.selectById(id);
        if (couponTemplate == null || couponTemplate.getDelStatus() == 1) {
            throw new ServiceException("优惠券模板不存在");
        }
        
        // 转换为响应对象
        CouponTemplateResp resp = new CouponTemplateResp();
        BeanUtils.copyProperties(couponTemplate, resp);
        
        // 计算过期状态
        resp.calculateExpireStatus();
        
        return resp;
    }

    /**
     * 查询优惠券模板列表
     */
    public List<CouponTemplateResp> queryList(CouponTemplateQueryReq req) {
        // 使用自定义SQL查询
        List<CouponTemplateResp> list = couponTemplateMapper.selectTemplateRespList(req);
        
        // 计算过期状态
        for (CouponTemplateResp item : list) {
            item.calculateExpireStatus();
        }
        
        return list;
    }

    /**
     * 分页查询优惠券模板
     */
    public TablePage<CouponTemplateResp> queryPage(CouponTemplateQueryReq req) {
        return PageUtils.paginate(() -> queryList(req));
    }
}
