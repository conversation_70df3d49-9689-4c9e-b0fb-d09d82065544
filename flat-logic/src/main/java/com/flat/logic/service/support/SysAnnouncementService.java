package com.flat.logic.service.support;

import com.flat.common.core.page.TablePage;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.support.AnnouncementQueryReq;
import com.flat.logic.entity.support.SysAnnouncement;
import com.flat.logic.mapper.support.SysAnnouncementMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 公告管理Service业务层处理
 */
@Service
public class SysAnnouncementService {

    @Resource
    private SysAnnouncementMapper sysAnnouncementMapper;

    /**
     * 查询公告管理
     * 
     * @param id 公告管理主键
     * @return 公告管理
     */
    public SysAnnouncement queryDetail(Long id) {
        return sysAnnouncementMapper.selectById(id);
    }



//    /**
//     * 查询公告管理列表
//     *
//     * @param sysAnnouncement 公告管理
//     * @return 公告管理
//     */
//    public List<SysAnnouncement> selectSysAnnouncementList(SysAnnouncement sysAnnouncement) {
//        return sysAnnouncementMapper.selectSysAnnouncementList(sysAnnouncement);
//    }
//
    public TablePage<SysAnnouncement> queryPage(AnnouncementQueryReq req) {
        return PageUtils.paginate(() -> sysAnnouncementMapper.selectBaseList(req));
    }
//
//    /**
//     * 新增公告管理
//     *
//     * @param sysAnnouncement 公告管理
//     * @return 结果
//     */
//    public int insertSysAnnouncement(SysAnnouncement sysAnnouncement)
//    {
//        sysAnnouncement.setCreateTime(LocalDateTime.now());
//        return sysAnnouncementMapper.insertSysAnnouncement(sysAnnouncement);
//    }
//
//    /**
//     * 修改公告管理
//     *
//     * @param sysAnnouncement 公告管理
//     * @return 结果
//     */
//    public int updateSysAnnouncement(SysAnnouncement sysAnnouncement)
//    {
//        sysAnnouncement.setUpdateTime(LocalDateTime.now());
//        return sysAnnouncementMapper.updateSysAnnouncement(sysAnnouncement);
//    }
//
//    /**
//     * 批量删除公告管理
//     *
//     * @param ids 需要删除的公告管理主键
//     * @return 结果
//     */
//    public int deleteSysAnnouncementByIds(Long[] ids)
//    {
//        return sysAnnouncementMapper.deleteSysAnnouncementByIds(ids);
//    }
//
//    /**
//     * 删除公告管理信息
//     *
//     * @param id 公告管理主键
//     * @return 结果
//     */
//    public int deleteSysAnnouncementById(Long id)
//    {
//        return sysAnnouncementMapper.deleteSysAnnouncementById(id);
//    }
//
//    public int delStatus(Long[] ids) {
//        return sysAnnouncementMapper.delStatus(ids);
//    }
}
