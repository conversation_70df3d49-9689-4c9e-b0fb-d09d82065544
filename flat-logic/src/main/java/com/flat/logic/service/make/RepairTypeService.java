package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.make.RepairTypeAddReq;
import com.flat.logic.dto.req.make.RepairTypeEditReq;
import com.flat.logic.dto.req.make.RepairTypeQueryReq;
import com.flat.logic.entity.make.FlatRepairType;
import com.flat.logic.mapper.make.FlatRepairTypeMapper;
import com.flat.system.entity.SysUser;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 公寓报修类型
 */
@Service
public class RepairTypeService {

    @Resource
    private FlatRepairTypeMapper repairTypeMapper;

    public FlatRepairType queryDetail(Long id) {
        FlatRepairType repairType = repairTypeMapper.selectById(id);
        if (repairType == null || repairType.getDelStatus() == 1) {
            throw new ServiceException("报修类型不存在");
        }
        return repairType;
    }

    public TablePage<FlatRepairType> queryPage(RepairTypeQueryReq req) {
        return PageUtils.paginate(() -> repairTypeMapper.selectList(q -> q
                .like(StringUtils.isNotBlank(req.getName()), FlatRepairType::getName, req.getName())
                .eq(FlatRepairType::getDelStatus, 0)
                .orderByDesc(FlatRepairType::getId)
        ));
    }

    /**
     * 新增报修类型
     */
    public void add(RepairTypeAddReq req, SysUser user) {
        // 检查名称是否重复
        long count = repairTypeMapper.selectCount(q -> q
                .eq(FlatRepairType::getName, req.getName())
                .eq(FlatRepairType::getDelStatus, 0));
        if (count > 0) {
            throw new ServiceException("报修类型名称已存在");
        }

        // 新增报修类型
        FlatRepairType repairType = new FlatRepairType();
        BeanUtils.copyProperties(req, repairType);
        repairType.setCreateBy(user.getUsername());
        repairType.setCreateTime(LocalDateTime.now());
        repairType.setDelStatus(0);
        repairTypeMapper.insert(repairType);
    }

    /**
     * 修改报修类型
     */
    public void edit(RepairTypeEditReq req, SysUser user) {
        // 检查报修类型是否存在
        FlatRepairType repairType = queryDetail(req.getId());

        // 检查名称是否重复
        long count = repairTypeMapper.selectCount(q -> q
                .eq(FlatRepairType::getName, req.getName())
                .eq(FlatRepairType::getDelStatus, 0)
                .ne(FlatRepairType::getId, req.getId()));
        if (count > 0) {
            throw new ServiceException("报修类型名称已存在");
        }

        // 修改报修类型
        BeanUtils.copyProperties(req, repairType);
        repairType.setUpdateBy(user.getUsername());
        repairType.setUpdateTime(LocalDateTime.now());
        repairTypeMapper.updateById(repairType);
    }

    /**
     * 删除报修类型
     */
    public void remove(Long id, SysUser user) {
        repairTypeMapper.update(q -> q
                .set(FlatRepairType::getDelStatus, 1)
                .set(FlatRepairType::getUpdateTime, LocalDateTime.now())
                .set(FlatRepairType::getUpdateBy, user.getUsername())
                .eq(FlatRepairType::getId, id)
                .eq(FlatRepairType::getDelStatus, 0)
        );
    }
}
