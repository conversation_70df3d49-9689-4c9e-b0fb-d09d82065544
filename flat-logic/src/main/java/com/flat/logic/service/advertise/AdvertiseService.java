package com.flat.logic.service.advertise;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.advertise.AdvertiseAddReq;
import com.flat.logic.dto.req.advertise.AdvertiseEditReq;
import com.flat.logic.dto.req.advertise.AdvertiseQueryReq;
import com.flat.logic.dto.resp.advertise.AdvertiseResp;
import com.flat.logic.entity.advertise.FlatAdvertise;
import com.flat.logic.entity.advertise.FlatAdvertiseType;
import com.flat.logic.mapper.advertise.FlatAdvertiseMapper;
import com.flat.logic.mapper.advertise.FlatAdvertiseTypeMapper;
import com.flat.system.entity.SysUser;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公寓广告Service业务层处理
 */
@Service
public class AdvertiseService {

    @Resource
    private FlatAdvertiseMapper advertiseMapper;

    @Resource
    private FlatAdvertiseTypeMapper advertiseTypeMapper;

    public List<AdvertiseResp> queryRespList(AdvertiseQueryReq req) {
        return advertiseMapper.selectRespList(req);
    }

    public TablePage<AdvertiseResp> queryRespPage(AdvertiseQueryReq req) {
        return PageUtils.paginate(() -> advertiseMapper.selectRespList(req));
    }

    public AdvertiseResp queryRespDetail(Long id) {
        FlatAdvertise advertise = advertiseMapper.selectById(id);
        if (advertise == null || advertise.getDelStatus() == 1) {
            throw new ServiceException("广告不存在");
        }

        FlatAdvertiseType advertiseType = advertiseTypeMapper.selectById(advertise.getAdvertiseTypeId());
        if (advertiseType == null || advertiseType.getDelStatus() == 1) {
            throw new ServiceException("广告分类不存在");
        }

        AdvertiseResp resp = new AdvertiseResp();
        BeanUtils.copyProperties(advertise, resp);
        resp.setTypeName(advertiseType.getName());

        return resp;
    }

    /**
     * 添加广告
     */
    public void add(AdvertiseAddReq req, SysUser sysUser) {
        // 验证广告分类是否存在
        FlatAdvertiseType advertiseType = advertiseTypeMapper.selectById(req.getAdvertiseTypeId());
        if (advertiseType == null || advertiseType.getDelStatus() == 1) {
            throw new ServiceException("广告分类不存在");
        }

        // 检查相同分类下是否已存在相同名称的广告
        long count = advertiseMapper.selectCount(q -> q
                .eq(FlatAdvertise::getDelStatus, 0)
                .eq(FlatAdvertise::getName, req.getName())
                .eq(FlatAdvertise::getAdvertiseTypeId, req.getAdvertiseTypeId()));
        if (count > 0) {
            throw new ServiceException("该分类下已存在相同名称的广告");
        }

        FlatAdvertise advertise = new FlatAdvertise();
        BeanUtils.copyProperties(req, advertise);
        advertise.setCreateBy(sysUser.getUsername());
        advertise.setCreateTime(LocalDateTime.now());
        advertise.setDelStatus(0);
        advertiseMapper.insert(advertise);
    }

    /**
     * 修改广告
     */
    public void edit(AdvertiseEditReq req, SysUser sysUser) {
        // 验证广告是否存在
        FlatAdvertise advertise = advertiseMapper.selectById(req.getId());
        if (advertise == null || advertise.getDelStatus() == 1) {
            throw new ServiceException("广告不存在");
        }

        // 验证广告分类是否存在
        FlatAdvertiseType advertiseType = advertiseTypeMapper.selectById(req.getAdvertiseTypeId());
        if (advertiseType == null || advertiseType.getDelStatus() == 1) {
            throw new ServiceException("广告分类不存在");
        }

        // 检查相同分类下是否已存在相同名称的其他广告
        long count = advertiseMapper.selectCount(q -> q
                .eq(FlatAdvertise::getDelStatus, 0)
                .eq(FlatAdvertise::getName, req.getName())
                .eq(FlatAdvertise::getAdvertiseTypeId, req.getAdvertiseTypeId())
                .ne(FlatAdvertise::getId, req.getId()));
        if (count > 0) {
            throw new ServiceException("该分类下已存在相同名称的广告");
        }

        BeanUtils.copyProperties(req, advertise);
        advertise.setUpdateBy(sysUser.getUsername());
        advertise.setUpdateTime(LocalDateTime.now());
        advertiseMapper.updateById(advertise);
    }

    /**
     * 删除广告
     */
    public void remove(Long id, SysUser sysUser) {
        // 验证广告是否存在
        FlatAdvertise advertise = advertiseMapper.selectById(id);
        if (advertise == null || advertise.getDelStatus() == 1) {
            throw new ServiceException("广告不存在");
        }

        // 逻辑删除
        FlatAdvertise updateAdvertise = new FlatAdvertise();
        updateAdvertise.setId(id);
        updateAdvertise.setDelStatus(1);
        updateAdvertise.setUpdateBy(sysUser.getUsername());
        updateAdvertise.setUpdateTime(LocalDateTime.now());
        advertiseMapper.updateById(updateAdvertise);
    }
}
