package com.flat.logic.service.support;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.support.MessageBatchReadReq;
import com.flat.logic.dto.req.support.MessageQueryReq;
import com.flat.logic.dto.resp.support.FlatMessageResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.support.FlatMessage;
import com.flat.logic.mapper.support.FlatMessageMapper;
import com.flat.system.entity.SysConfig;
import com.flat.system.mapper.SysConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公寓消息通知Service业务层处理
 */
@Service
public class MessageService {

    @Resource
    private FlatMessageMapper messageMapper;

    @Resource
    private SysConfigMapper sysConfigMapper;

    public static final String TYPE_CONTRACT = "contract";
    public static final String TYPE_CHANGE = "change";

    public void sysToCus(Long senderId, Long receiveId, String content) {
        sysToCus(senderId, receiveId, content, null);
    }

    public void sysToCus(Long senderId, Long receiveId, String content, String skipUrl) {
        sysToCus(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void sysToCus(String type, Long senderId, Long receiveId, String content) {
        sysToCus(type, senderId, receiveId, content, null);
    }

    public void sysToCus(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("system");
        message.setSenderId(senderId);
        message.setReceiveType("customer");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }


    public void sysToButler(Long senderId, Long receiveId, String content) {
        sysToButler(senderId, receiveId, content, null);
    }

    public void sysToButler(Long senderId, Long receiveId, String content, String skipUrl) {
        sysToButler(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void sysToButler(String type, Long senderId, Long receiveId, String content) {
        sysToButler(type, senderId, receiveId, content, null);
    }

    public void sysToButler(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("system");
        message.setSenderId(senderId);
        message.setReceiveType("butler");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }


    public void sysToProperty(Long senderId, Long receiveId, String content) {
        sysToProperty(senderId, receiveId, content, null);
    }

    public void sysToProperty(Long senderId, Long receiveId, String content, String skipUrl) {
        sysToProperty(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void sysToProperty(String type, Long senderId, Long receiveId, String content) {
        sysToProperty(type, senderId, receiveId, content, null);
    }

    public void sysToProperty(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("system");
        message.setSenderId(senderId);
        message.setReceiveType("property");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }


    public void cusToButler(Long senderId, Long receiveId, String content) {
        cusToButler(senderId, receiveId, content, null);
    }

    public void cusToButler(Long senderId, Long receiveId, String content, String skipUrl) {
        cusToButler(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void cusToButler(String type, Long senderId, Long receiveId, String content) {
        cusToButler(type, senderId, receiveId, content, null);
    }

    public void cusToButler(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("customer");
        message.setSenderId(senderId);
        message.setReceiveType("butler");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }







    public void cusToProperty(Long senderId, Long receiveId, String content) {
        cusToProperty(senderId, receiveId, content, null);
    }

    public void cusToProperty(Long senderId, Long receiveId, String content, String skipUrl) {
        cusToProperty(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void cusToProperty(String type, Long senderId, Long receiveId, String content) {
        cusToProperty(type, senderId, receiveId, content, null);
    }

    public void cusToProperty(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("customer");
        message.setSenderId(senderId);
        message.setReceiveType("property");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }





    public void butlerToCus(Long senderId, Long receiveId, String content) {
        butlerToCus(senderId, receiveId, content, null);
    }

    public void butlerToCus(Long senderId, Long receiveId, String content, String skipUrl) {
        butlerToCus(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void butlerToCus(String type, Long senderId, Long receiveId, String content) {
        butlerToCus(type, senderId, receiveId, content, null);
    }

    public void butlerToCus(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }
        
        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("butler");
        message.setSenderId(senderId);
        message.setReceiveType("customer");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }




    public void butlerToProperty(Long senderId, Long receiveId, String content) {
        butlerToProperty(senderId, receiveId, content, null);
    }

    public void butlerToProperty(Long senderId, Long receiveId, String content, String skipUrl) {
        butlerToProperty(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void butlerToProperty(String type, Long senderId, Long receiveId, String content) {
        butlerToProperty(type, senderId, receiveId, content, null);
    }

    public void butlerToProperty(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("butler");
        message.setSenderId(senderId);
        message.setReceiveType("property");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }







    public void butlerToButler(Long senderId, Long receiveId, String content) {
        butlerToButler(senderId, receiveId, content, null);
    }

    public void butlerToButler(Long senderId, Long receiveId, String content, String skipUrl) {
        butlerToButler(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void butlerToButler(String type, Long senderId, Long receiveId, String content) {
        butlerToButler(type, senderId, receiveId, content, null);
    }

    public void butlerToButler(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("butler");
        message.setSenderId(senderId);
        message.setReceiveType("butler");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }


    public void propertyToCus(Long senderId, Long receiveId, String content) {
        propertyToCus(senderId, receiveId, content, null);
    }

    public void propertyToCus(Long senderId, Long receiveId, String content, String skipUrl) {
        propertyToCus(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void propertyToCus(String type, Long senderId, Long receiveId, String content) {
        propertyToCus(type, senderId, receiveId, content, null);
    }

    public void propertyToCus(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("property");
        message.setSenderId(senderId);
        message.setReceiveType("customer");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }


    public void propertyToButler(Long senderId, Long receiveId, String content) {
        propertyToButler(senderId, receiveId, content, null);
    }

    public void propertyToButler(Long senderId, Long receiveId, String content, String skipUrl) {
        propertyToButler(TYPE_CHANGE, senderId, receiveId, content, skipUrl);
    }

    public void propertyToButler(String type, Long senderId, Long receiveId, String content) {
        propertyToButler(type, senderId, receiveId, content, null);
    }

    public void propertyToButler(String type, Long senderId, Long receiveId, String content, String skipUrl) {
        if(senderId == null) {
            throw new ServiceException("消息发送者ID不能为空");
        }

        if(receiveId == null) {
            throw new ServiceException("消息接收者ID不能为空");
        }

        FlatMessage message = new FlatMessage();
        message.setType(type);
        message.setReadStatus(0);
        message.setContent(content);
        message.setSenderType("property");
        message.setSenderId(senderId);
        message.setReceiveType("butler");
        message.setReceiveId(receiveId);
        message.setSkipUrl(skipUrl);
        message.setDelStatus(0);
        message.setCreateTime(LocalDateTime.now());
        messageMapper.insert(message);
    }



    /**
     * 查询公寓消息通知
     *
     * @param id 公寓消息通知主键
     * @return 公寓消息通知
     */
    public FlatMessage queryDetail(Long id) {
        return messageMapper.selectById(id);
    }

    public void read(Long id, MaiUser maiUser) {
        FlatMessage message = messageMapper.selectById(id);
        if(message == null || message.getDelStatus() == 1) {
            throw new RuntimeException("消息不存在");
        }

        message.setReadStatus(1);
        message.setUpdateBy(maiUser.getUsername());
        message.setUpdateTime(LocalDateTime.now());
        messageMapper.updateById(message);
    }

    public void read(Long id, CusUser cusUser) {
        FlatMessage message = messageMapper.selectById(id);
        if(message == null || message.getDelStatus() == 1) {
            throw new RuntimeException("消息不存在");
        }

        message.setReadStatus(1);
        message.setUpdateBy(cusUser.getNickName());
        message.setUpdateTime(LocalDateTime.now());
        messageMapper.updateById(message);
    }

    public void batchRead(MessageBatchReadReq req, MaiUser maiUser) {
        messageMapper.update(q -> q
                .set(FlatMessage::getReadStatus, 1)
                .set(FlatMessage::getUpdateBy, maiUser.getUsername())
                .set(FlatMessage::getUpdateTime, LocalDateTime.now())
                .eq(FlatMessage::getReceiveId, maiUser.getUserId())
                .eq(FlatMessage::getReceiveType, req.getRoleType())
                .eq(FlatMessage::getReadStatus, 0)
                .eq(FlatMessage::getDelStatus, 0)
        );
    }

    public void batchRead(CusUser cusUser) {
        messageMapper.update(q -> q
                .set(FlatMessage::getReadStatus, 1)
                .set(FlatMessage::getUpdateBy, cusUser.getNickName())
                .set(FlatMessage::getUpdateTime, LocalDateTime.now())
                .eq(FlatMessage::getReceiveId, cusUser.getUserId())
                .eq(FlatMessage::getReceiveType, "customer")
                .eq(FlatMessage::getReadStatus, 0)
                .eq(FlatMessage::getDelStatus, 0)
        );
    }

    public long statUnread(String roleType, MaiUser maiUser) {
        return messageMapper.selectCount(q -> q
                .eq(FlatMessage::getReceiveId, maiUser.getUserId())
                .eq(FlatMessage::getReceiveType, roleType)
                .eq(FlatMessage::getReadStatus, 0)
                .eq(FlatMessage::getDelStatus, 0)
        );
    }

    public long statUnread(CusUser cusUser) {
        return messageMapper.selectCount(q -> q
                .eq(FlatMessage::getReceiveId, cusUser.getUserId())
                .eq(FlatMessage::getReceiveType, "customer")
                .eq(FlatMessage::getReadStatus, 0)
                .eq(FlatMessage::getDelStatus, 0)
        );
    }
//
//    /**
//     * 查询公寓消息通知列表
//     *
//     * @param flatMessage 公寓消息通知
//     * @return 公寓消息通知
//     */
//    public List<FlatMessage> selectFlatMessageList(FlatMessage flatMessage) {
//        return flatMessageMapper.selectFlatMessageList(flatMessage);
//    }
//
//    /**
//     * 新增公寓消息通知
//     *
//     * @param flatMessage 公寓消息通知
//     * @return 结果
//     */
//    public int insertFlatMessage(FlatMessage flatMessage) {
//        flatMessage.setCreateTime(LocalDateTime.now());
//        return flatMessageMapper.insertFlatMessage(flatMessage);
//    }
//
//    /**
//     * 修改公寓消息通知
//     *
//     * @param flatMessage 公寓消息通知
//     * @return 结果
//     */
//    public int updateFlatMessage(FlatMessage flatMessage) {
//        flatMessage.setUpdateTime(LocalDateTime.now());
//        return flatMessageMapper.updateFlatMessage(flatMessage);
//    }
//
//    /**
//     * 批量删除公寓消息通知
//     *
//     * @param ids 需要删除的公寓消息通知主键
//     * @return 结果
//     */
//    public int deleteFlatMessageByIds(Long[] ids) {
//        return flatMessageMapper.deleteFlatMessageByIds(ids);
//    }
//
//    /**
//     * 删除公寓消息通知信息
//     *
//     * @param id 公寓消息通知主键
//     * @return 结果
//     */
//    public int deleteFlatMessageById(Long id) {
//        return flatMessageMapper.deleteFlatMessageById(id);
//    }
//
//
//    public int delStatus(Long[] ids) {
//        return flatMessageMapper.delStatus(ids);
//    }
//
//
//    public List<FlatMessageResp> selectFlatMessageDtoList(FlatMessage flatMessage) {
//        return flatMessageMapper.selectFlatMessageDtoList(flatMessage);
//    }
//
    public TablePage<FlatMessageResp> queryRespPage(MessageQueryReq req) {
        List<SysConfig> configs = sysConfigMapper.selectList();
        return PageUtils.paginate(
                () -> messageMapper.selectRespList(req),
                (e) -> {
                    change(e);
                    configs.stream()
                            .filter(c -> c.getConfigKey().equals(e.getType()))
                            .findFirst()
                            .ifPresent(c -> e.setIcon(c.getConfigValue()));
                    return e;
                }
        );
    }

    private String getMessageType(String type) {
        return switch (type) {
            case "make_error" -> "预约失败";
            case "make_success" -> "预约成功";
            case "contract_charge" -> "合同出账";
            case "contract_relet" -> "合同续租通知";
            case "contract_expire" -> "合同到期";
            case "pay_inform" -> "付款通知";
            case "contract" -> "合同";
            case "bill" -> "账单";
            case "transaction" -> "交易流水";
            case "change" -> "处理通知";
            default -> "";
        };
    }

    private void change(FlatMessageResp response) {
        response.setTitle(getMessageType(response.getType()));
        response.setContent(response.getContent());

        switch (response.getType()) {
            case "make_room":
                response.setMake("预约看房通知：" + response.getContent());
                break;
            case "make_error":
                response.setMake("预约失败通知：" + response.getContent());
                break;
            case "make_success":
                response.setMake("预约成功通知：" + response.getContent());
                break;
            case "contract":
                response.setMake("合同通知：" + response.getContent());
                break;
            case "change":
                response.setMake("处理成功通知：" + response.getContent());
                break;
            default:
                response.setMake("");
                break;
        }
    }

}
