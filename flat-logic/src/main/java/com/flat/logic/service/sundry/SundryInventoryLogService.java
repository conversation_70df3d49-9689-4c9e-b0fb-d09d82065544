package com.flat.logic.service.sundry;

import java.time.LocalDateTime;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.sundry.SundryInventoryLogQueryReq;
import com.flat.logic.dto.req.sundry.SundryProductStockInReq;
import com.flat.logic.dto.req.sundry.SundryProductStockOutReq;
import com.flat.logic.dto.resp.sundry.SundryInventoryLogListResp;
import com.flat.logic.entity.sundry.SundryCategory;
import com.flat.logic.entity.sundry.SundryInventoryLog;
import com.flat.logic.entity.sundry.SundryProduct;
import com.flat.logic.mapper.sundry.SundryCategoryMapper;
import com.flat.logic.mapper.sundry.SundryInventoryLogMapper;
import com.flat.logic.mapper.sundry.SundryProductMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

@Service
public class SundryInventoryLogService {

    @Resource
    private SundryInventoryLogMapper sundryInventoryLogMapper;

    @Resource
    private SundryProductMapper sundryProductMapper;

    @Resource
    private SundryCategoryMapper sundryCategoryMapper;

    @Transactional
    public void stockIn(SundryProductStockInReq req, SysUser user) {
        SundryProduct product = sundryProductMapper.selectById(req.getProductId());
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }

        // 更新库存
        product.setTotalInventory(product.getTotalInventory() + req.getAmount());
        product.setUpdateBy(user.getUsername());
        product.setUpdateTime(LocalDateTime.now());
        sundryProductMapper.updateById(product);

        // 记录入库日志
        SundryInventoryLog log = new SundryInventoryLog();
        log.setProductId(req.getProductId());
        log.setAmount(req.getAmount());
        log.setType(1);
        log.setRemark(req.getRemark());
        log.setCreateBy(user.getUsername());
        log.setCreateTime(LocalDateTime.now());
        log.setDelStatus(0);
        sundryInventoryLogMapper.insert(log);
    }

    @Transactional
    public void stockOut(SundryProductStockOutReq req, SysUser user) {
        SundryProduct product = sundryProductMapper.selectById(req.getProductId());
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }

        // 检查库存
        if (product.getTotalInventory() < req.getAmount()) {
            throw new ServiceException("库存不足");
        }

        // 更新库存
        product.setTotalInventory(product.getTotalInventory() - req.getAmount());
        product.setUpdateBy(user.getUsername());
        product.setUpdateTime(LocalDateTime.now());
        sundryProductMapper.updateById(product);

        // 记录出库日志
        SundryInventoryLog log = new SundryInventoryLog();
        log.setProductId(req.getProductId());
        log.setAmount(req.getAmount());
        log.setType(2);
        log.setRemark(req.getRemark());
        log.setCreateBy(user.getUsername());
        log.setCreateTime(LocalDateTime.now());
        log.setDelStatus(0);
        sundryInventoryLogMapper.insert(log);
    }

    public TablePage<SundryInventoryLogListResp> listPage(SundryInventoryLogQueryReq req) {
        return PageUtils.paginate(() -> sundryInventoryLogMapper.selectRespList(req));
    }

    public SundryInventoryLogListResp detail(Long id) {
        SundryInventoryLog log = sundryInventoryLogMapper.selectById(id);
        if (log == null || log.getDelStatus() == 1) {
            throw new ServiceException("物品库产品进库与出库记录不存在");
        }

        SundryProduct product = sundryProductMapper.selectById(log.getProductId());
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }

        SundryCategory category = sundryCategoryMapper.selectById(product.getCategoryId());
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("物品库类别不存在");
        }

        SundryInventoryLogListResp resp = new SundryInventoryLogListResp();
        BeanUtils.copyProperties(log, resp);
        resp.setCategoryName(category.getName());
        resp.setProductName(product.getName());
        return resp;
    }

    @Transactional
    public void delete(Long id, SysUser user) {
        SundryInventoryLog log = sundryInventoryLogMapper.selectById(id);
        if (log == null || log.getDelStatus() == 1) {
            throw new ServiceException("物品库产品进库与出库记录不存在");
        }

        SundryProduct product = sundryProductMapper.selectById(log.getProductId());
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }
        // 按照入库或出库类型恢复库存
        if (log.getType() == 1) { // 入库
            product.setTotalInventory(product.getTotalInventory() - log.getAmount());
        } else if (log.getType() == 2) { // 出库
            product.setTotalInventory(product.getTotalInventory() + log.getAmount());
        } else {
            throw new ServiceException("物品库产品进库与出库记录类型错误");
        }
        product.setUpdateBy(user.getUsername());
        product.setUpdateTime(LocalDateTime.now());
        sundryProductMapper.updateById(product);

        log.setDelStatus(1);
        log.setUpdateBy(user.getUsername());
        log.setUpdateTime(LocalDateTime.now());
        sundryInventoryLogMapper.updateById(log);
    }
}
