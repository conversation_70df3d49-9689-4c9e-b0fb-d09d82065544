package com.flat.logic.service.account;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.common.utils.StringUtils;
import com.flat.common.utils.spring.SpringUtils;
import com.flat.logic.dto.req.account.MaiUserAddReq;
import com.flat.logic.dto.req.account.MaiUserEditReq;
import com.flat.logic.dto.req.account.MaiUserQueryReq;
import com.flat.logic.dto.resp.account.ButlerContactResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.mapper.account.FlatRelationMapper;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.service.cloud.WeChatPublicService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管家用户信息Service业务层处理
 */
@Service
public class MaiUserService {

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private FlatRelationMapper flatRelationMapper;

    @Resource
    private WeChatPublicService weChatPublicService;

    public List<ButlerContactResp> queryButlerContactList(CusUser cusUser) {
        return maiUserMapper.selectButlerContactList(cusUser.getUserId());
    }

    /**
     * 通过管家用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public MaiUser selectUserByUsername(String userName) {
        return maiUserMapper.selectUserByUsername(userName);
    }

    /**
     * 分页查询管家用户信息
     *
     * @param req 查询参数
     * @return 分页结果
     */
    public TablePage<MaiUser> queryPage(MaiUserQueryReq req) {
        return PageUtils.paginate(() -> maiUserMapper.selectList(q -> q
                .eq(req.getFlatId() != null, MaiUser::getFlatId, req.getFlatId())
                .like(StringUtils.isNotEmpty(req.getUsername()), MaiUser::getUsername, req.getUsername())
                .like(StringUtils.isNotEmpty(req.getNickName()), MaiUser::getNickName, req.getNickName())
                .eq(StringUtils.isNotEmpty(req.getUserType()), MaiUser::getUserType, req.getUserType())
                .eq(StringUtils.isNotEmpty(req.getPhoneNumber()), MaiUser::getPhoneNumber, req.getPhoneNumber())
                .eq(req.getStatus() != null, MaiUser::getStatus, req.getStatus())
                .eq(MaiUser::getDelStatus, 0)
                .orderByDesc(MaiUser::getCreateTime)
        ));
    }

    public List<MaiUser> queryList(MaiUserQueryReq req) {
        return maiUserMapper.selectList(q -> q
            .eq(req.getFlatId() != null, MaiUser::getFlatId, req.getFlatId())
            .like(StringUtils.isNotEmpty(req.getUsername()), MaiUser::getUsername, req.getUsername())
            .like(StringUtils.isNotEmpty(req.getNickName()), MaiUser::getNickName, req.getNickName())
            .eq(StringUtils.isNotEmpty(req.getUserType()), MaiUser::getUserType, req.getUserType())
            .eq(StringUtils.isNotEmpty(req.getPhoneNumber()), MaiUser::getPhoneNumber, req.getPhoneNumber())
            .eq(req.getStatus() != null, MaiUser::getStatus, req.getStatus())
            .eq(MaiUser::getDelStatus, 0)
            .orderByDesc(MaiUser::getCreateTime)
        );
    }

    /**
     * 查询管家用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    public MaiUser queryDetail(Long userId) {
        MaiUser user = maiUserMapper.selectFirst(q -> q
                .eq(MaiUser::getUserId, userId)
                .eq(MaiUser::getDelStatus, 0)
        );

        if(user == null) {
            throw new ServiceException("管家用户未找到");
        }

        return user;
    }

    /**
     * 新增管家用户
     *
     * @param req 新增参数
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean add(MaiUserAddReq req, String userType, SysUser sysUser) {
        if(StringUtils.isBlank(req.getPassword())) {
            throw new ServiceException("密码不能为空");
        }

        if(req.getGender() == null) {
            req.setGender(0);
        }

        // 检查用户名是否已存在
        if (selectUserByUsername(req.getUsername()) != null) {
            throw new ServiceException("用户名已存在");
        }
        // 检查手机号是否已存在
        if (maiUserMapper.selectCount(q -> q
                .eq(MaiUser::getPhoneNumber, req.getPhoneNumber())
                .eq(MaiUser::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("手机号已存在");
        }

        MaiUser maiUser = new MaiUser();
        BeanUtils.copyProperties(req, maiUser);
        maiUser.setUserType(userType);
        maiUser.setPassword(SpringUtils.getBean(PasswordEncoder.class).encode(req.getPassword()));
        if(req.getPermissions() != null && req.getPermissions().size() > 0) {
            maiUser.setPermission(JSON.toJSONString(req.getPermissions()));
        }
        maiUser.setCreateTime(LocalDateTime.now());
        maiUser.setCreateBy(sysUser.getUsername());
        maiUser.setDelStatus(0);
        return maiUserMapper.insert(maiUser) > 0;
    }

    /**
     * 修改管家用户
     *
     * @param req 修改参数
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean edit(MaiUserEditReq req, String userType, SysUser sysUser) {
        if (req.getUserId() == null) {
            throw new ServiceException("用户ID不能为空");
        }

        if(req.getGender() == null) {
            req.setGender(0);
        }

        // 检查用户是否存在
        MaiUser maiUser = maiUserMapper.selectById(req.getUserId());
        if (maiUser == null) {
            throw new ServiceException("用户不存在");
        }

        // 检查用户名是否重复
        MaiUser userByName = selectUserByUsername(req.getUsername());
        if (userByName != null && !userByName.getUserId().equals(req.getUserId())) {
            throw new ServiceException("用户名已存在");
        }

        // 检查手机号是否重复
        LambdaQueryWrapper<MaiUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaiUser::getPhoneNumber, req.getPhoneNumber())
                .ne(MaiUser::getUserId, req.getUserId());
        if (maiUserMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("手机号已存在");
        }

        String oldPassword = maiUser.getPassword();
        BeanUtils.copyProperties(req, maiUser);
        maiUser.setUserType(userType);
        if(StringUtils.isEmpty(req.getPassword())) {
            maiUser.setPassword(oldPassword);
        } else {
            maiUser.setPassword(SpringUtils.getBean(PasswordEncoder.class).encode(req.getPassword()));
        }

        if(req.getPermissions() != null && req.getPermissions().size() > 0) {
            maiUser.setPermission(JSON.toJSONString(req.getPermissions()));
        } else {
            maiUser.setPermission(null);
        }
        maiUser.setUpdateTime(LocalDateTime.now());
        maiUser.setUpdateBy(sysUser.getUsername());
        return maiUserMapper.updateById(maiUser) > 0;
    }

    /**
     * 删除管家用户
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long userId, SysUser sysUser) {
        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }
        maiUserMapper.update(q -> q
                .set(MaiUser::getDelStatus, 1)
                .set(MaiUser::getUpdateTime, LocalDateTime.now())
                .set(MaiUser::getUpdateBy, sysUser.getUsername())
                .eq(MaiUser::getUserId, userId)
                .eq(MaiUser::getDelStatus, 0)
        );
    }

    public void authWxPublic(MaiUser maiUser, String code) {
        JSONObject jsonObject = weChatPublicService.auth(code);
        String openId = jsonObject.getString("openid");
        String unionId = jsonObject.getString("unionid");
        maiUser.setWxPublicOpenId(openId);
        maiUser.setWxUnionId(unionId);
        maiUserMapper.update(q -> q
                .set(MaiUser::getWxPublicOpenId, openId)
                .set(MaiUser::getWxUnionId, unionId)
                .set(MaiUser::getUpdateTime, LocalDateTime.now())
                .set(MaiUser::getUpdateBy, maiUser.getUsername())
                .eq(MaiUser::getUserId, maiUser.getUserId())
                .eq(MaiUser::getDelStatus, 0)
        );
    }
}
