package com.flat.logic.service.cloud;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.core.redis.RedisCache;
import com.flat.common.exception.ServiceException;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;

import cn.hutool.http.HttpUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WeChatPublicMessageService {

    @Value("${wx.public.appId}")
    private String appId;

    @Value("${wx.public.appSecret}")
    private String appSecret;

    @Value("${wx.user.appId}")
    private String userAppId;

    @Value("${wx.butler.appId}")
    private String butlerAppId;

    @Resource
    private RedisCache redisCache;

    public String queryAccessToken() {
        String accessToken = redisCache.getCacheObject("wx:publicMessage:accessToken");
        if(accessToken != null) {
            return accessToken;
        }

        Map<String, Object> param = new HashMap<>(10);
        param.put("appid", appId);
        param.put("secret", appSecret);
        param.put("grant_type", "client_credential");

        try {
            String wxResult = HttpUtil.get("https://api.weixin.qq.com/cgi-bin/token", param);
            JSONObject jsonObject = JSON.parseObject(wxResult);
            accessToken = jsonObject.getString("access_token");
        } catch (Exception e) {
            throw new ServiceException("获取accessToken失败", e);
        }

        if (accessToken == null) {
            throw new ServiceException("获取accessToken失败");
        }

        redisCache.setCacheObject("wx:publicMessage:accessToken", accessToken, 10, TimeUnit.MINUTES);
        return accessToken;
    }

    //用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。
    public Integer checkSubscribe(String openId) {
        if(StringUtils.isBlank(openId)) {
            return 0;
        }

        Map<String, Object> param = new HashMap<>(10);
        param.put("access_token", queryAccessToken());
        param.put("openid", openId);
        param.put("lang", "zh_CN");

        Integer subscribe;
        try {
            String wxResult = HttpUtil.get("https://api.weixin.qq.com/cgi-bin/user/info", param);
            JSONObject jsonObject = JSON.parseObject(wxResult);
            subscribe = jsonObject.getInteger("subscribe");
        } catch (Exception e) {
            throw new ServiceException("获取微信公众号用户基本信息失败", e);
        }

        if (subscribe == null) {
            throw new ServiceException("获取微信公众号用户基本信息失败");
        }

        return subscribe;
    }

    public void sendMessage(CusUser cusUser, String templateId, String pagePath, Map<String, Object> data) {
        log.info("用户{}微信消息发送, 参数: templateId={}, pagePath={}, data={}", cusUser.getUserId(), templateId, pagePath, data);
        
        if(StringUtils.isBlank(cusUser.getWxPublicOpenId())) {
            log.warn("用户{}未关注公众号，无法发送微信消息", cusUser.getUserId());
            return;
        }

        try {
            String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + queryAccessToken();
            String wxResult = HttpUtil.post(url, JSON.toJSONString(Map.of(
                    "touser", cusUser.getWxPublicOpenId(),
                    "template_id", templateId,
                    "miniprogram", Map.of(
                            "appid", userAppId,
                            "pagepath", pagePath
                    ),
                    "data", data
            )));
            log.info("用户{}微信消息发送结果: {}, 参数: templateId={}, pagePath={}, data={}", cusUser.getUserId(), wxResult, templateId, pagePath, data);
        } catch (Exception e) {
            log.warn("用户{}微信消息发送失败, 参数: templateId={}, pagePath={}, data={}", cusUser.getUserId(), templateId, pagePath, data, e);
            throw new ServiceException("微信消息发送失败", e);
        }
    }

    public void sendMessage(MaiUser maiUser, String templateId, String pagePath, Map<String, Object> data) {
        log.info("管家用户{}微信消息发送, 参数: templateId={}, pagePath={}, data={}", maiUser.getUserId(), templateId, pagePath, data);

        if(StringUtils.isBlank(maiUser.getWxPublicOpenId())) {
            log.warn("管家用户{}未关注公众号，无法发送微信消息", maiUser.getUserId());
            return;
        }

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("appid", butlerAppId);
        if(StringUtils.isNotBlank(pagePath)) {
            metadata.put("pagepath", pagePath);
        }

        try {
            String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + queryAccessToken();
            String wxResult = HttpUtil.post(url, JSON.toJSONString(Map.of(
                    "touser", maiUser.getWxPublicOpenId(),
                    "template_id", templateId,
                    "miniprogram", metadata,
                    "data", data
            )));
            log.info("管家用户{}微信消息发送结果: {}, 参数: templateId={}, pagePath={}, data={}", maiUser.getUserId(), wxResult, templateId, pagePath, data);
        } catch (Exception e) {
            log.warn("管家用户{}微信消息发送失败, 参数: templateId={}, pagePath={}, data={}", maiUser.getUserId(), templateId, pagePath, data, e);
            throw new ServiceException("微信消息发送失败", e);
        }
    }
}
