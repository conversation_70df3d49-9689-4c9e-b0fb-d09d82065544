package com.flat.logic.service.news;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.news.NewsCategoryAddReq;
import com.flat.logic.dto.req.news.NewsCategoryEditReq;
import com.flat.logic.dto.req.news.NewsCategoryQueryReq;
import com.flat.logic.dto.resp.news.NewsCategoryResp;
import com.flat.logic.entity.news.NewsCategory;
import com.flat.logic.mapper.news.NewsCategoryMapper;
import com.flat.system.entity.SysUser;
import com.github.pagehelper.PageInfo;

import jakarta.annotation.Resource;

/**
 * 新闻分类服务
 */
@Service
public class NewsCategoryService {

    @Resource
    private NewsCategoryMapper newsCategoryMapper;

    /**
     * 添加新闻分类
     */
    public void add(NewsCategoryAddReq req, SysUser sysUser) {
        // 检查同名分类是否存在
        if (newsCategoryMapper.selectCount(q -> q
                .eq(NewsCategory::getName, req.getName())
                .eq(NewsCategory::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("新闻分类名称已存在");
        }

        // 创建新闻分类对象
        NewsCategory newsCategory = new NewsCategory();
        BeanUtils.copyProperties(req, newsCategory);
        
        // 设置创建者信息
        newsCategory.setCreateBy(sysUser.getUsername());
        newsCategory.setCreateTime(LocalDateTime.now());
        newsCategory.setDelStatus(0);
        
        // 保存到数据库
        newsCategoryMapper.insert(newsCategory);
    }

    /**
     * 编辑新闻分类
     */
    public void edit(NewsCategoryEditReq req, SysUser sysUser) {
        // 检查分类是否存在
        NewsCategory newsCategory = newsCategoryMapper.selectById(req.getId());
        if (newsCategory == null || newsCategory.getDelStatus() == 1) {
            throw new ServiceException("新闻分类不存在");
        }
        
        // 检查同名分类是否存在（排除自身）
        if (newsCategoryMapper.selectCount(q -> q
                .eq(NewsCategory::getName, req.getName())
                .eq(NewsCategory::getDelStatus, 0)
                .ne(NewsCategory::getId, req.getId())
        ) > 0) {
            throw new ServiceException("新闻分类名称已存在");
        }
        
        // 更新分类信息
        newsCategory.setName(req.getName());
        newsCategory.setUpdateBy(sysUser.getUsername());
        newsCategory.setUpdateTime(LocalDateTime.now());
        
        // 保存到数据库
        newsCategoryMapper.updateById(newsCategory);
    }

    /**
     * 删除新闻分类
     */
    public void remove(Long id, SysUser sysUser) {
        // 检查分类是否存在
        NewsCategory newsCategory = newsCategoryMapper.selectById(id);
        if (newsCategory == null || newsCategory.getDelStatus() == 1) {
            throw new ServiceException("新闻分类不存在");
        }
        
        // 逻辑删除
        newsCategory.setDelStatus(1);
        newsCategory.setUpdateBy(sysUser.getUsername());
        
        // 保存到数据库
        newsCategoryMapper.updateById(newsCategory);
    }

    /**
     * 查询新闻分类详情
     */
    public NewsCategoryResp queryDetail(Long id) {
        // 查询分类
        NewsCategory newsCategory = newsCategoryMapper.selectById(id);
        if (newsCategory == null || newsCategory.getDelStatus() == 1) {
            throw new ServiceException("新闻分类不存在");
        }
        
        // 转换为响应对象
        NewsCategoryResp resp = new NewsCategoryResp();
        BeanUtils.copyProperties(newsCategory, resp);
        
        return resp;
    }

    /**
     * 查询新闻分类列表
     */
    public List<NewsCategoryResp> queryList(NewsCategoryQueryReq req) {
        // 查询分类列表
        List<NewsCategory> list = newsCategoryMapper.selectList(q -> q
                .like(req.getName() != null, NewsCategory::getName, req.getName())
                .eq(NewsCategory::getDelStatus, 0)
                .orderByDesc(NewsCategory::getCreateTime)
        );
        
        // 转换为响应对象
        return list.stream().map(item -> {
            NewsCategoryResp resp = new NewsCategoryResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).toList();
    }

    /**
     * 分页查询新闻分类
     */
    public TablePage<NewsCategoryResp> queryPage(NewsCategoryQueryReq req) {
        // 分页查询
        List<NewsCategory> list = PageUtils.execute(() -> newsCategoryMapper.selectList(q -> q
                .like(req.getName() != null, NewsCategory::getName, req.getName())
                .eq(NewsCategory::getDelStatus, 0)
                .orderByDesc(NewsCategory::getCreateTime)
        ));
        
        // 转换为响应对象
        List<NewsCategoryResp> respList = list.stream().map(item -> {
            NewsCategoryResp resp = new NewsCategoryResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).toList();
        
        // 创建分页结果
        return TablePage.from(new PageInfo<>(list), respList);
    }
} 