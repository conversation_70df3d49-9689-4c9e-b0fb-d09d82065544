package com.flat.logic.service.activity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.activity.ActivityJoinReq;
import com.flat.logic.dto.req.activity.ActivityPayReq;
import com.flat.logic.dto.req.activity.ActivityUserQueryReq;
import com.flat.logic.dto.resp.activity.ActivityPayResp;
import com.flat.logic.dto.resp.activity.ActivityUserExportResp;
import com.flat.logic.dto.resp.activity.ActivityUserResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.activity.Activity;
import com.flat.logic.entity.activity.ActivityUser;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.activity.ActivityMapper;
import com.flat.logic.mapper.activity.ActivityUserMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.logic.service.finance.BillService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.Resource;

/**
 * 活动用户关联服务
 */
@Service
public class ActivityUserService {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private ActivityUserMapper activityUserMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private BillService billService;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    @Resource
    private SysConfigService sysConfigService;

    /**
     * 用户报名活动
     *
     * @param req 报名请求
     * @param cusUser 用户信息
     * @return 活动用户关联ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long join(ActivityJoinReq req, CusUser cusUser) {
        Activity activity = activityMapper.selectById(req.getActivityId());
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        // 检查报名时间
        LocalDateTime now = LocalDateTime.now();
        
        // 检查报名开始时间
        if (activity.getRegisterBeginTime() != null && now.isBefore(activity.getRegisterBeginTime())) {
            throw new ServiceException("活动报名尚未开始，开始报名时间：" + 
                    activity.getRegisterBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        // 检查报名结束时间
        if (activity.getRegisterEndTime() != null && now.isAfter(activity.getRegisterEndTime())) {
            throw new ServiceException("活动报名已结束，报名截止时间：" + 
                    activity.getRegisterEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        // 检查活动状态
        if (now.isBefore(activity.getBeginTime())) {
            // 可以提前报名
        } else if (now.isAfter(activity.getEndTime())) {
            throw new ServiceException("活动已结束，无法报名");
        }

        // 检查是否已报名
        Long count = activityUserMapper.selectCount(q -> q
                .eq(ActivityUser::getActivityId, req.getActivityId())
                .eq(ActivityUser::getCusId, cusUser.getUserId())
                .eq(ActivityUser::getDelStatus, 0));

        if (count > 0) {
            throw new ServiceException("您已报名该活动，请勿重复报名");
        }

        // 检查人数限制
        if (activity.getMaxAmount() > 0) {
            Long joinCount = activityUserMapper.selectCount(q -> q
                    .eq(ActivityUser::getActivityId, req.getActivityId())
                    .eq(ActivityUser::getDelStatus, 0));

            if (joinCount >= activity.getMaxAmount()) {
                throw new ServiceException("活动报名人数已满");
            }
        }

        // 创建活动用户关联记录
        ActivityUser activityUser = new ActivityUser();
        activityUser.setActivityId(req.getActivityId());
        activityUser.setCusId(cusUser.getUserId());
        activityUser.setRealName(req.getRealName());
        activityUser.setPhoneNumber(req.getPhoneNumber());
        activityUser.setEntryFeeFlag(activity.getEntryFeeFlag());
        activityUser.setEntryFee(activity.getEntryFee());
        activityUser.setPayStatus(0); // 初始状态为未支付
        activityUser.setPayMode(0); // 默认为线上支付
        activityUser.setCreateBy(cusUser.getRealName());
        activityUser.setCreateTime(LocalDateTime.now());
        activityUser.setDelStatus(0);

        activityUserMapper.insert(activityUser);

        return activityUser.getId();
    }

    /**
     * 支付活动报名费
     *
     * @param req 支付请求
     * @param cusUser 用户信息
     * @return 支付结果
     * @throws ServiceException 业务异常
     */
    @Transactional(rollbackFor = Exception.class)
    public ActivityPayResp pay(ActivityPayReq req, CusUser cusUser) {
        ActivityUser activityUser = activityUserMapper.selectById(req.getActivityUserId());
        if (activityUser == null || activityUser.getDelStatus() == 1) {
            throw new ServiceException("报名记录不存在");
        }

        if (!activityUser.getCusId().equals(cusUser.getUserId())) {
            throw new ServiceException("无权操作此报名记录");
        }

        if (activityUser.getPayStatus() == 1) {
            throw new ServiceException("已支付，请勿重复支付");
        }

        Activity activity = activityMapper.selectById(activityUser.getActivityId());
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        // 检查活动状态
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(activity.getEndTime())) {
            throw new ServiceException("活动已结束，无法支付");
        }

        FlatCompany company = companyMapper.selectBySymbol("by");
        if (company == null) {
            throw new ServiceException("收款企业不存在");
        }

        // 检查是否需要支付
        if (activityUser.getEntryFeeFlag() == 0 || activityUser.getEntryFee() == null || activityUser.getEntryFee().compareTo(BigDecimal.ZERO) <= 0) {
            // 免费活动，直接返回
            return ActivityPayResp.noPay();
        }

        // 检查是否已创建订单
        FlatBill bill = billMapper.selectFirst(q -> q
                .eq(FlatBill::getActivityUserId, activityUser.getId())
                .eq(FlatBill::getDelStatus, 0));

        if (bill == null) {
            String outTradeNo = "ACT" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);

            // 创建账单
            bill = new FlatBill();
            bill.setOrderNo(outTradeNo);
            bill.setMoney(activityUser.getEntryFee());
            bill.setPayChannel(1);
            bill.setCusId(cusUser.getUserId());
            bill.setPayTarget("activity-entry"); // 活动支付
            bill.setReadStatus(0);
            bill.setPayStatus(0);
            bill.setPayMode(0); // 线上支付
            bill.setInvoiceStatus(0);
            bill.setUseCouponFlag(0);
            bill.setActivityUserId(activityUser.getId());
            bill.setReceiveCompanyId(company.getId());
            bill.setName(activity.getName()); // 活动名称作为账单名称
            bill.setCreateBy(cusUser.getRealName());
            bill.setCreateTime(LocalDateTime.now());
            bill.setDelStatus(0);
            billMapper.insert(bill);

            // 更新活动用户关联账单ID
            activityUser.setUpdateTime(LocalDateTime.now());
            activityUser.setUpdateBy(cusUser.getRealName());
            activityUserMapper.updateById(activityUser);
        } else {
            if (bill.getPayStatus() == 1) {
                throw new ServiceException("订单已支付，请勿重复支付");
            }
        }

        // 调用微信支付接口
        WxPayMpOrderResult wxPayResult = billService.pay(bill, company, cusUser);
        return ActivityPayResp.needPay(wxPayResult);
    }

    /**
     * 处理支付回调
     *
     * @param outTradeNo 商户订单号
     * @param xmlData XML数据
     * @throws WxPayException 微信支付异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlePay(String outTradeNo, String xmlData) throws WxPayException {
        // 查询支付记录
        FlatPayLog payLog = payLogMapper.selectFirst(q -> q
                .eq(FlatPayLog::getOutTradeNo, outTradeNo)
                .eq(FlatPayLog::getChannel, 0)
                .eq(FlatPayLog::getDelStatus, 0));

        if (payLog == null) {
            throw new ServiceException("支付记录不存在");
        }

        if (payLog.getPayStatus() == 1) {
            return; // 已处理过的支付
        }

        // 查询账单
        FlatBill bill = billMapper.selectById(payLog.getBillId());
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单不存在");
        }

        if (bill.getPayStatus() == 1) {
            return; // 账单已支付
        }

        // 查询活动用户关联
        ActivityUser activityUser = activityUserMapper.selectById(bill.getActivityUserId());
        if (activityUser == null || activityUser.getDelStatus() == 1) {
            throw new ServiceException("活动报名记录不存在");
        }

        Activity activity = activityMapper.selectById(activityUser.getActivityId());
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        FlatCompany company = companyMapper.selectById(bill.getReceiveCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        CusUser cusUser = cusUserMapper.selectById(activityUser.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        // 解析支付结果
        WxPayOrderNotifyResult result = weChatPayService.parseOrderNotifyResult(company.getWxPayAppId(), company.getWxPayMchId(), xmlData);
        if (!"SUCCESS".equals(result.getResultCode())) {
            throw new ServiceException("支付失败: " + result.getReturnMsg());
        }

        // 更新支付记录
        payLog.setPayStatus(1);
        payLog.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payLog.setUpdateTime(LocalDateTime.now());
        payLogMapper.updateById(payLog);

        // 更新账单
        bill.setPayStatus(1);
        bill.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        bill.setUpdateTime(LocalDateTime.now());
        billMapper.updateById(bill);

        // 更新活动用户关联
        activityUser.setPayStatus(1);
        activityUser.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        activityUser.setUpdateTime(LocalDateTime.now());
        activityUserMapper.updateById(activityUser);


        String butlerIdStr = sysConfigService.queryConfigByKey("message_notify_butler");
        if (StringUtils.isNotBlank(butlerIdStr)) {
            Long butlerId = Long.parseLong(butlerIdStr);

            MaiUser maiUser = maiUserMapper.selectById(butlerId);
            if(maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
                weChatPublicMessageService.sendMessage(
                        maiUser,
                        "cAq85XSX6f_rdjvcoC1sZVB8ls7isKDkBRECICtxGN0",
                        null,
                        Map.of(
                                "thing10", Map.of("value", "--"),
                                "thing11", Map.of("value", cusUser.getRealName()),
                                "amount6", Map.of("value", bill.getMoney().toString()),
                                "thing14", Map.of("value", activity.getName() + "报名费用支付"),
                                "time5", Map.of("value", bill.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        )
                );
            }
        }
    }

    /**
     * 分页查询活动报名人员列表
     *
     * @param req 查询条件
     * @return 报名人员列表分页结果
     */
    public TablePage<ActivityUserResp> queryPage(ActivityUserQueryReq req) {
        return PageUtils.paginate(() -> activityUserMapper.selectActivityUserList(req));
    }

    /**
     * 根据活动ID获取我参加的记录详情
     *
     * @param userActivityId 活动报名记录ID
     * @return 报名记录详情
     */
    public ActivityUserResp queryDetail(Long userActivityId) {
        return activityUserMapper.selectUserActivityDetail(userActivityId);
    }

    public ActivityUserResp checkJoin(Long cusId, Long activityId) {
        return activityUserMapper.selectUserJoinActivity(cusId, activityId);
    }

    /**
     * 导出活动报名人员列表
     *
     * @param req 查询条件
     * @return 活动报名人员导出数据列表
     */
    public List<ActivityUserExportResp> exportList(ActivityUserQueryReq req) {
        List<ActivityUserResp> respList = activityUserMapper.selectActivityUserList(req);
        List<ActivityUserExportResp> exportList = new ArrayList<>();

        for (ActivityUserResp resp : respList) {
            ActivityUserExportResp exportResp = new ActivityUserExportResp();
            exportResp.setId(resp.getId());
            exportResp.setActivityName(resp.getActivityName());
            exportResp.setActivityBeginTime(resp.getActivityBeginTime());
            exportResp.setActivityEndTime(resp.getActivityEndTime());

            // 设置活动状态名称
            exportResp.setProcessStatus(resp.getProcessStatus());
            if (resp.getProcessStatus() != null) {
                switch (resp.getProcessStatus()) {
                    case 0:
                        exportResp.setProcessStatusName("未开始");
                        break;
                    case 1:
                        exportResp.setProcessStatusName("进行中");
                        break;
                    case 2:
                        exportResp.setProcessStatusName("已结束");
                        break;
                    default:
                        exportResp.setProcessStatusName("未知");
                }
            }

            exportResp.setRealName(resp.getRealName());
            exportResp.setPhoneNumber(resp.getPhoneNumber());

            // 设置报名费类型名称
            exportResp.setEntryFeeFlag(resp.getEntryFeeFlag());
            if (resp.getEntryFeeFlag() != null) {
                if (resp.getEntryFeeFlag() == 0) {
                    exportResp.setEntryFeeFlagName("免费");
                } else if (resp.getEntryFeeFlag() == 1) {
                    exportResp.setEntryFeeFlagName("需要报名费");
                } else {
                    exportResp.setEntryFeeFlagName("未知");
                }
            }

            exportResp.setEntryFee(resp.getEntryFee());

            // 设置支付状态名称
            exportResp.setPayStatus(resp.getPayStatus());
            if (resp.getPayStatus() != null) {
                if (resp.getPayStatus() == 0) {
                    exportResp.setPayStatusName("未支付");
                } else if (resp.getPayStatus() == 1) {
                    exportResp.setPayStatusName("已支付");
                } else {
                    exportResp.setPayStatusName("未知");
                }
            }

            // 设置支付方式名称
            exportResp.setPayMode(resp.getPayMode());
            if (resp.getPayMode() != null) {
                if (resp.getPayMode() == 0) {
                    exportResp.setPayModeName("线上支付");
                } else if (resp.getPayMode() == 1) {
                    exportResp.setPayModeName("线下支付");
                } else {
                    exportResp.setPayModeName("未知");
                }
            }

            exportResp.setPayTime(resp.getPayTime());
            exportResp.setCreateTime(resp.getCreateTime());

            exportList.add(exportResp);
        }

        return exportList;
    }

    /**
     * 删除活动报名人员
     *
     * @param id 报名记录ID
     * @param sysUser 操作用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id, SysUser sysUser) {
        // 查询报名记录
        ActivityUser activityUser = activityUserMapper.selectById(id);
        if (activityUser == null || activityUser.getDelStatus() == 1) {
            throw new ServiceException("报名记录不存在");
        }

        // 查询活动信息
        Activity activity = activityMapper.selectById(activityUser.getActivityId());
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        // 检查活动是否已结束
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(activity.getEndTime())) {
            throw new ServiceException("活动已结束，无法删除报名记录");
        }

        // 检查是否已支付
        if (activityUser.getPayStatus() == 1) {
            throw new ServiceException("该报名已支付费用，不能直接删除");
        }

        // 查询关联的账单
        FlatBill bill = billMapper.selectFirst(q -> q
                .eq(FlatBill::getActivityUserId, activityUser.getId())
                .eq(FlatBill::getDelStatus, 0));

        // 如果存在账单，也需要逻辑删除
        if (bill != null) {
            bill.setDelStatus(1);
            bill.setUpdateBy(sysUser.getUsername());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);
        }

        // 逻辑删除报名记录
        activityUser.setDelStatus(1);
        activityUser.setUpdateBy(sysUser.getUsername());
        activityUser.setUpdateTime(LocalDateTime.now());
        activityUserMapper.updateById(activityUser);
    }
}
