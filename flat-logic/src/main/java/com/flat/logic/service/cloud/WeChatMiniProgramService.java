package com.flat.logic.service.cloud;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.core.redis.RedisCache;
import com.flat.common.exception.ServiceException;
import com.flat.logic.dto.resp.FileUploadResp;
import com.flat.logic.model.WeChatSession;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class WeChatMiniProgramService {

    @Value("${wx.user.appId}")
    private String appId;

    @Value("${wx.user.appSecret}")
    private String appSecret;

    @Resource
    private OSSService ossService;

    @Resource
    private RedisCache redisCache;

    public String queryAccessToken() {
        String accessToken = redisCache.getCacheObject("wx:accessToken");
        if(accessToken != null) {
            return accessToken;
        }

        Map<String, Object> param = new HashMap<>(10);
        param.put("appid", appId);
        param.put("secret", appSecret);
        param.put("grant_type", "client_credential");

        try {
            String wxResult = HttpUtil.get("https://api.weixin.qq.com/cgi-bin/token", param);
            JSONObject jsonObject = JSON.parseObject(wxResult);
            accessToken = jsonObject.getString("access_token");
        } catch (Exception e) {
            throw new ServiceException("获取accessToken失败", e);
        }

        if (accessToken == null) {
            throw new ServiceException("获取accessToken失败");
        }

        redisCache.setCacheObject("wx:accessToken", accessToken, 10, TimeUnit.MINUTES);
        return accessToken;
    }

    public WeChatSession querySession(String code) {
        JSONObject respJson;
        try {
            String wxResult = HttpUtil.get(
                    "https://api.weixin.qq.com/sns/jscode2session",
                    Map.of(
                            "appid", appId,
                            "secret", appSecret,
                            "js_code", code,
                            "grant_type", "authorization_code"
                    )
            );
            respJson = JSON.parseObject(wxResult);
        } catch (Exception e) {
            throw new ServiceException("获取微信session失败", e);
        }

        if(respJson.containsKey("errmsg")) {
            throw new ServiceException("获取微信session失败：" + respJson.getString("errmsg"));
        }

        WeChatSession session = new WeChatSession();
        if(respJson.containsKey("session_key")) {
            session.setSessionKey(respJson.getString("session_key"));
        } else {
            throw new ServiceException("获取wechat session key失败");
        }

        if (respJson.containsKey("openid")) {
            session.setOpenId(respJson.getString("openid"));
        } else {
            throw new ServiceException("获取wechat openid失败");
        }

        if (respJson.containsKey("unionid")) {
            session.setUnionId(respJson.getString("unionid"));
        } else {
            throw new ServiceException("获取wechat unionid失败");
        }

        return session;
    }

    public FileUploadResp queryQrCodeImage(String path) {
        String accessToken = queryAccessToken();

        try {
            String url = "https://api.weixin.qq.com/wxa/getwxacode?access_token=" + accessToken;
            try(HttpResponse response = HttpUtil.createPost(url).body(JSON.toJSONString(Map.of(
                    "path", path
            ))).execute()) {
                byte[] bytes = response.bodyBytes();
                FileUtil.writeBytes(bytes, "/Users/<USER>/tmp/a.jpeg");

                return ossService.uploadFile(bytes, "image/jpeg", "jpeg");
            }
        } catch (Exception e) {
            throw new ServiceException("获取微信小程序码失败", e);
        }
    }

}
