package com.flat.logic.service.cost;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.cost.CostTypeAddReq;
import com.flat.logic.dto.req.cost.CostTypeEditReq;
import com.flat.logic.dto.req.cost.CostTypeQueryReq;
import com.flat.logic.dto.resp.cost.CostTypeDetailResp;
import com.flat.logic.dto.resp.coupon.CouponTemplateResp;
import com.flat.logic.entity.cost.FlatCostDelivery;
import com.flat.logic.entity.cost.FlatCostType;
import com.flat.logic.entity.cost.FlatCostTypeDelivery;
import com.flat.logic.mapper.cost.FlatCostDeliveryMapper;
import com.flat.logic.mapper.cost.FlatCostTypeDeliveryMapper;
import com.flat.logic.mapper.cost.FlatCostTypeMapper;
import com.flat.logic.mapper.coupon.CouponTemplateMapper;
import com.flat.logic.dto.req.coupon.CouponTemplateQueryReq;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 费用类型服务
 */
@Service
public class CostTypeService {

    @Resource
    private FlatCostTypeMapper costTypeMapper;

    @Resource
    private FlatCostDeliveryMapper costDeliveryMapper;

    @Resource
    private FlatCostTypeDeliveryMapper costTypeDeliveryMapper;
    
    @Resource
    private CouponTemplateMapper couponTemplateMapper;

    /**
     * 添加费用类型
     */
    public void add(CostTypeAddReq req, SysUser sysUser) {
        // 判断定点取货条目是否存在
        if (req.getUseDeliveryFlag() == 1) {
            if (req.getDeliveryIds() == null || req.getDeliveryIds().isEmpty()) {
                throw new ServiceException("定点取货条目不能为空");
            }   

            List<FlatCostDelivery> deliveries = costDeliveryMapper.selectByIds(req.getDeliveryIds());
            if (deliveries.size() != req.getDeliveryIds().size()) {
                throw new ServiceException("定点取货条目不存在");
            }
        }
        // 判断名称是否重复
        if (costTypeMapper.selectCount(q -> q
                .eq(FlatCostType::getName, req.getName())
                .eq(FlatCostType::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("费用类型名称已存在");
        }

        FlatCostType costType = new FlatCostType();
        BeanUtils.copyProperties(req, costType);
        costType.setCreateBy(sysUser.getUsername());
        costType.setCreateTime(LocalDateTime.now());
        costType.setDelStatus(0);
        costTypeMapper.insert(costType);

        if (req.getUseDeliveryFlag() == 1) {
            for (Long deliveryId : req.getDeliveryIds()) {
                FlatCostTypeDelivery costTypeDelivery = new FlatCostTypeDelivery();
                costTypeDelivery.setCostTypeId(costType.getId());
                costTypeDelivery.setDeliveryId(deliveryId);
                costTypeDelivery.setCreateBy(sysUser.getUsername());
                costTypeDelivery.setCreateTime(LocalDateTime.now());
                costTypeDelivery.setDelStatus(0);
                costTypeDeliveryMapper.insert(costTypeDelivery);
            }
        }
    }

    /**
     * 修改费用类型
     */
    public void edit(CostTypeEditReq req, SysUser sysUser) {
        // 判断定点取货条目是否存在
        if (req.getUseDeliveryFlag() == 1) {
            if (req.getDeliveryIds() == null || req.getDeliveryIds().isEmpty()) {
                throw new ServiceException("定点取货条目不能为空");
            }
        
            List<FlatCostDelivery> deliveries = costDeliveryMapper.selectByIds(req.getDeliveryIds());
            if (deliveries.size() != req.getDeliveryIds().size()) {
                throw new ServiceException("定点取货条目不存在");
            }
        }

        // 判断名称是否重复
        if (costTypeMapper.selectCount(q -> q
                .eq(FlatCostType::getName, req.getName())
                .ne(FlatCostType::getId, req.getId())
                .eq(FlatCostType::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("费用类型名称已存在");
        }

        FlatCostType costType = costTypeMapper.selectById(req.getId());
        if (costType == null) {
            throw new ServiceException("费用类型不存在");
        }

        BeanUtils.copyProperties(req, costType);
        costType.setUpdateBy(sysUser.getUsername());
        costType.setUpdateTime(LocalDateTime.now());
        costTypeMapper.updateById(costType);

        if (req.getUseDeliveryFlag() == 1) {
            costTypeDeliveryMapper.delete(q -> q.eq(FlatCostTypeDelivery::getCostTypeId, costType.getId()));
            for (Long deliveryId : req.getDeliveryIds()) {
                FlatCostTypeDelivery costTypeDelivery = new FlatCostTypeDelivery();
                costTypeDelivery.setCostTypeId(costType.getId());
                costTypeDelivery.setDeliveryId(deliveryId);
                costTypeDelivery.setCreateBy(sysUser.getUsername());
                costTypeDelivery.setCreateTime(LocalDateTime.now());
                costTypeDelivery.setDelStatus(0);
                costTypeDeliveryMapper.insert(costTypeDelivery);
            }
        }
    }

    /**
     * 删除费用类型
     */
    public void remove(Long id, SysUser sysUser) {
        costTypeMapper.update(q -> q
                .set(FlatCostType::getDelStatus, 1)
                .set(FlatCostType::getUpdateTime, LocalDateTime.now())
                .set(FlatCostType::getUpdateBy, sysUser.getUsername())
                .eq(FlatCostType::getId, id)
                .eq(FlatCostType::getDelStatus, 0)
        );
    }

    /**
     * 查询费用类型详情
     */
    public CostTypeDetailResp queryDetail(Long id) {
        // 查询费用类型基本信息
        FlatCostType costType = costTypeMapper.selectById(id);
        if (costType == null || costType.getDelStatus() == 1) {
            throw new ServiceException("费用类型不存在");
        }
        
        // 转换为详情响应对象
        CostTypeDetailResp resp = new CostTypeDetailResp();
        BeanUtils.copyProperties(costType, resp);
        
        // 查询支持的优惠券模板列表
        CouponTemplateQueryReq templateQueryReq = new CouponTemplateQueryReq();
        templateQueryReq.setTarget(2); // 自定义费用类型的优惠券
        templateQueryReq.setCostTypeId(id); // 只查询当前费用类型的优惠券模板
        List<CouponTemplateResp> templates = couponTemplateMapper.selectTemplateRespList(templateQueryReq);
        
        // 设置优惠券模板列表
        resp.setCouponTemplates(templates);
        
        // 计算优惠券模板的过期状态
        if (templates != null) {
            templates.forEach(CouponTemplateResp::calculateExpireStatus);
        }
        
        // 如果支持定点取货，查询定点取货记录列表
        if (costType.getUseDeliveryFlag() == 1) {
            // 查询关联的定点取货ID列表
            List<Long> deliveryIds = costTypeDeliveryMapper.selectList(q -> q
                    .select(FlatCostTypeDelivery::getDeliveryId)
                    .eq(FlatCostTypeDelivery::getCostTypeId, id)
                    .eq(FlatCostTypeDelivery::getDelStatus, 0)
            ).stream().map(FlatCostTypeDelivery::getDeliveryId).toList();
            
            if (!deliveryIds.isEmpty()) {
                // 查询定点取货记录详情
                List<FlatCostDelivery> deliveries = costDeliveryMapper.selectList(q -> q
                        .in(FlatCostDelivery::getId, deliveryIds)
                        .eq(FlatCostDelivery::getDelStatus, 0)
                        .orderByAsc(FlatCostDelivery::getDeliveryTime)
                );
                resp.setDeliveries(deliveries);
            }
        }
        
        return resp;
    }

    /**
     * 查询费用类型列表
     */
    public List<FlatCostType> queryList(CostTypeQueryReq req) {
        return costTypeMapper.selectCostTypeList(req);
    }

    /**
     * 分页查询费用类型
     */
    public TablePage<FlatCostType> queryPage(CostTypeQueryReq req) {
        return PageUtils.paginate(() -> costTypeMapper.selectCostTypeList(req));
    }
}
