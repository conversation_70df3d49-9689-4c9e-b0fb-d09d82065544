package com.flat.logic.service.flat;

import cn.hutool.core.bean.BeanUtil;
import com.flat.logic.dto.resp.flat.RoomWifiResp;
import com.flat.logic.entity.flat.FlatRoomWifi;
import jakarta.annotation.Resource;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.flat.RoomWifiAddReq;
import com.flat.logic.dto.req.flat.RoomWifiEditReq;
import com.flat.logic.dto.req.flat.RoomWifiQueryReq;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.flat.FlatRoomWifiMapper;
import com.flat.system.entity.SysUser;

import java.time.LocalDateTime;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class RoomWifiService {

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatRoomWifiMapper roomWifiMapper;

    public void add(RoomWifiAddReq req, SysUser user) {
        FlatRoom room = roomMapper.selectById(req.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        FlatRoomWifi roomWifi = new FlatRoomWifi();
        BeanUtils.copyProperties(req, roomWifi);
        roomWifi.setFlatId(room.getFlatId());
        roomWifi.setCreateBy(user.getUsername());
        roomWifi.setCreateTime(LocalDateTime.now());
        roomWifi.setDelStatus(0);
        roomWifiMapper.insert(roomWifi);
    }

    public void edit(RoomWifiEditReq req, SysUser user) {
        FlatRoomWifi roomWifi = roomWifiMapper.selectById(req.getId());
        if (roomWifi == null || roomWifi.getDelStatus() == 1) {
            throw new ServiceException("wifi不存在");
        }
        BeanUtil.copyProperties(req, roomWifi);
        roomWifi.setUpdateTime(LocalDateTime.now());
        roomWifi.setUpdateBy(user.getUsername());
        roomWifiMapper.updateById(roomWifi);
    }

    public TablePage<RoomWifiResp> queryPage(RoomWifiQueryReq req) {
        return PageUtils.paginate(() -> roomWifiMapper.selectResp(req));
    }

    public RoomWifiResp queryDetail(Long id) {
        FlatRoomWifi roomWifi = roomWifiMapper.selectById(id);
        if (roomWifi == null || roomWifi.getDelStatus() == 1) {
            throw new ServiceException("wifi不存在");
        }

        FlatRoom room = roomMapper.selectById(roomWifi.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        RoomWifiResp resp = new RoomWifiResp();
        BeanUtils.copyProperties(roomWifi, resp);
        resp.setRoomName(room.getName());
        return resp;
    }

    public void remove(Long id, SysUser user) {
        FlatRoomWifi roomWifi = roomWifiMapper.selectById(id);
        if (roomWifi == null || roomWifi.getDelStatus() == 1) {
            throw new ServiceException("wifi不存在");
        }
        
        roomWifi.setDelStatus(1);
        roomWifi.setUpdateTime(LocalDateTime.now());
        roomWifi.setUpdateBy(user.getUsername());
        roomWifiMapper.updateById(roomWifi);
    }
}
