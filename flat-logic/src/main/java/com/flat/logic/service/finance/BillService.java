package com.flat.logic.service.finance;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.finance.BillQueryReq;
import com.flat.logic.dto.req.finance.InvoiceApplyBillQueryReq;
import com.flat.logic.dto.resp.bill.BillExportResp;
import com.flat.logic.dto.resp.finance.BillResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatInvoice;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatInvoiceMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.system.entity.SysConfig;
import com.flat.system.entity.SysUser;
import com.flat.system.mapper.SysConfigMapper;
import com.flat.system.service.SysConfigService;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;

import cn.hutool.core.util.IdUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 公寓账单Service业务层处理
 */
@Service
@Slf4j
public class BillService {

    @Resource
    private FlatBillMapper flatBillMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private FlatCompanyMapper flatCompanyMapper;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private FlatInvoiceMapper flatInvoiceMapper;

    /**
     * 查询公寓账单列表
     */
    public TablePage<BillResp> queryRespPage(BillQueryReq queryReq) {
        List<SysConfig> configs = sysConfigMapper.selectList();

        return PageUtils.paginate(
                () -> flatBillMapper.selectRespList(queryReq),
                (e) -> {
                    if (ObjectUtils.isNotEmpty(e.getPayTarget())) {
                        for (SysConfig s : configs) {
                            if (s.getConfigKey().equals(e.getPayTarget())) {
                                e.setIcon(s.getConfigValue());
                            }
                        }
                    }
                    return e;
                }
        );
    }

    public List<BillExportResp> exportRespList(BillQueryReq queryReq) {
        List<BillResp> billResps = flatBillMapper.selectRespList(queryReq);
        List<BillExportResp> exportResps = new ArrayList<>();
        List<Long> invoiceIds = new ArrayList<>();
        for (BillResp billResp : billResps) {

            String content;
            if(billResp.getPayTarget().equals("water")) {
                content = "水费";
            } else if(billResp.getPayTarget().equals("electric")) {
                content = "电费";
            } else if(billResp.getPayTarget().equals("plan")) {
                if(billResp.getPayPlanType() == 1) {
                    content = "租金";
                } else if(billResp.getPayPlanType() == 2) {
                    content = "能耗费";
                } else if(billResp.getPayPlanType() == 3) {
                    content = "物业费";
                } else {
                    content = "其他";
                }
            } else if(billResp.getPayTarget().equals("clean")) {
                content = "保洁费";
            } else if(billResp.getPayTarget().equals("public")) {
                content = "公共设施使用费";
            } else if(billResp.getPayTarget().equals("cost")) {
                content = "个性化交易";
            } else {
                content = "其他";
            }

            BillExportResp exportResp = new BillExportResp();
            exportResp.setId(billResp.getId());
            if(billResp.getPayTime() != null) {
                exportResp.setDate(billResp.getPayTime().toLocalDate());
                exportResp.setYear(String.valueOf(billResp.getPayTime().getYear()));
                exportResp.setMonth(String.valueOf(billResp.getPayTime().getMonthValue()));
            }
            exportResp.setCompany(billResp.getCompanyName());
            exportResp.setBuildingNo(billResp.getRoomBlockNo());
            exportResp.setRoomNo(billResp.getRoomName());
            exportResp.setContent(content);
            exportResp.setAmount(billResp.getMoney());
            if(billResp.getPayChannel() != null) {
                exportResp.setPaymentMethod(billResp.getPayChannel() == 0 ? "支付宝" : billResp.getPayChannel() == 1 ? "微信" : "其他");
            }

            exportResp.setInvoiceStatus(billResp.getInvoiceStatus() == 1 ? "已开票" : "未开票");
            exportResp.setInvoiceId(billResp.getInvoiceId());
            exportResp.setRemark(billResp.getRemark());
            exportResp.setCollection(billResp.getMoney());

            if(billResp.getInvoiceStatus() == 1) {
                invoiceIds.add(billResp.getId());
            }
            
            exportResps.add(exportResp);
        }

        if(!invoiceIds.isEmpty()) {
            List<FlatInvoice> invoices = flatInvoiceMapper.selectByIds(invoiceIds);
            for(BillExportResp exportResp : exportResps) {
                for(FlatInvoice invoice : invoices) {
                    if(invoice.getId().equals(exportResp.getInvoiceId())) {
                        exportResp.setInvoiceTitle(invoice.getRiseName());
                    }
                }
            }
        }
        return exportResps;
    }

    /**
     * 查询可申请发票的账单列表
     */
    public TablePage<BillResp> queryInvoiceApplyBillRespPage(InvoiceApplyBillQueryReq queryReq, CusUser cusUser) {
        BillQueryReq billQueryReq = new BillQueryReq();
        BeanUtils.copyProperties(queryReq, billQueryReq);

        // 检查支付目标为水费、电费、租金和能耗费
        if(StringUtils.isNotBlank(queryReq.getPayTarget())) {
            if(!queryReq.getPayTarget().equals("water") && !queryReq.getPayTarget().equals("electric") && !queryReq.getPayTarget().equals("plan")) {
                throw new ServiceException("支付目标不支持");
            }
        }

        billQueryReq.setPayStatus(1);
        billQueryReq.setInvoiceStatus(0);
        billQueryReq.setCusId(cusUser.getUserId());
        // 支付结束时间为上个月最后一刻
        billQueryReq.setPayTimeEnd(LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).minusSeconds(1));
        return queryRespPage(billQueryReq);
    }

    public BillResp queryResp(Long billId) {
        FlatBill flatBill = flatBillMapper.selectById(billId);
        if (flatBill == null || flatBill.getDelStatus() == 1) {
            throw new ServiceException("账单不存在");
        }

        FlatCompany flatCompany = flatCompanyMapper.selectById(flatBill.getReceiveCompanyId());
        if (flatCompany == null || flatCompany.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        BillResp response = new BillResp();
        BeanUtils.copyProperties(flatBill, response);
        if (ObjectUtils.isNotEmpty(flatBill.getPayTarget())) {
            response.setIcon(sysConfigService.queryConfigByKey(flatBill.getPayTarget()));
        }
        response.setCompanyName(flatCompany.getCompanyName());

        return response;
    }

    public WxPayMpOrderResult pay(FlatBill bill, FlatCompany company, CusUser cusUser) {
        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        if(StringUtils.isEmpty(bill.getOrderNo())) {
            throw new ServiceException("账单号为空");
        }

        FlatPayLog payLog = new FlatPayLog();
        payLog.setBillId(bill.getId());
        payLog.setChannel(0);
        payLog.setOutTradeNo(IdUtil.fastSimpleUUID());
        payLog.setPayStatus(0);
        payLog.setCreateTime(LocalDateTime.now());
        payLog.setCreateBy(cusUser.getNickName());
        payLog.setDelStatus(0);
        payLogMapper.insert(payLog);

        WxPayMpOrderResult order;
        try {
            order = weChatPayService.createOrder(
                    bill.getPayTarget(),
                    company.getWxPayAppId(),
                    company.getWxPayMchId(),
                    cusUser.getWxOpenId(),
                    bill.getName(),
                    bill.getOrderNo(),
                    payLog.getOutTradeNo(),
                    bill.getMoney().multiply(BigDecimal.valueOf(100)).intValue()
            );
        } catch (Exception e) {
            throw new ServiceException("生成支付订单失败", e);
        }

        return order;
    }

    public WxPayMpOrderResult pay(Long billId, CusUser cusUser) {
        // 查找账单
        FlatBill bill = flatBillMapper.selectById(billId);
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单不存在");
        }

        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        FlatCompany company = flatCompanyMapper.selectById(bill.getReceiveCompanyId());
        if(company == null || company.getDelStatus() == 1) {
            throw new ServiceException("账单对应收款企业未找到");
        }

        return pay(bill, company, cusUser);
    }

    /**
     * 删除账单
     *
     * @param billId 账单ID
     * @param sysUser 系统用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long billId, SysUser sysUser) {
        // 判断账单是否存在
        FlatBill bill = flatBillMapper.selectById(billId);
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单不存在");
        }

        // 判断是否已支付
        if (bill.getPayStatus() == 1) {
            throw new ServiceException("已支付的账单不能删除");
        }

        // 执行逻辑删除
        bill.setDelStatus(1);
        bill.setUpdateBy(sysUser.getNickName());
        bill.setUpdateTime(LocalDateTime.now());
        flatBillMapper.updateById(bill);
    }
}
