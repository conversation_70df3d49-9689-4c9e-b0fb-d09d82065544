package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.make.CleanTypeQueryReq;
import com.flat.logic.dto.req.make.CleanTypeAddReq;
import com.flat.logic.dto.req.make.CleanTypeEditReq;
import com.flat.logic.entity.make.FlatCleanType;
import com.flat.logic.mapper.make.FlatCleanTypeMapper;
import com.flat.system.entity.SysUser;

import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 保洁类型管理Service
 */
@Service
@RequiredArgsConstructor
public class CleanTypeService {

    private final FlatCleanTypeMapper cleanTypeMapper;

    /**
     * 分页查询保洁类型列表
     */
    public TablePage<FlatCleanType> queryPage(CleanTypeQueryReq req) {
        return PageUtils.paginate(() -> cleanTypeMapper.selectList(q -> q
                .like(StringUtils.isNotBlank(req.getName()), FlatCleanType::getName, req.getName())
                .eq(FlatCleanType::getDelStatus, 0)
                .orderByDesc(FlatCleanType::getId)
        ));
    }

    /**
     * 获取保洁类型详情
     */
    public FlatCleanType queryDetail(Long id) {
        FlatCleanType cleanType = cleanTypeMapper.selectById(id);
        if (cleanType == null || cleanType.getDelStatus() == 1) {
            throw new ServiceException("保洁类型不存在");
        }

        return cleanType;
    }

    /**
     * 新增保洁类型
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(CleanTypeAddReq req, SysUser user) {
        // 检查名称是否重复
//        long count = cleanTypeMapper.selectCount(q -> q
//                .eq(FlatCleanType::getName, req.getName())
//                .eq(FlatCleanType::getDelStatus, 0));
//        if (count > 0) {
//            throw new ServiceException("保洁类型名称已存在");
//        }

        // 新增保洁类型
        FlatCleanType cleanType = new FlatCleanType();
        BeanUtils.copyProperties(req, cleanType);
        cleanType.setCreateBy(user.getUsername());
        cleanType.setCreateTime(LocalDateTime.now());
        cleanType.setDelStatus(0);
        cleanTypeMapper.insert(cleanType);
    }

    /**
     * 修改保洁类型
     */
    public void edit(CleanTypeEditReq req, SysUser user) {
        // 检查保洁类型是否存在
        FlatCleanType cleanType = queryDetail(req.getId());

        // 检查名称是否重复
//        long count = cleanTypeMapper.selectCount(q -> q
//                .eq(FlatCleanType::getName, req.getName())
//                .eq(FlatCleanType::getDelStatus, 0)
//                .ne(FlatCleanType::getId, req.getId()));
//        if (count > 0) {
//            throw new ServiceException("保洁类型名称已存在");
//        }

        // 修改保洁类型
        BeanUtils.copyProperties(req, cleanType);
        cleanType.setUpdateBy(user.getUsername());
        cleanType.setUpdateTime(LocalDateTime.now());
        cleanTypeMapper.updateById(cleanType);
    }

    /**
     * 删除保洁类型
     */
    public void remove(Long id, SysUser user) {
        cleanTypeMapper.update(q -> q
                .set(FlatCleanType::getDelStatus, 1)
                .set(FlatCleanType::getUpdateTime, LocalDateTime.now())
                .set(FlatCleanType::getUpdateBy, user.getUsername())
                .eq(FlatCleanType::getId, id)
                .eq(FlatCleanType::getDelStatus, 0)
        );
    }
}
