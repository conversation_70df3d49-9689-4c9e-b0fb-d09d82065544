package com.flat.logic.service.flat;

import com.flat.common.core.page.TablePage;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.flat.FlatAddReq;
import com.flat.logic.dto.req.flat.FlatEditReq;
import com.flat.logic.dto.req.flat.FlatQueryReq;
import com.flat.logic.entity.flat.Flat;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.mapper.flat.FlatMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.system.entity.SysUser;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公寓Service业务层处理
 */
@Service
public class FlatService {

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatMapper flatMapper;

    /**
     * 查询公寓
     *
     * @param id 公寓主键
     * @return 公寓
     */
    public Flat queryDetail(Long id) {
        Flat flat = flatMapper.selectById(id);
        if (flat == null || flat.getDelStatus() == 1) {
            throw new RuntimeException("公寓不存在");
        }
        return flat;
    }

    public List<Flat> queryList(FlatQueryReq req) {
        return flatMapper.selectBaseList(req);
    }

    /**
     * 查询公寓列表
     */
    public TablePage<Flat> queryPage(FlatQueryReq req) {
        return PageUtils.paginate(() -> flatMapper.selectBaseList(req));
    }

    /**
     * 新增公寓
     */
    public void addFlat(FlatAddReq req, SysUser sysUser) {
        Flat flat = flatMapper.selectFirst(q -> q
                .eq(Flat::getName, req.getName())
                .eq(Flat::getDelStatus, 0)
        );
        if(flat != null) {
            throw new RuntimeException("公寓名称已存在");
        }

        flat = new Flat();
        BeanUtils.copyProperties(req, flat);
        flat.setCreateBy(sysUser.getUsername());
        flat.setCreateTime(LocalDateTime.now());
        flatMapper.insert(flat);
    }

    /**
     * 修改公寓
     */
    public void editFlat(FlatEditReq req, SysUser sysUser) {
        Flat flat = flatMapper.selectById(req.getId());
        if (flat == null || flat.getDelStatus() == 1) {
            throw new RuntimeException("公寓不存在");
        }

        Flat existedFlat = flatMapper.selectFirst(q -> q
                .eq(Flat::getName, req.getName())
                .ne(Flat::getId, req.getId())
                .eq(Flat::getDelStatus, 0)
        );
        if(existedFlat != null) {
            throw new RuntimeException("公寓名称已存在");
        }

        BeanUtils.copyProperties(req, flat);
        flat.setUpdateBy(sysUser.getUsername());
        flat.setUpdateTime(LocalDateTime.now());
        flatMapper.updateById(flat);
    }

    /**
     * 删除公寓
     *
     * @param flatId 需要删除的公寓主键
     * @param sysUser 操作用户
     */
    public void removeFlat(Long flatId, SysUser sysUser) {
        // 判断该公寓是否还有房源
        long count = roomMapper.selectCount(q -> q
                .eq(FlatRoom::getDelStatus, 0)
                .eq(FlatRoom::getFlatId, flatId)
        );
        if (count > 0) {
            throw new RuntimeException("该公寓下还有房源");
        }
        
        flatMapper.update(q -> q
                .set(Flat::getDelStatus, 1)
                .set(Flat::getUpdateTime, LocalDateTime.now())
                .set(Flat::getUpdateBy, sysUser.getUsername())
                .eq(Flat::getId, flatId)
                .eq(Flat::getDelStatus, 0)
        );
    }

}
