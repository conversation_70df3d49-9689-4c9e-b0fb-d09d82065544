package com.flat.logic.service.lease;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.lease.LiveAddV1Req;
import com.flat.logic.dto.req.lease.LiveAddV2Req;
import com.flat.logic.dto.req.lease.LiveQueryReq;
import com.flat.logic.dto.resp.lease.LiveResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatContractProp;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatContractPropMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.model.ContractProp;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

@Service
public class LiveService {

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private FlatContractMapper contractMapper;

    @Resource
    private FlatContractPropMapper contractPropMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private ContractService contractService;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private MessageService messageService;

    @Transactional(rollbackFor = Exception.class)
    public void addLiveV1(LiveAddV1Req req, MaiUser maiUser) {
        FlatLease hostLease = leaseMapper.selectById(req.getLeaseId());
        if (hostLease == null || hostLease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(hostLease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁已作废，无法添加同住人");
        }

        if (hostLease.getStatus() != 2) {
            throw new ServiceException("租赁非履约状态，无法添加同住人");
        }

        // 检查合同版本是否匹配
        if(hostLease.getContractVersion() != 1) {
            throw new ServiceException("承租方合同版本不匹配");
        }

        if(liveMapper.selectCount(q -> q
                .eq(FlatLive::getLiveCusId, req.getLiveCusId())
                .eq(FlatLive::getDelStatus, 0)
                .in(FlatLive::getStatus, List.of(0, 2))
        ) > 0) {
            throw new ServiceException("该用户存在同住人记录");
        }

        FlatRoom room = roomMapper.selectById(hostLease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        FlatCompany company = companyMapper.selectById(hostLease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        CusUser hostCusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, hostLease.getCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (hostCusUser == null) {
            throw new ServiceException("承租人用户不存在");
        }

        CusUser liveCusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, req.getLiveCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (liveCusUser == null) {
            throw new ServiceException("同住人用户不存在");
        }
        
        // 检查实名认证
        if(liveCusUser.getIsReal() != 1) {
            throw new ServiceException("同住人未实名认证");
        }

        // 检查是否已经添加过该同住人
        if(liveMapper.selectCount((query) ->
                query.eq(FlatLive::getLeaseId, req.getLeaseId()).eq(FlatLive::getLiveCusId, req.getLiveCusId())
                        .eq(FlatLive::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("该同住人已添加");
        }

        // 检查承租方合同是否存在
        FlatContract hostContract = contractMapper.selectById(hostLease.getContractId());
        if(hostContract == null || hostContract.getDelStatus() == 1) {
            throw new ServiceException("承租方合同不存在");
        }

        // 添加新的同住人合同
        FlatContract liveContract = contractService.addContract(
                room, company, liveCusUser, 2,
                hostContract.getBeginTime().toLocalDate(), hostContract.getBeginTime().toLocalDate(), hostContract.getMonthCount(),
                hostContract.getMonthMoney(), hostContract.getDepositMoney(), hostContract.getMonthPropertyMoney(),
                hostContract.getLiveCount(), hostContract.getMonthCount() <= 3 ? 0 : 1, hostContract.getPayPeriod(),
                req.getContactName(), req.getContactPhoneNumber(), req.getDeliveryAddress(),
                null, req.getProps(),
                maiUser
        );

        FlatLive live = new FlatLive();
        live.setFlatId(hostLease.getFlatId());
        live.setRoomId(hostLease.getRoomId());
        live.setCompanyId(hostLease.getCompanyId());
        live.setLeaseId(req.getLeaseId());
        live.setButlerId(room.getButlerId());
        live.setPropertyId(room.getPropertyId());
        live.setHostCusId(hostLease.getCusId());
        live.setLiveCusId(req.getLiveCusId());
        live.setContractId(liveContract.getId());
        live.setContractVersion(2);
        live.setContractCreateTime(LocalDateTime.now());
        live.setContractBeginTime(liveContract.getBeginTime());
        live.setContractEndTime(liveContract.getEndTime());
        live.setStatus(0);
        live.setCreateBy(maiUser.getNickName());
        live.setCreateTime(LocalDateTime.now());
        live.setDelStatus(0);
        liveMapper.insert(live);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, hostLease.getId())
                .set(FlatContract::getLiveId, live.getId())
                .eq(FlatContract::getId, liveContract.getId())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void addLiveV2(LiveAddV2Req req, MaiUser maiUser) {
        FlatLease hostLease = leaseMapper.selectById(req.getLeaseId());
        if (hostLease == null || hostLease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(hostLease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁已作废，无法添加同住人");
        }

        if (hostLease.getStatus() != 2) {
            throw new ServiceException("租赁非履约状态，无法添加同住人");
        }

        // 检查合同版本是否匹配
        if(hostLease.getContractVersion() != 2) {
            throw new ServiceException("承租方合同版本不匹配");
        }

        if(liveMapper.selectCount(q -> q
                .eq(FlatLive::getLiveCusId, req.getLiveCusId())
                .eq(FlatLive::getDelStatus, 0)
                .in(FlatLive::getStatus, List.of(0, 2))
        ) > 0) {
            throw new ServiceException("该用户存在同住人记录");
        }

        FlatRoom room = roomMapper.selectById(hostLease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        FlatCompany company = companyMapper.selectById(hostLease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        CusUser hostCusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, hostLease.getCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (hostCusUser == null) {
            throw new ServiceException("承租人用户不存在");
        }

        CusUser liveCusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, req.getLiveCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (liveCusUser == null) {
            throw new ServiceException("同住人用户不存在");
        }
        
        // 检查实名认证
        if(liveCusUser.getIsReal() != 1) {
            throw new ServiceException("同住人未实名认证");
        }

        // 检查是否已经添加过该同住人
        if(liveMapper.selectCount((query) ->
                query.eq(FlatLive::getLeaseId, req.getLeaseId()).eq(FlatLive::getLiveCusId, req.getLiveCusId())
                        .eq(FlatLive::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("该同住人已添加");
        }

        // 检查承租方合同是否存在
        FlatContract hostContract = contractMapper.selectById(hostLease.getContractId());
        if(hostContract == null || hostContract.getDelStatus() == 1) {
            throw new ServiceException("承租方合同不存在");
        }

        FlatContractProp hostContractProp = contractPropMapper.selectFirst((query) -> query
            .eq(FlatContractProp::getContractId, hostContract.getId())
            .eq(FlatContractProp::getType, 1)
            .eq(FlatContractProp::getDelStatus, 0)
        );
        if(hostContractProp == null) {
            throw new ServiceException("承租方合同属性不存在");
        }

        List<ContractProp> hostContractProps = JSON.parseArray(hostContractProp.getPropsData(), ContractProp.class);

        // 添加新的同住人合同
        FlatContract liveContract = contractService.addContract(
            room, company, liveCusUser, 2, 
            hostContract.getDeliveryTime().toLocalDate(), hostContract.getBeginTime().toLocalDate(), hostContract.getMonthCount(), 
            hostContract.getMonthMoney(), hostContract.getDepositMoney(), hostContract.getMonthPropertyMoney(),
            hostContract.getLiveCount(), hostContract.getDurationType(), hostContract.getPayPeriod(), 
            req.getContactName(), req.getContactPhoneNumber(), req.getDeliveryAddress(), 
            null, hostContractProps,
            maiUser
        );

        FlatLive live = new FlatLive();
        live.setFlatId(hostLease.getFlatId());
        live.setRoomId(hostLease.getRoomId());
        live.setCompanyId(hostLease.getCompanyId());
        live.setLeaseId(req.getLeaseId());
        live.setButlerId(room.getButlerId());
        live.setPropertyId(room.getPropertyId());
        live.setHostCusId(hostLease.getCusId());
        live.setLiveCusId(req.getLiveCusId());
        live.setContractId(liveContract.getId());
        live.setContractVersion(2);
        live.setContractCreateTime(LocalDateTime.now());
        live.setContractBeginTime(liveContract.getBeginTime());
        live.setContractEndTime(liveContract.getEndTime());
        live.setStatus(0);
        live.setCreateBy(maiUser.getNickName());
        live.setCreateTime(LocalDateTime.now());
        live.setDelStatus(0);
        liveMapper.insert(live);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, hostLease.getId())
                .set(FlatContract::getLiveId, live.getId())
                .eq(FlatContract::getId, liveContract.getId())
        );
    }

    /**
     * 合同审核拒绝
     * @param contract
     * @param room
     * @param client 审核用户客户端[0=管理端;1=管家端]
     * @param userId
     * @param username
     */
    public void contractVerifyRejected(FlatContract contract, FlatRoom room, Integer client, Long userId, String username) {
        FlatLive live = liveMapper.selectById(contract.getLiveId());
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录未找到");
        }

        live.setStatus(1);
        live.setCancelTime(LocalDateTime.now());
        live.setUpdateBy(username);
        live.setUpdateTime(LocalDateTime.now());
        liveMapper.updateById(live);

        if(client == 0) {
            messageService.sysToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核驳回",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );
        } else if(client == 1) {
            messageService.butlerToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核驳回",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );
        }
    }

    /**
     * 合同审核通过
     * @param contract
     * @param room
     * @param client 审核用户客户端[0=管理端;1=管家端]
     * @param userId
     * @param username
     */
    public void contractVerifyPassed(FlatContract contract, FlatRoom room, Integer client, Long userId, String username) {
        FlatLive live = liveMapper.selectById(contract.getLiveId());
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录未找到");
        }

        if(client == 0) {
            messageService.sysToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核通过",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );

            messageService.sysToCus(
                    MessageService.TYPE_CONTRACT, userId, contract.getCusId(),
                    "合同已经生成，请尽快完成签署",
                    "/pages/user/agreement/info/index?id=" + contract.getId()
            );
        } else if(client == 1) {
            messageService.butlerToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核通过",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );

            messageService.butlerToCus(
                    MessageService.TYPE_CONTRACT, userId, contract.getCusId(),
                    "合同已经生成，请尽快完成签署",
                    "/pages/user/agreement/info/index?id=" + contract.getId()
            );
        }
    }

    /**
     * 用户拒绝签约
     * @param contract 合同
     * @param room 房源
     * @param cusUser 用户
     * @param operateTime 操作时间
     */
    public void signRejected(FlatContract contract, FlatRoom room, CusUser cusUser, LocalDateTime operateTime) {
        FlatLive live = liveMapper.selectById(contract.getLiveId());
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录未找到");
        }

        live.setStatus(1);
        live.setCancelTime(operateTime);
        live.setUpdateBy(cusUser.getNickName());
        live.setUpdateTime(LocalDateTime.now());
        liveMapper.updateById(live);

        messageService.cusToButler(
                MessageService.TYPE_CONTRACT, cusUser.getUserId(), contract.getButlerId(),
                "用户拒绝签署合同",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
        );
    }

    /**
     * 用户签署合同
     * @param contract 合同
     * @param room 房源
     * @param cusUser 用户
     * @param operateTime 操作时间
     */
    public void signPassed(FlatContract contract, FlatRoom room, CusUser cusUser, LocalDateTime operateTime) {
        FlatLive live = liveMapper.selectById(contract.getLiveId());
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录未找到");
        }

        live.setStatus(2);
        live.setContractSignTime(operateTime);
        live.setUpdateBy(cusUser.getNickName());
        live.setUpdateTime(LocalDateTime.now());
        liveMapper.updateById(live);

        messageService.cusToButler(
                MessageService.TYPE_CONTRACT, cusUser.getUserId(), contract.getButlerId(),
                "用户已签署合同",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
        );
    }

    public List<LiveResp> queryRespList(LiveQueryReq req) {
        return liveMapper.selectRespList(req);
    }

    public TablePage<LiveResp> queryRespPage(LiveQueryReq req) {
        return PageUtils.paginate(() -> liveMapper.selectRespList(req));
    }

    public LiveResp queryRespDetail(Long id) {
        FlatLive live = liveMapper.selectById(id);
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录不存在");
        }

        FlatRoom room = roomMapper.selectById(live.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        CusUser liveUser = cusUserMapper.selectById(live.getLiveCusId());
        if (liveUser == null || liveUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在");
        }

        LiveResp resp = new LiveResp();
        BeanUtils.copyProperties(live, resp);

        resp.setRoomName(room.getName());
        resp.setLiveCusAvatar(liveUser.getAvatar());
        resp.setLiveCusRealName(liveUser.getRealName());
        resp.setLiveCusPhoneNumber(liveUser.getPhoneNumber());
        resp.setLiveCusIdCard(liveUser.getIdCard());

        return resp;
    }

    // 删除同住人记录，只能删除状态为合同还未签署的或者已取消的记录
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long liveId, SysUser sysUser) {
        // 查询同住人记录
        FlatLive live = liveMapper.selectById(liveId);
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录不存在");
        }

        FlatContract contract = contractMapper.selectById(live.getContractId());
        if (contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("同住人合同记录未找到");
        }
        
        // 检查记录状态，只能删除合同待签署或已取消的记录
        if (live.getStatus() != 0 && live.getStatus() != 1) {
            throw new ServiceException("只能删除合同待签署或已取消的同住人记录");
        }
        
        // 执行逻辑删除操作
        live.setDelStatus(1);
        live.setUpdateBy(sysUser.getUsername());
        live.setUpdateTime(LocalDateTime.now());
        liveMapper.updateById(live);

        contract.setDelStatus(1);
        contract.setUpdateBy(sysUser.getUsername());
        contract.setUpdateTime(LocalDateTime.now());
        contractMapper.updateById(contract);

    }

    @Transactional(rollbackFor = Exception.class)
    public void exit(Long liveId, SysUser sysUser) {
        // 查询同住人记录
        FlatLive live = liveMapper.selectById(liveId);
        if (live == null || live.getDelStatus() == 1) {
            throw new ServiceException("同住人记录不存在");
        }

        FlatContract contract = contractMapper.selectById(live.getContractId());
        if (contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("同住人合同记录未找到");
        }

        // 同住已完成
        live.setStatus(3);
        live.setUpdateBy(sysUser.getUsername());
        live.setUpdateTime(LocalDateTime.now());
        live.setFinishTime(LocalDateTime.now());
        liveMapper.updateById(live);

        contract.setStatus(6);
        contract.setUpdateBy(sysUser.getUsername());
        contract.setUpdateTime(LocalDateTime.now());
        contract.setFinishTime(LocalDateTime.now());
        contractMapper.updateById(contract);
    }

}
