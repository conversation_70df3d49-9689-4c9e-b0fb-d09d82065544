package com.flat.logic.service.meter;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.entity.finance.FlatBalanceLog;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatSurrender;
import com.flat.logic.mapper.finance.FlatBalanceLogMapper;
import com.flat.logic.mapper.lease.FlatSurrenderMapper;
import com.flat.logic.model.meter.ElectricMeterCheck;
import com.flat.logic.model.meter.WaterMeterCheck;
import com.flat.logic.dto.req.meter.MeterLogQueryReq;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.meter.FlatMeterLog;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.meter.FlatMeterLogMapper;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class MeterService {

    @Resource
    private FlatMeterLogMapper meterLogMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatBalanceLogMapper balanceLogMapper;

    @Resource
    private FlatSurrenderMapper surrenderMapper;

    public List<WaterMeterCheck> exportWaterCheckList() {
        List<FlatRoom> rooms = roomMapper.selectList(q -> q
                .eq(FlatRoom::getDelStatus, 0)
        );

        List<FlatMeterLog> lastMeterLogs = meterLogMapper.selectLastWaterListGroupByRoom();

        return rooms.stream().map(room -> {
            FlatMeterLog lastLog = lastMeterLogs.stream().filter(meterLog -> meterLog.getRoomId().equals(room.getId())).findFirst().orElse(null);

            WaterMeterCheck resp = new WaterMeterCheck();
            resp.setRoomId(room.getId());
            resp.setRoomName(room.getName());
            resp.setWaterAccountNo(room.getWaterAccountNo());
            if (lastLog != null) {
                resp.setPreviousValue(lastLog.getEndValue());
                resp.setPreviousReadDate(lastLog.getRecordTime().toLocalDate());
            } else {
                resp.setPreviousValue(BigDecimal.ZERO);
            }
            return resp;
        }).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public void importWaterCheckList(List<WaterMeterCheck> checkList, SysUser sysUser) {
        for (WaterMeterCheck check : checkList) {
            if(StringUtils.isBlank(check.getWaterAccountNo())) {
                throw new ServiceException("水费户号不能为空，序号：" + check.getRoomId());
            }

            if(check.getCurrentValue() == null || check.getCurrentValue().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("本次抄表数不合法，序号：" + check.getRoomId());
            }

            if(check.getReadDate() == null) {
                throw new ServiceException("抄表时间不能为空，序号：" + check.getRoomId());
            }
        }

        String priceValue = sysConfigService.queryConfigByKey("WATER_PRICE");
        if(StringUtils.isBlank(priceValue)) {
            throw new ServiceException("水费单价未找到");
        }
        BigDecimal price = new BigDecimal(priceValue);
        Long batchNo = System.currentTimeMillis();
        LocalDateTime nowTime = LocalDateTime.now();

        List<FlatMeterLog> lastMeterLogs = meterLogMapper.selectLastWaterListGroupByRoom();

        for (WaterMeterCheck check : checkList) {
            FlatRoom room = roomMapper.selectById(check.getRoomId());
            if(room == null || room.getDelStatus() == 1) {
                throw new ServiceException("房源不存在，序号：" + check.getRoomId());
            }

            if(!room.getName().equalsIgnoreCase(check.getRoomName())) {
                throw new ServiceException("房源名称不匹配，序号：" + check.getRoomId());
            }

            if (!room.getWaterAccountNo().equalsIgnoreCase(check.getWaterAccountNo())) {
                throw new ServiceException("水费户号不匹配，序号：" + check.getRoomId());
            }

            FlatLease lease = leaseMapper.selectFirst(q -> q
                    .eq(FlatLease::getRoomId, room.getId())
                    .in(FlatLease::getStatus, List.of(2, 3, 5, 7))
                    .eq(FlatLease::getDelStatus, 0)
            );

            FlatMeterLog lastLog = lastMeterLogs.stream().filter(meterLog -> meterLog.getRoomId().equals(room.getId())).findFirst().orElse(null);
            if(lastLog != null && check.getCurrentValue().compareTo(lastLog.getEndValue()) < 0) {
                throw new ServiceException("本次抄表数小于上次抄表数，序号：" + check.getRoomId());
            }

            // 添加新的抄表记录
            FlatMeterLog newLog = new FlatMeterLog();
            newLog.setRoomId(room.getId());
            newLog.setRoomName(room.getName());
            newLog.setBatchNo(batchNo);
            newLog.setType(1);
            newLog.setAccountNo(room.getWaterAccountNo());
            if(lastLog != null) {
                newLog.setBeginValue(lastLog.getEndValue());
            } else {
                newLog.setBeginValue(BigDecimal.ZERO);
            }
            newLog.setEndValue(check.getCurrentValue());
            newLog.setPrice(price);
            newLog.setRecordTime(check.getReadDate().atStartOfDay());
            newLog.setCreateBy(sysUser.getUsername());
            newLog.setCreateTime(nowTime);
            newLog.setDelStatus(0);
            meterLogMapper.insert(newLog);

            room.setWaterMeterValue(check.getCurrentValue());
            room.setWaterMeterLastTime(check.getReadDate().atStartOfDay());
            room.setUpdateBy(sysUser.getUsername());
            room.setUpdateTime(nowTime);
            roomMapper.updateById(room);

            // 计算费用并扣除用户余额
            if(lease == null) { // 没有租赁记录
                continue;
            }

             if(lease.getStatus() == 7) { // 属于已申请退租，需要继续判断退租时是否已经确认了退款款项，如果没有确认可以继续扣款，如果已经确认则不再扣款
                 FlatSurrender surrender = surrenderMapper.selectById(lease.getSurrenderId());
                 if(surrender.getStatus() != 0) { // 不可以继续扣费
                     continue;
                 }
             }

            // 判断是否是入住后第一次扣费，如果是第一次则使用入住时登记的水表读数进行计算，否则使用上次抄表记录的读数进行计算
            Long count = balanceLogMapper.selectCount(q -> q
                    .eq(FlatBalanceLog::getLeaseId, lease.getId())
                    .eq(FlatBalanceLog::getAccountType, 1)
                    .eq(FlatBalanceLog::getChangeType, 1)
                    .eq(FlatBalanceLog::getDelStatus, 0)
            );

            BigDecimal realBeginValue = count == 0 ? (lease.getCheckInWaterValue() == null ? BigDecimal.ZERO : lease.getCheckInWaterValue()) : newLog.getBeginValue();
            BigDecimal realEndValue = newLog.getEndValue();

            BigDecimal money = realEndValue.subtract(realBeginValue).multiply(price);
            if(money.compareTo(BigDecimal.ZERO) <= 0) { // 如果最后算出来的值小于等于0，后面就不添加余额变更记录和更新余额了
                continue;
            }

            // 添加余额变更记录
            FlatBalanceLog balanceLog = new FlatBalanceLog();
            balanceLog.setRoomId(room.getId());
            balanceLog.setCusId(lease.getCusId());
            balanceLog.setLeaseId(lease.getId());
            balanceLog.setAccountNo(room.getWaterAccountNo());
            balanceLog.setAccountType(1);
            balanceLog.setChangeType(1);
            balanceLog.setOldMoney(lease.getWaterAccountBalance());
            balanceLog.setNewMoney(lease.getWaterAccountBalance().subtract(money));
            balanceLog.setAmount(money);
            balanceLog.setMeterLogId(newLog.getId());
            balanceLog.setMeterBeginValue(realBeginValue);
            balanceLog.setMeterEndValue(realEndValue);
            balanceLog.setMeterAmount(realEndValue.subtract(realBeginValue));
            balanceLog.setMeterPrice(price);
            balanceLog.setCreateBy(sysUser.getUsername());
            balanceLog.setCreateTime(LocalDateTime.now());
            balanceLog.setDelStatus(0);
            balanceLogMapper.insert(balanceLog);

            // 更新账户余额
            lease.setWaterAccountBalance(lease.getWaterAccountBalance().subtract(money));
            lease.setUpdateBy(sysUser.getUsername());
            lease.setUpdateTime(nowTime);
            leaseMapper.updateById(lease);
        }
    }

    public List<ElectricMeterCheck> exportElectricCheckList() {
        List<FlatRoom> rooms = roomMapper.selectList(q -> q
                .eq(FlatRoom::getDelStatus, 0)
        );

        List<FlatMeterLog> lastMeterLogs = meterLogMapper.selectLastElectricListGroupByRoom();

        return rooms.stream().map(room -> {
            FlatMeterLog lastLog = lastMeterLogs.stream().filter(meterLog -> meterLog.getRoomId().equals(room.getId())).findFirst().orElse(null);

            ElectricMeterCheck resp = new ElectricMeterCheck();
            resp.setRoomId(room.getId());
            resp.setRoomName(room.getName());
            resp.setElectricAccountNo(room.getElectricAccountNo());
            if (lastLog != null) {
                resp.setPreviousValue(lastLog.getEndValue());
                resp.setPreviousReadDate(lastLog.getRecordTime().toLocalDate());
            } else {
                resp.setPreviousValue(BigDecimal.ZERO);
            }
            return resp;
        }).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public void importElectricCheckList(List<ElectricMeterCheck> checkList, SysUser sysUser) {
        for (ElectricMeterCheck check : checkList) {
            if(StringUtils.isBlank(check.getElectricAccountNo())) {
                throw new ServiceException("电费户号不能为空，序号：" + check.getRoomId());
            }

            if(check.getCurrentValue() == null || check.getCurrentValue().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("本次抄表数不合法，序号：" + check.getRoomId());
            }

            if(check.getReadDate() == null) {
                throw new ServiceException("抄表时间不能为空，序号：" + check.getRoomId());
            }
        }

        String priceValue = sysConfigService.queryConfigByKey("ELECTRICITY_PRICE");
        if(StringUtils.isBlank(priceValue)) {
            throw new ServiceException("电费单价未找到");
        }
        BigDecimal price = new BigDecimal(priceValue);
        Long batchNo = System.currentTimeMillis();
        LocalDateTime nowTime = LocalDateTime.now();

        List<FlatMeterLog> lastMeterLogs = meterLogMapper.selectLastElectricListGroupByRoom();

        for (ElectricMeterCheck check : checkList) {
            FlatRoom room = roomMapper.selectById(check.getRoomId());
            if(room == null || room.getDelStatus() == 1) {
                throw new ServiceException("房源不存在，序号：" + check.getRoomId());
            }

            if(!room.getName().equalsIgnoreCase(check.getRoomName())) {
                throw new ServiceException("房源名称不匹配，序号：" + check.getRoomId());
            }

            if(!room.getElectricAccountNo().equalsIgnoreCase(check.getElectricAccountNo())) {
                throw new ServiceException("电费户号不匹配，序号：" + check.getRoomId());
            }

            FlatLease lease = leaseMapper.selectFirst(q -> q
                    .eq(FlatLease::getRoomId, room.getId())
                    .in(FlatLease::getStatus, List.of(2, 3, 5, 7))
                    .eq(FlatLease::getDelStatus, 0)
            );

            FlatMeterLog lastLog = lastMeterLogs.stream().filter(meterLog -> meterLog.getRoomId().equals(room.getId())).findFirst().orElse(null);
            if(lastLog != null && check.getCurrentValue().compareTo(lastLog.getEndValue()) < 0) {
                throw new ServiceException("本次抄表数小于上次抄表数，序号：" + check.getRoomId());
            }

            // 添加新的抄表记录
            FlatMeterLog newLog = new FlatMeterLog();
            newLog.setRoomId(room.getId());
            newLog.setRoomName(room.getName());
            newLog.setBatchNo(batchNo);
            newLog.setType(2);
            newLog.setAccountNo(room.getElectricAccountNo());
            if(lastLog != null) {
                newLog.setBeginValue(lastLog.getEndValue());
            } else {
                newLog.setBeginValue(BigDecimal.ZERO);
            }
            newLog.setEndValue(check.getCurrentValue());
            newLog.setPrice(price);
            newLog.setRecordTime(check.getReadDate().atStartOfDay());
            newLog.setCreateBy(sysUser.getUsername());
            newLog.setCreateTime(nowTime);
            newLog.setDelStatus(0);
            meterLogMapper.insert(newLog);

            room.setElectricMeterValue(check.getCurrentValue());
            room.setElectricMeterLastTime(check.getReadDate().atStartOfDay());
            room.setUpdateBy(sysUser.getUsername());
            room.setUpdateTime(nowTime);
            roomMapper.updateById(room);

            // 计算费用并扣除用户余额
            if(lease == null) { // 没有租赁记录
                continue;
            }

            if(lease.getStatus() == 7) { // 属于已申请退租，需要继续判断退租时是否已经确认了退款款项，如果没有确认可以继续扣款，如果已经确认则不再扣款
                FlatSurrender surrender = surrenderMapper.selectById(lease.getSurrenderId());
                if(surrender.getStatus() != 0) { // 不可以继续扣费
                    continue;
                }
            }

            // 判断是否是入住后第一次扣费，如果是第一次则使用入住时登记的电表读数进行计算，否则使用上次抄表记录的读数进行计算
            Long count = balanceLogMapper.selectCount(q -> q
                    .eq(FlatBalanceLog::getLeaseId, lease.getId())
                    .eq(FlatBalanceLog::getAccountType, 2)
                    .eq(FlatBalanceLog::getChangeType, 1)
                    .eq(FlatBalanceLog::getDelStatus, 0)
            );

            BigDecimal realBeginValue = count == 0 ? (lease.getCheckInElectricValue() == null ? BigDecimal.ZERO : lease.getCheckInElectricValue()) : newLog.getBeginValue();
            BigDecimal realEndValue = newLog.getEndValue();

            BigDecimal money = realEndValue.subtract(realBeginValue).multiply(price);
            if(money.compareTo(BigDecimal.ZERO) <= 0) { // 如果最后算出来的值小于等于0，后面就不添加余额变更记录和更新余额了
                continue;
            }

            // 添加余额变更记录
            FlatBalanceLog balanceLog = new FlatBalanceLog();
            balanceLog.setRoomId(room.getId());
            balanceLog.setCusId(lease.getCusId());
            balanceLog.setLeaseId(lease.getId());
            balanceLog.setAccountNo(room.getElectricAccountNo());
            balanceLog.setAccountType(2);
            balanceLog.setChangeType(1);
            balanceLog.setOldMoney(lease.getElectricAccountBalance());
            balanceLog.setNewMoney(lease.getElectricAccountBalance().subtract(money));
            balanceLog.setAmount(money);
            balanceLog.setMeterLogId(newLog.getId());
            balanceLog.setMeterBeginValue(realBeginValue);
            balanceLog.setMeterEndValue(realEndValue);
            balanceLog.setMeterAmount(realEndValue.subtract(realBeginValue));
            balanceLog.setMeterPrice(price);
            balanceLog.setCreateBy(sysUser.getUsername());
            balanceLog.setCreateTime(LocalDateTime.now());
            balanceLog.setDelStatus(0);
            balanceLogMapper.insert(balanceLog);

            // 更新账户余额
            lease.setElectricAccountBalance(lease.getElectricAccountBalance().subtract(money));
            lease.setUpdateBy(sysUser.getUsername());
            lease.setUpdateTime(nowTime);
            leaseMapper.updateById(lease);
        }
    }

    public TablePage<FlatMeterLog> queryPage(MeterLogQueryReq req) {
        return PageUtils.paginate(() -> meterLogMapper.selectBaseList(req));
    }

    public FlatMeterLog queryDetail(Long id) {
        
        FlatMeterLog meterLog = meterLogMapper.selectById(id);
        if (meterLog == null || meterLog.getDelStatus() == 1) {
            throw new ServiceException("抄表记录不存在");
        }

        return meterLog;
    
    }

    public void removeLog(Long meterLogId, SysUser sysUser) {
        // 查找抄表记录
        FlatMeterLog meterLog = meterLogMapper.selectById(meterLogId);
        if(meterLog == null || meterLog.getDelStatus() == 1) {
            throw new ServiceException("抄表记录不存在");
        }

        FlatRoom room = roomMapper.selectById(meterLog.getRoomId());
        if(room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 查找余额变更记录
        FlatBalanceLog balanceLog = balanceLogMapper.selectOne(q -> q
                .eq(FlatBalanceLog::getMeterLogId, meterLogId)
                .eq(FlatBalanceLog::getDelStatus, 0)
        );

        if(balanceLog == null) { // 没有涉及到余额变更记录和费用扣除，只删除抄表记录
            meterLog.setDelStatus(1);
            meterLog.setUpdateBy(sysUser.getUsername());
            meterLog.setUpdateTime(LocalDateTime.now());
            meterLogMapper.updateById(meterLog);
            
        } else {

            // 查找租赁记录
            FlatLease lease = leaseMapper.selectById(balanceLog.getLeaseId());
            if(lease == null || lease.getDelStatus() == 1) {
                throw new ServiceException("租赁记录不存在");
            }

            if(lease.getStatus() == 7) { // 属于已申请退租，需要继续判断退租时是否已经确认了退款款项，如果没有确认可以继续处理金额，如果已经确认则不能处理金额
                FlatSurrender surrender = surrenderMapper.selectById(lease.getSurrenderId());
                if(surrender.getStatus() != 0) { // 不可以继续处理金额
                    throw new ServiceException("退租已确认退款，不可再处理金额");
                }
            }

            meterLog.setDelStatus(1);
            meterLog.setUpdateBy(sysUser.getUsername());
            meterLog.setUpdateTime(LocalDateTime.now());
            meterLogMapper.updateById(meterLog);

            balanceLog.setDelStatus(1);
            balanceLog.setUpdateBy(sysUser.getUsername());
            balanceLog.setUpdateTime(LocalDateTime.now());
            balanceLogMapper.updateById(balanceLog);

            if(meterLog.getType() == 1) { // 水费
                lease.setWaterAccountBalance(lease.getWaterAccountBalance().add(balanceLog.getAmount()));
                lease.setUpdateBy(sysUser.getUsername());
                lease.setUpdateTime(LocalDateTime.now());
                leaseMapper.updateById(lease);
            } else if(meterLog.getType() == 2) { // 电费
                lease.setElectricAccountBalance(lease.getElectricAccountBalance().add(balanceLog.getAmount()));
                lease.setUpdateBy(sysUser.getUsername());
                lease.setUpdateTime(LocalDateTime.now());
                leaseMapper.updateById(lease);
            }
        }

        // 使用最新的日志记录更新房源最后的抄表值和抄表时间
        FlatMeterLog lastMeterLog = meterLogMapper.selectFirst(q -> q
            .eq(FlatMeterLog::getRoomId, meterLog.getRoomId())
            .eq(FlatMeterLog::getDelStatus, 0)
            .eq(FlatMeterLog::getType, meterLog.getType())
            .orderByDesc(FlatMeterLog::getCreateTime)
        );
        if(meterLog.getType() == 1) { // 水费
            room.setWaterMeterValue(lastMeterLog.getEndValue());
            room.setWaterMeterLastTime(lastMeterLog.getRecordTime());
            room.setUpdateBy(sysUser.getUsername());
            room.setUpdateTime(LocalDateTime.now());
            roomMapper.updateById(room);
        } else if(meterLog.getType() == 2) { // 电费
            room.setElectricMeterValue(lastMeterLog.getEndValue());
            room.setElectricMeterLastTime(lastMeterLog.getRecordTime());
            room.setUpdateBy(sysUser.getUsername());
            room.setUpdateTime(LocalDateTime.now());
            roomMapper.updateById(room);
        }
    }
}
