package com.flat.logic.service.cloud;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.exception.ServiceException;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.esign.comm.ESignFileBean;
import com.flat.logic.esign.comm.ESignHttpHelper;
import com.flat.logic.esign.enums.ESignHeaderConstant;
import com.flat.logic.esign.enums.ESignRequestType;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.model.ContractProp;
import com.flat.logic.model.SignFlowResult;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ESignCloudService {

    @Value("${eSign.appId}")
    private String appId;

    @Value("${eSign.appSecret}")
    private String appSecret;

    @Value("${eSign.apiUrl}")
    private String apiUrl;

    @Value("${eSign.orgId}")
    private String orgId;

    @Value("${eSign.notifyUrl}")
    private String notifyUrl;

    @Resource
    private FlatCompanyMapper flatCompanyMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    private String createFileByDocTemplate(String name, String docTemplateId, List<ContractProp> props) {
        String apiPath = "/v3/files/create-by-doc-template";

        String jsonParam = JSON.toJSONString(Map.of(
                "docTemplateId", docTemplateId,
                "fileName", name,
                "components", props.stream().map((prop) -> Map.of("componentKey", prop.getKey(), "componentValue", prop.getValue())).collect(Collectors.toList())
        ));
        ESignRequestType requestType = ESignRequestType.POST;
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(appId, appSecret, requestType, apiPath, jsonParam);
        JSONObject response = ESignHttpHelper.doCommHttp(apiUrl, apiPath, requestType, jsonParam, header);

        return response.getString("fileId");
    }

    // 获取个人用户文件签署区域位置
    public List<JSONObject> requestDocTemplateSignAreaPositions(String docTemplateId) {
        String apiPath = "/v3/doc-templates/" + docTemplateId;

        //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
        String jsonParam = "{}";
        //请求方法
        ESignRequestType requestType = ESignRequestType.GET;
        //生成签名鉴权方式的的header
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(appId, appSecret, requestType, apiPath, jsonParam);
        //发起接口请求
        JSONObject response = ESignHttpHelper.doCommHttp(apiUrl, apiPath, requestType, jsonParam, header);

        return response.getJSONArray("components")
                .stream()
                .map(JSONObject.class::cast)
                .filter((json) -> json.getIntValue("componentType") == 6)
                .map((json) -> json.getJSONObject("componentPosition"))
                .collect(Collectors.toList());
    }

    private String createSignFlowByFile(String name, String docTemplateId, String fileId, String sealId, String trueName, String idCard, String phone) {
        String apiPath = "/v3/sign-flow/create-by-file";

        List<JSONObject> signAreaPositions = requestDocTemplateSignAreaPositions(docTemplateId);
        if (signAreaPositions.isEmpty()) {
            throw new ServiceException("文件不存在签署区域");
        }

        List<Map<String, Object>> signers = new ArrayList<>();
        for (JSONObject signAreaPosition : signAreaPositions) {
            // 个人签署方位置信息
            String pageNum = signAreaPosition.getString("componentPageNum");
            BigDecimal userSignX = signAreaPosition.getBigDecimal("componentPositionX");
            BigDecimal userSignY = signAreaPosition.getBigDecimal("componentPositionY");

            // 企业签署方位置信息
            BigDecimal companySignX = userSignX.subtract(BigDecimal.valueOf(230));

            signers.add(Map.of(//企业签署方
                    "signerType", 1,
                    "signFields", List.of(Map.of(
                            "fileId", fileId,
                            "normalSignFieldConfig", Map.of(
                                    "autoSign", true,
                                    "assignedSealId", sealId,
                                    "signFieldStyle", 1,
                                    "signFieldPosition", Map.of("positionPage", pageNum, "positionX", companySignX.floatValue(), "positionY", userSignY.floatValue())
                            )
                    ))
            ));

            signers.add(Map.of(//个人签署方
                    "signerType", 0,
                    "psnSignerInfo", Map.of(
                            "psnAccount", phone,
                            "psnInfo", Map.of(
                                    "psnName", trueName,
                                    "psnIDCardNum", idCard,
                                    "psnIDCardType", "CRED_PSN_CH_IDCARD"
                            )
                    ),
                    "signFields", List.of(Map.of(
                            "fileId", fileId,
                            "normalSignFieldConfig", Map.of(
                                    "signFieldStyle", 1,
                                    "signFieldPosition", Map.of("positionPage", pageNum, "positionX", userSignX.floatValue(), "positionY", userSignY.floatValue())
                            )
                    ))
            ));
        }

        Map<String, Object> reqMap = Map.of(
                "docs", List.of(Map.of("fileId", fileId)),
                "signFlowConfig", Map.of(
                        "signFlowTitle", name,
                        "autoStart", true,
                        "autoFinish", true,
                        "notifyUrl", notifyUrl,
                        "signConfig", Map.of("availableSignClientTypes", "1")
                ),
                "signers", signers
        );

        String jsonParam = JSON.toJSONString(reqMap);
        ESignRequestType requestType = ESignRequestType.POST;
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(appId, appSecret, requestType, apiPath, jsonParam);
        JSONObject response = ESignHttpHelper.doCommHttp(apiUrl, apiPath, requestType, jsonParam, header);
        return response.getString("signFlowId");
    }

    public String uploadFileByUrl(String fileUrl) {
        String urlPath = URLUtil.getPath(fileUrl);
        String fileName = FileUtil.getName(urlPath);
        File file;
        try {
            file = File.createTempFile("esign", fileName);
        } catch (IOException e) {
            throw new ServiceException("签章时需要的文件无法存储", e);
        }

        try {
            HttpUtil.downloadFile(fileUrl, file);
        } catch (Exception e) {
            boolean ignore = file.delete();
            throw new ServiceException("签章时需要的文件获取失败", e);
        }

        try {
            return uploadFile(file.getAbsolutePath());
        } finally {
            boolean ignore = file.delete();
        }
    }

    public String uploadFile(String filePath) {
        //获取文件id以及文件上传地址
        String apiPath = "/v3/files/file-upload-url";
        ESignFileBean esignFileBean = new ESignFileBean(filePath);
        String jsonParam = "{\n" +
                "    \"contentMd5\": \"" + esignFileBean.getFileContentMD5() + "\",\n" +
                "    \"fileName\":\"" + esignFileBean.getFileName() + "\"," +
                "    \"fileSize\": " + esignFileBean.getFileSize() + ",\n" +
                "    \"convertToPDF\": false,\n" +
                "    \"contentType\": \"" + ESignHeaderConstant.CONTENT_TYPE_STREAM.VALUE() + "\"\n" +
                "}";

        //生成签名鉴权方式的的header
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(appId, appSecret, ESignRequestType.POST, apiPath, jsonParam);
        //发起接口请求
        JSONObject addrJson = ESignHttpHelper.doCommHttp(apiUrl, apiPath, ESignRequestType.POST, jsonParam, header);
        String fileId = addrJson.getString("fileId");
        String fileUploadUrl = addrJson.getString("fileUploadUrl");

        //请求方法
        ESignHttpHelper.doUploadHttp(fileUploadUrl, ESignRequestType.PUT, esignFileBean.getFileBytes(), esignFileBean.getFileContentMD5(), ESignHeaderConstant.CONTENT_TYPE_STREAM.VALUE());

        return fileId;
    }

    private String requestUserSignUrl(String flowId, String phone) {
        String apiPath = "/v3/sign-flow/" + flowId + "/sign-url";

        Map<String, Object> reqMap = Map.of(
                "signFlowId", flowId,
                "needLogin", false,
                "urlType", 2,
                "operator", Map.of("psnAccount", phone),
                "redirectConfig", Map.of("redirectUrl", "wechat://back")
        );

        String jsonParam = JSON.toJSONString(reqMap);
        ESignRequestType requestType = ESignRequestType.POST;
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(appId, appSecret, requestType, apiPath, jsonParam);
        JSONObject response = ESignHttpHelper.doCommHttp(apiUrl, apiPath, requestType, jsonParam, header);
        return response.getString("url");
    }

    public SignFlowResult createSignFlow(FlatRoom room, FlatContract contract, List<ContractProp> props) {
        FlatCompany company = flatCompanyMapper.selectById(contract.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业信息不存在");
        }

        CusUser cusUser = cusUserMapper.selectById(contract.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户信息不存在");
        }

        String fileId = createFileByDocTemplate(
            room.getKind() == 0 ? "公寓房屋租赁合同" : "商铺房屋租赁合同",
            room.getKind() == 0 ? company.getESignHouseDocTemplateId() : company.getESignShopDocTemplateId(),
            props
        );

        String flowId = createSignFlowByFile(
                room.getKind() == 0 ? "公寓房屋租赁合同" : "商铺房屋租赁合同",
                room.getKind() == 0 ? company.getESignHouseDocTemplateId() : company.getESignShopDocTemplateId(),
                fileId,
                company.getESignSealId(),
                cusUser.getRealName(),
                cusUser.getIdCard(),
                cusUser.getPhoneNumber()
        );

        String userSignUrl = requestUserSignUrl(flowId, cusUser.getPhoneNumber());

        SignFlowResult result = new SignFlowResult();
        result.setFlowId(flowId);
        result.setUserSignUrl(userSignUrl);
        return result;
    }

    public String requestSignFileUrl(String flowId) {
        String apiPath = "/v3/sign-flow/" + flowId + "/file-download-url";

        String jsonParam = "{}";
        ESignRequestType requestType = ESignRequestType.GET;
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(appId, appSecret, requestType, apiPath, jsonParam);
        JSONObject response = ESignHttpHelper.doCommHttp(apiUrl, apiPath, requestType, jsonParam, header);

        JSONArray files = response.getJSONArray("files");
        return files.getJSONObject(0).getString("downloadUrl");
    }

}
