package com.flat.logic.service.account;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.account.StaffAddReq;
import com.flat.logic.dto.req.account.StaffEditReq;
import com.flat.logic.dto.req.account.StaffQueryReq;
import com.flat.logic.entity.account.FlatStaff;
import com.flat.logic.mapper.account.FlatStaffMapper;
import com.flat.system.entity.SysUser;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公寓员工（保洁/保安/维修员等）Service业务层处理
 */
@Service
public class StaffService {

    @Resource
    private FlatStaffMapper staffMapper;

    /**
     * 添加员工
     */
    public void add(StaffAddReq req, SysUser sysUser) {
        // 判断手机号是否重复
        if (staffMapper.selectCount(q -> q
                .eq(FlatStaff::getPhoneNumber, req.getPhoneNumber())
                .eq(FlatStaff::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("手机号已存在");
        }

        FlatStaff staff = new FlatStaff();
        BeanUtils.copyProperties(req, staff);
        staff.setCreateBy(sysUser.getUsername());
        staff.setCreateTime(LocalDateTime.now());
        staff.setDelStatus(0);
        staffMapper.insert(staff);
    }

    /**
     * 修改员工
     */
    public void edit(StaffEditReq req, SysUser sysUser) {
        // 判断手机号是否重复
        if (staffMapper.selectCount(q -> q
                .eq(FlatStaff::getPhoneNumber, req.getPhoneNumber())
                .ne(FlatStaff::getId, req.getId())
                .eq(FlatStaff::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("手机号已存在");
        }

        FlatStaff staff = staffMapper.selectById(req.getId());
        if (staff == null || staff.getDelStatus() == 1) {
            throw new ServiceException("员工不存在");
        }

        BeanUtils.copyProperties(req, staff);
        staff.setUpdateBy(sysUser.getUsername());
        staff.setUpdateTime(LocalDateTime.now());
        staffMapper.updateById(staff);
    }

    /**
     * 删除员工
     */
    public void remove(Long id, SysUser sysUser) {
        staffMapper.update(q -> q
                .set(FlatStaff::getDelStatus, 1)
                .set(FlatStaff::getUpdateTime, LocalDateTime.now())
                .set(FlatStaff::getUpdateBy, sysUser.getUsername())
                .eq(FlatStaff::getId, id)
                .eq(FlatStaff::getDelStatus, 0)
        );
    }

    /**
     * 查询员工详情
     */
    public FlatStaff queryDetail(Long id) {
        FlatStaff staff = staffMapper.selectById(id);
        if (staff == null || staff.getDelStatus() == 1) {
            throw new ServiceException("员工不存在");
        }
        return staff;
    }

    /**
     * 查询员工列表
     */
    public List<FlatStaff> queryList(StaffQueryReq req) {
        return staffMapper.selectList(q -> q
                .like(StringUtils.isNotBlank(req.getName()), FlatStaff::getName, req.getName())
                .eq(req.getType() != null, FlatStaff::getType, req.getType())
                .like(StringUtils.isNotBlank(req.getPhoneNumber()), FlatStaff::getPhoneNumber, req.getPhoneNumber())
                .ge(req.getBeginCreateTime() != null, FlatStaff::getCreateTime, req.getBeginCreateTime())
                .le(req.getEndCreateTime() != null, FlatStaff::getCreateTime, req.getEndCreateTime())
                .eq(FlatStaff::getDelStatus, 0)
                .orderByDesc(FlatStaff::getCreateTime)
        );
    }

    /**
     * 分页查询员工
     */
    public TablePage<FlatStaff> queryPage(StaffQueryReq req) {
        return PageUtils.paginate(() -> staffMapper.selectList(q -> q
                .like(StringUtils.isNotBlank(req.getName()), FlatStaff::getName, req.getName())
                .eq(req.getType() != null, FlatStaff::getType, req.getType())
                .like(StringUtils.isNotBlank(req.getPhoneNumber()), FlatStaff::getPhoneNumber, req.getPhoneNumber())
                .ge(req.getBeginCreateTime() != null, FlatStaff::getCreateTime, req.getBeginCreateTime())
                .le(req.getEndCreateTime() != null, FlatStaff::getCreateTime, req.getEndCreateTime())
                .eq(FlatStaff::getDelStatus, 0)
                .orderByDesc(FlatStaff::getCreateTime)
        ));
    }
}
