package com.flat.logic.service.lease;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.service.cloud.OSSService;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.common.utils.spring.SpringUtils;
import com.flat.logic.dto.req.lease.LeaseAddReq;
import com.flat.logic.dto.req.lease.LeaseQueryReq;
import com.flat.logic.dto.req.lease.LeaseRenewV1Req;
import com.flat.logic.dto.req.lease.LeaseRenewV2Req;
import com.flat.logic.dto.req.lease.LeaseSubletV1Req;
import com.flat.logic.dto.req.lease.LeaseSubletV2Req;
import com.flat.logic.dto.req.lease.LiveQueryReq;
import com.flat.logic.dto.req.lease.SurrenderApplyReq;
import com.flat.logic.dto.resp.lease.LeaseDetailResp;
import com.flat.logic.dto.resp.lease.LeaseExportResp;
import com.flat.logic.dto.resp.lease.LeaseListResp;
import com.flat.logic.dto.resp.lease.LiveResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatContractProp;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.entity.lease.FlatPayPlan;
import com.flat.logic.entity.lease.FlatSurrender;
import com.flat.logic.entity.meter.FlatMeterLog;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatContractPropMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.mapper.lease.FlatPayPlanMapper;
import com.flat.logic.mapper.lease.FlatSurrenderMapper;
import com.flat.logic.mapper.meter.FlatMeterLogMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.model.ContractProp;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

@Service
public class LeaseService {

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private ContractService contractService;

    @Resource
    private PayPlanService payPlanService;

    @Resource
    private FlatPayPlanMapper payPlanMapper;

    @Resource
    private FlatContractMapper contractMapper;

    @Resource
    private FlatContractPropMapper contractPropMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private FlatSurrenderMapper surrenderMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private FlatMeterLogMapper meterLogMapper;

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    @Resource
    private OSSService ossService;

    /**
     * 获取最后一次水表值
     */
    public BigDecimal getLastWaterValue(FlatLease lease) {
        FlatMeterLog waterMeterLog = meterLogMapper.selectFirst(q -> q
            .eq(FlatMeterLog::getRoomId, lease.getRoomId())
            .eq(FlatMeterLog::getType, 1)
            .orderByDesc(FlatMeterLog::getCreateTime)
        );
        BigDecimal waterValue;
        if (waterMeterLog != null) {
            waterValue = waterMeterLog.getEndValue();
        } else {
            waterValue = lease.getCheckInWaterValue();
        }
        if(waterValue == null) {
            waterValue = BigDecimal.ZERO;
        }
        return waterValue;
    }

    /**
     * 获取最后一次电表值
     */
    public BigDecimal getLastElectricValue(FlatLease lease) {
        FlatMeterLog electricMeterLog = meterLogMapper.selectFirst(q -> q
            .eq(FlatMeterLog::getRoomId, lease.getRoomId())
            .eq(FlatMeterLog::getType, 2)
            .orderByDesc(FlatMeterLog::getCreateTime)
        );
        BigDecimal electricValue;
        if (electricMeterLog != null) {
            electricValue = electricMeterLog.getEndValue();
        } else {
            electricValue = lease.getCheckInElectricValue();
        }
        if(electricValue == null) {
            electricValue = BigDecimal.ZERO;
        }
        return electricValue;
    }

    /**
     * 新增租赁
     */
    @Transactional(rollbackFor = Exception.class)
    public void addLease(LeaseAddReq req, MaiUser maiUser) {
        FlatRoom room = roomMapper.selectById(req.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        if (room.getStatus() == 1) {
            throw new ServiceException("房源已被租赁");
        }

        if(room.getStatus() == 2) {
            throw new ServiceException("房源正在维修中，无法租赁");
        }

        if(room.getStatus() == 3) {
            throw new ServiceException("房源属于脏房，无法租赁");
        }

        if(room.getStatus() == 4) {
            throw new ServiceException("房源已被占用，无法租赁");
        }

        // 检查参数
        if(room.getKind() == 0) { // 公寓住房
            if(req.getCompanyId() == null) {
                throw new ServiceException("企业不能为空");
            }

            if(req.getMonthMoney() == null) {
                throw new ServiceException("每月租金不能为空");
            }

            if(req.getDepositMoney() == null) {
                throw new ServiceException("押金不能为空");
            }

            if(req.getDepositMoney().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("押金不能为0");
            }

            if(req.getLiveCount() == null) {
                throw new ServiceException("居住人数不能为空");
            }

            if(req.getLiveCount() <= 0) {
                throw new ServiceException("居住人数不能为0");
            }

            if(req.getDeliveryDate() == null) {
                throw new ServiceException("交付日期不能为空");
            }

            if(req.getPayPeriod() == null) {
                throw new ServiceException("付租周期不能为空");
            }

            if(req.getPayMode() == null) {
                throw new ServiceException("支付方式不能为空");
            }

            if(req.getPayMode() != 0 && req.getPayMode() != 1) {
                throw new ServiceException("支付方式只能为0或1");
            }

            if(req.getDurationType() == null) {
                throw new ServiceException("租赁时长类型不能为空");
            }

            if(req.getDurationType() != 0 && req.getDurationType() != 1) {
                throw new ServiceException("租赁时长类型只能为0或1");
            }
        } else if(room.getKind() == 1) { // 商铺

            // 物业费
//            if(req.getMonthPropertyMoney() == null) {
//                throw new ServiceException("每月物业费不能为空");
//            }
//
//            if(req.getMonthPropertyMoney().compareTo(BigDecimal.ZERO) <= 0) {
//                throw new ServiceException("每月物业费不能为0");
//            }
        }

        FlatCompany company = room.getKind() == 0 ? companyMapper.selectById(req.getCompanyId()) : companyMapper.selectFirst(q -> q.eq(FlatCompany::getSymbol, "hn"));
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        if(leaseMapper.selectCount(q -> q
                .eq(FlatLease::getRoomId, room.getId())
                .eq(FlatLease::getInvalidFlag, 0)
                .in(FlatLease::getStatus, List.of(0, 2, 3, 5, 7))
                .eq(FlatLease::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("房源已存在租赁记录");
        }

        CusUser cusUser = cusUserMapper.selectFirst(q -> q
                .eq(CusUser::getUserId, req.getCusId())
                .eq(CusUser::getDelStatus, 0)
        );
        if (cusUser == null) {
            throw new ServiceException("用户不存在");
        }

        //检查实名认证
        if (cusUser.getIsReal() != 1) {
            throw new ServiceException("用户未实名认证");
        }

        // 根据房源kind=1，检查付租周期只能为年并且租赁月份最少为12个月
        if (room.getKind() == 1) {
            if (req.getMonthCount() < 12) {
                throw new ServiceException("商铺租赁月份最少为12个月");
            }
        }

        // 计算租金计划
        List<FlatPayPlan> rentPlans = new ArrayList<>();
        if (room.getKind() == 0) {
            rentPlans = payPlanService.calculateRentPlans(
                company.getSymbol(), room.getKind() == 0 ? req.getDurationType() : 1, req.getMonthMoney(),
                req.getBeginDate(), req.getMonthCount(), room.getKind() == 0 ? req.getPayPeriod() : 3
            );
        }

        // 计算能耗费计划
        List<FlatPayPlan> energyPlans = new ArrayList<>();
        if (room.getKind() == 0) {
            energyPlans = payPlanService.calculateEnergyPlans(
                    company.getSymbol(), req.getBeginDate(), req.getMonthCount()
            );
        }

        // 计算商铺物业费计划
        List<FlatPayPlan> shopPropertyPlans = new ArrayList<>();
        if (room.getKind() == 1) {
            shopPropertyPlans = payPlanService.calculateShopPropertyPlans(
                req.getBeginDate(), req.getMonthCount(), req.getMonthPropertyMoney()
            );
        }

        // 新增合同
        FlatContract contract = contractService.addContract(
            room, company, cusUser, 1,
            req.getDeliveryDate(), req.getBeginDate(), req.getMonthCount(),
            req.getMonthMoney(), req.getDepositMoney(),
            req.getMonthPropertyMoney(),
            req.getLiveCount(), room.getKind() == 0 ? req.getDurationType() : 1, room.getKind() == 0 ? req.getPayPeriod() : 3,
            req.getContactName(), req.getContactPhoneNumber(), req.getDeliveryAddress(),
            rentPlans.isEmpty() ? null : rentPlans.get(0), req.getProps(),
            maiUser
        );

        // 新增租赁
        FlatLease lease = new FlatLease();
        lease.setFlatId(contract.getFlatId());
        lease.setRoomId(contract.getRoomId());
        lease.setCusId(contract.getCusId());
        lease.setButlerId(contract.getButlerId());
        lease.setPropertyId(room.getPropertyId());
        lease.setCompanyId(contract.getCompanyId());
        lease.setContractId(contract.getId());
        lease.setPayPeriod(room.getKind() == 0 ? req.getPayPeriod() : 3);
        lease.setStatus(0);
        lease.setContractVersion(contract.getVersion());
        lease.setContractCreateTime(contract.getCreateTime());
        lease.setContractBeginTime(contract.getBeginTime());
        lease.setContractEndTime(contract.getEndTime());
        lease.setContractMonthMoney(contract.getMonthMoney());
        lease.setContractMonthCount(contract.getMonthCount());
        lease.setContractTotalMoney(contract.getTotalMoney());
        lease.setContractDepositMoney(contract.getDepositMoney());
        lease.setContractMonthPropertyMoney(contract.getMonthPropertyMoney());
        lease.setSettleChannel(contract.getSettleChannel());
        lease.setCheckInWaterValue(req.getCheckInWaterValue());
        lease.setCheckInElectricValue(req.getCheckInElectricValue());
        lease.setWaterAccountBalance(BigDecimal.ZERO);
        lease.setElectricAccountBalance(BigDecimal.ZERO);
        lease.setRenewFlag(0);
        lease.setSubletFlag(0);
        lease.setInvalidFlag(0);
        lease.setCreateTime(contract.getCreateTime());
        lease.setCreateBy(maiUser.getUsername());
        lease.setDelStatus(0);
        leaseMapper.insert(lease);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, lease.getId())
                .eq(FlatContract::getId, contract.getId())
        );

        room.setStatus(1);
        room.setUpdateTime(lease.getCreateTime());
        room.setUpdateBy(lease.getCreateBy());
        roomMapper.updateById(room);

        if(!rentPlans.isEmpty()) {
            for (FlatPayPlan plan : rentPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(lease.getId());
                plan.setContractId(contract.getId());
                plan.setFlatId(lease.getFlatId());
                plan.setRoomId(lease.getRoomId());
                plan.setCusId(lease.getCusId());
                plan.setButlerId(lease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(lease.getCreateBy());
                plan.setCreateTime(lease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(rentPlans);
        }

        if(!energyPlans.isEmpty()) {
            for (FlatPayPlan plan : energyPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(lease.getId());
                plan.setContractId(contract.getId());
                plan.setFlatId(lease.getFlatId());
                plan.setRoomId(lease.getRoomId());
                plan.setCusId(lease.getCusId());
                plan.setButlerId(lease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(lease.getCreateBy());
                plan.setCreateTime(lease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(energyPlans);
        }

        if(!shopPropertyPlans.isEmpty()) {
            for (FlatPayPlan plan : shopPropertyPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(lease.getId());
                plan.setContractId(contract.getId());
                plan.setFlatId(lease.getFlatId());
                plan.setRoomId(lease.getRoomId());
                plan.setCusId(lease.getCusId());
                plan.setButlerId(lease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(lease.getCreateBy());
                plan.setCreateTime(lease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(shopPropertyPlans);
        }

    }

    /**
     * 续租
     */
    @Transactional(rollbackFor = Exception.class)
    public void renewLeaseV1(LeaseRenewV1Req req, MaiUser maiUser) {
        FlatLease oldLease = leaseMapper.selectById(req.getLeaseId());
        if (oldLease == null || oldLease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(oldLease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁已作废，无法续租");
        }

        // 检查租赁状态
        if (oldLease.getStatus() != 2) {
            throw new ServiceException("租赁非履约状态，无法续租");
        }

        // 合同
        FlatContract oldContract = contractMapper.selectById(oldLease.getContractId());
        if (oldContract == null || oldContract.getDelStatus() == 1) {
            throw new ServiceException("原合同不存在");
        }

        if(oldContract.getVersion() != 1) {
            throw new ServiceException("原合同版本不匹配");
        }

        FlatCompany company = companyMapper.selectById(oldLease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        FlatRoom room = roomMapper.selectById(oldLease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 商铺不支持续租
        if(room.getKind() == 1) {
            throw new ServiceException("商铺不支持续租");
        }

        CusUser cusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, oldLease.getCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (cusUser == null) {
            throw new ServiceException("用户不存在");
        }

        // 获取最新的水表值
        BigDecimal lastWaterValue = getLastWaterValue(oldLease);
        // 获取最新的电表值
        BigDecimal lastElectricValue = getLastElectricValue(oldLease);

        List<FlatPayPlan> newRentPlans = payPlanService.calculateRentPlans(
                company.getSymbol(), req.getDurationType(), req.getMonthMoney(),
                oldLease.getContractEndTime().toLocalDate(), req.getMonthCount(), req.getPayPeriod()
        );

        List<FlatPayPlan> newEnergyPlans = payPlanService.calculateEnergyPlans(
                company.getSymbol(), oldLease.getContractEndTime().toLocalDate(), req.getMonthCount()
        );

        FlatContract newContract = contractService.addContract(
            room, company, cusUser, 1,
            oldLease.getContractEndTime().toLocalDate(), oldLease.getContractEndTime().toLocalDate(), req.getMonthCount(),
            req.getMonthMoney(), oldLease.getContractDepositMoney(),
            oldLease.getContractMonthPropertyMoney(),
            req.getLiveCount(), req.getDurationType(), req.getPayPeriod(),
            req.getContactName(), req.getContactPhoneNumber(), req.getDeliveryAddress(),
            newRentPlans.isEmpty() ? null : newRentPlans.get(0), req.getProps(),
            maiUser
        );

        FlatLease newLease = new FlatLease();
        newLease.setFlatId(newContract.getFlatId());
        newLease.setRoomId(newContract.getRoomId());
        newLease.setCusId(newContract.getCusId());
        newLease.setButlerId(newContract.getButlerId());
        newLease.setPropertyId(room.getPropertyId());
        newLease.setCompanyId(newContract.getCompanyId());
        newLease.setContractId(newContract.getId());
        newLease.setPayPeriod(newContract.getPayPeriod());
        newLease.setStatus(0);
        newLease.setContractVersion(newContract.getVersion());
        newLease.setContractCreateTime(newContract.getCreateTime());
        newLease.setContractBeginTime(newContract.getBeginTime());
        newLease.setContractEndTime(newContract.getEndTime());
        newLease.setContractMonthMoney(newContract.getMonthMoney());
        newLease.setContractMonthCount(newContract.getMonthCount());
        newLease.setContractTotalMoney(newContract.getTotalMoney());
        newLease.setContractDepositMoney(newContract.getDepositMoney());
        newLease.setSettleChannel(newContract.getSettleChannel());
        newLease.setCheckInWaterValue(lastWaterValue);
        newLease.setCheckInElectricValue(lastElectricValue);
        newLease.setWaterAccountBalance(oldLease.getWaterAccountBalance());
        newLease.setElectricAccountBalance(oldLease.getElectricAccountBalance());
        newLease.setRenewFlag(1);
        newLease.setRenewFromLeaseId(oldLease.getId());
        newLease.setSubletFlag(0);
        newLease.setInvalidFlag(0);
        newLease.setCreateTime(newContract.getCreateTime());
        newLease.setCreateBy(maiUser.getUsername());
        newLease.setDelStatus(0);
        leaseMapper.insert(newLease);

        oldLease.setStatus(3);
        oldLease.setRenewApplyTime(LocalDateTime.now());
        oldLease.setRenewToLeaseId(newLease.getId());
        oldLease.setUpdateBy(maiUser.getNickName());
        oldLease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(oldLease);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, newLease.getId())
                .eq(FlatContract::getId, newContract.getId())
        );

        if(!newRentPlans.isEmpty()) {
            for (FlatPayPlan plan : newRentPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newRentPlans);
        }

        if(!newEnergyPlans.isEmpty()) {
            for (FlatPayPlan plan : newEnergyPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newEnergyPlans);
        }
    }

    /**
     * 续租
     */
    @Transactional(rollbackFor = Exception.class)
    public void renewLeaseV2(LeaseRenewV2Req req, MaiUser maiUser) {
        FlatLease oldLease = leaseMapper.selectById(req.getLeaseId());
        if (oldLease == null || oldLease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(oldLease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁已作废，无法续租");
        }

        // 检查租赁状态
        if (oldLease.getStatus() != 2) {
            throw new ServiceException("租赁非履约状态，无法续租");
        }

        if(payPlanMapper.selectCount(q -> q
                .eq(FlatPayPlan::getLeaseId, oldLease.getId())
                .eq(FlatPayPlan::getPayStatus, 0)
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 1)
        ) > 0) {
            throw new ServiceException("存在未支付的租金计划，无法续租");
        }

        if(payPlanMapper.selectCount(q -> q
                .eq(FlatPayPlan::getLeaseId, oldLease.getId())
                .eq(FlatPayPlan::getPayStatus, 0)
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 2)
        ) > 0) {
            throw new ServiceException("存在未支付的能耗费计划，无法续租");
        }

        // 合同
        FlatContract oldContract = contractMapper.selectById(oldLease.getContractId());
        if (oldContract == null || oldContract.getDelStatus() == 1) {
            throw new ServiceException("原合同不存在");
        }

        if(oldContract.getVersion() != 2) {
            throw new ServiceException("原合同版本不匹配");
        }

        FlatCompany company = companyMapper.selectById(oldLease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        FlatRoom room = roomMapper.selectById(oldLease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 商铺不支持续租
        if(room.getKind() == 1) {
            throw new ServiceException("商铺不支持续租");
        }

        CusUser cusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, oldLease.getCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (cusUser == null) {
            throw new ServiceException("用户不存在");
        }

        // 获取最新的水表值
        BigDecimal lastWaterValue = getLastWaterValue(oldLease);
        // 获取最新的电表值
        BigDecimal lastElectricValue = getLastElectricValue(oldLease);

        // prop
        FlatContractProp oldContractProp = contractPropMapper.selectFirst(q -> q
                .eq(FlatContractProp::getContractId, oldContract.getId())
                .eq(FlatContractProp::getDelStatus, 0)
        );
        if (oldContractProp == null || oldContractProp.getDelStatus() == 1) {
            throw new ServiceException("原合同属性不存在");
        }

        List<ContractProp> oldContractProps = JSON.parseArray(oldContractProp.getPropsData(), ContractProp.class);

        List<FlatPayPlan> newRentPlans = payPlanService.calculateRentPlans(
                company.getSymbol(), req.getDurationType(), req.getMonthMoney(),
                oldLease.getContractEndTime().toLocalDate(), req.getMonthCount(), req.getPayPeriod()
        );

        List<FlatPayPlan> newEnergyPlans = payPlanService.calculateEnergyPlans(
                company.getSymbol(), oldLease.getContractEndTime().toLocalDate(), req.getMonthCount()
        );

        FlatContract newContract = contractService.addContract(
            room, company, cusUser, 1,
            oldLease.getContractEndTime().toLocalDate(), oldLease.getContractEndTime().toLocalDate(), req.getMonthCount(),
            req.getMonthMoney(), oldLease.getContractDepositMoney(), oldLease.getContractMonthPropertyMoney(),
            oldContract.getLiveCount(), req.getDurationType(), req.getPayPeriod(),
            oldContract.getContactName(), oldContract.getContactPhoneNumber(), oldContract.getDeliveryAddress(),
            newRentPlans.isEmpty() ? null : newRentPlans.get(0), oldContractProps,
            maiUser
        );

        FlatLease newLease = new FlatLease();
        newLease.setFlatId(newContract.getFlatId());
        newLease.setRoomId(newContract.getRoomId());
        newLease.setCusId(newContract.getCusId());
        newLease.setButlerId(newContract.getButlerId());
        newLease.setPropertyId(room.getPropertyId());
        newLease.setCompanyId(newContract.getCompanyId());
        newLease.setContractId(newContract.getId());
        newLease.setPayPeriod(newContract.getPayPeriod());
        newLease.setStatus(0);
        newLease.setContractVersion(newContract.getVersion());
        newLease.setContractCreateTime(newContract.getCreateTime());
        newLease.setContractBeginTime(newContract.getBeginTime());
        newLease.setContractEndTime(newContract.getEndTime());
        newLease.setContractMonthMoney(newContract.getMonthMoney());
        newLease.setContractMonthCount(newContract.getMonthCount());
        newLease.setContractTotalMoney(newContract.getTotalMoney());
        newLease.setContractDepositMoney(newContract.getDepositMoney());
        newLease.setSettleChannel(newContract.getSettleChannel());
        newLease.setCheckInWaterValue(lastWaterValue);
        newLease.setCheckInElectricValue(lastElectricValue);
        newLease.setWaterAccountBalance(oldLease.getWaterAccountBalance());
        newLease.setElectricAccountBalance(oldLease.getElectricAccountBalance());
        newLease.setRenewFlag(1);
        newLease.setRenewFromLeaseId(oldLease.getId());
        newLease.setSubletFlag(0);
        newLease.setInvalidFlag(0);
        newLease.setCreateTime(newContract.getCreateTime());
        newLease.setCreateBy(maiUser.getUsername());
        newLease.setDelStatus(0);
        leaseMapper.insert(newLease);

        oldLease.setStatus(3);
        oldLease.setRenewApplyTime(LocalDateTime.now());
        oldLease.setRenewToLeaseId(newLease.getId());
        oldLease.setUpdateBy(maiUser.getNickName());
        oldLease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(oldLease);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, newLease.getId())
                .eq(FlatContract::getId, newContract.getId())
        );

        if(!newRentPlans.isEmpty()) {
            for (FlatPayPlan plan : newRentPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newRentPlans);
        }

        if(!newEnergyPlans.isEmpty()) {
            for (FlatPayPlan plan : newEnergyPlans) {
                plan.setSubletFlag(0);
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newEnergyPlans);
        }
    }

    /**
     * 转租
     */
    @Transactional(rollbackFor = Exception.class)
    public void subletLeaseV1(LeaseSubletV1Req req, MaiUser maiUser) {
        FlatLease oldLease = leaseMapper.selectById(req.getLeaseId());
        if (oldLease == null || oldLease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(oldLease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁已作废，无法转租");
        }

        if (oldLease.getStatus() != 2) {
            throw new ServiceException("租赁非履约状态，无法转租");
        }

        if(Objects.equals(oldLease.getCusId(), req.getTargetCusId())) {
            throw new ServiceException("不能转租给自己");
        }

        if(oldLease.getContractEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("合同已到期，无法转租");
        }

        FlatContract oldContract = contractMapper.selectById(oldLease.getContractId());
        if (oldContract == null || oldContract.getDelStatus() == 1) {
            throw new ServiceException("原合同不存在");
        }

        if (oldContract.getVersion() != 1) {
            throw new ServiceException("原合同版本不匹配");
        }

        FlatCompany company = companyMapper.selectById(oldLease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        Integer durationType = oldContract.getMonthCount() <= 3 ? 0 : 1;
//        if("hn".equalsIgnoreCase(company.getSymbol()) && durationType == 0) {
//            throw new ServiceException("恒诺公司短租不允许转租");
//        }

        FlatRoom room = roomMapper.selectById(oldLease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 商铺不支持转租
        if(room.getKind() == 1) {
            throw new ServiceException("商铺不支持转租");
        }

        CusUser targetCusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, req.getTargetCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (targetCusUser == null) {
            throw new ServiceException("转租目标用户不存在");
        }

        if(targetCusUser.getIsReal() != 1) {
            throw new ServiceException("转租目标用户未实名认证");
        }

        // 获取最新的水表值
        BigDecimal lastWaterValue = getLastWaterValue(oldLease);
        // 获取最新的电表值
        BigDecimal lastElectricValue = getLastElectricValue(oldLease);

        LocalDateTime subletBeginTime = oldContract.getBeginTime();

        List<FlatPayPlan> newRentPlans = payPlanService.calculateRentPlans(
                company.getSymbol(), durationType, oldLease.getContractMonthMoney(),
                subletBeginTime.toLocalDate(), oldContract.getMonthCount(), oldContract.getPayPeriod()
        );

        List<FlatPayPlan> newEnergyPlans = payPlanService.calculateEnergyPlans(
                company.getSymbol(), subletBeginTime.toLocalDate(), oldContract.getMonthCount()
        );

        FlatContract newContract = contractService.addContract(
            room, company, targetCusUser, 1,
            subletBeginTime.toLocalDate(), subletBeginTime.toLocalDate(), oldContract.getMonthCount(),
            oldLease.getContractMonthMoney(), oldLease.getContractDepositMoney(), oldLease.getContractMonthPropertyMoney(),
            req.getLiveCount(), durationType, oldContract.getPayPeriod(),
            req.getContactName(), req.getContactPhoneNumber(), req.getDeliveryAddress(),
            newRentPlans.isEmpty() ? null : newRentPlans.get(0), req.getProps(),
            maiUser
        );

        FlatLease newLease = new FlatLease();
        newLease.setFlatId(newContract.getFlatId());
        newLease.setRoomId(newContract.getRoomId());
        newLease.setCusId(newContract.getCusId());
        newLease.setButlerId(newContract.getButlerId());
        newLease.setPropertyId(room.getPropertyId());
        newLease.setCompanyId(newContract.getCompanyId());
        newLease.setContractId(newContract.getId());
        newLease.setPayPeriod(newContract.getPayPeriod());
        newLease.setStatus(0);
        newLease.setContractVersion(newContract.getVersion());
        newLease.setContractCreateTime(newContract.getCreateTime());
        newLease.setContractBeginTime(newContract.getBeginTime());
        newLease.setContractEndTime(newContract.getEndTime());
        newLease.setContractMonthMoney(newContract.getMonthMoney());
        newLease.setContractMonthCount(newContract.getMonthCount());
        newLease.setContractTotalMoney(newContract.getTotalMoney());
        newLease.setContractDepositMoney(newContract.getDepositMoney());
        newLease.setSettleChannel(newContract.getSettleChannel());
        newLease.setCheckInWaterValue(lastWaterValue);
        newLease.setCheckInElectricValue(lastElectricValue);
        newLease.setWaterAccountBalance(oldLease.getWaterAccountBalance());
        newLease.setElectricAccountBalance(oldLease.getElectricAccountBalance());
        newLease.setRenewFlag(0);
        newLease.setSubletFlag(1);
        newLease.setSubletFromLeaseId(oldLease.getId());
        newLease.setInvalidFlag(0);
        newLease.setCreateTime(newContract.getCreateTime());
        newLease.setCreateBy(maiUser.getUsername());
        newLease.setDelStatus(0);
        leaseMapper.insert(newLease);

        oldLease.setStatus(5);
        oldLease.setSubletToKeepLive(req.getKeepLive());
        oldLease.setSubletApplyTime(LocalDateTime.now());
        oldLease.setSubletToLeaseId(newLease.getId());
        oldLease.setUpdateBy(maiUser.getNickName());
        oldLease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(oldLease);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, newLease.getId())
                .set(FlatContract::getCreateBy, maiUser.getUsername())
                .set(FlatContract::getCreateTime, newLease.getCreateTime())
                .eq(FlatContract::getId, newContract.getId())
                .eq(FlatContract::getDelStatus, 0)
        );

        if (!newRentPlans.isEmpty()) {
            for (FlatPayPlan plan : newRentPlans) {
                plan.setSubletFlag(1);
                plan.setSubletFromLeaseId(oldLease.getId());
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newRentPlans);
        }

        if (!newEnergyPlans.isEmpty()) {
            for (FlatPayPlan plan : newEnergyPlans) {
                plan.setSubletFlag(1);
                plan.setSubletFromLeaseId(oldLease.getId());
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newEnergyPlans);
        }
    }

    /**
     * 转租
     */
    @Transactional(rollbackFor = Exception.class)
    public void subletLeaseV2(LeaseSubletV2Req req, MaiUser maiUser) {
        FlatLease oldLease = leaseMapper.selectById(req.getLeaseId());
        if (oldLease == null || oldLease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(oldLease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁已作废，无法转租");
        }

        if (oldLease.getStatus() != 2) {
            throw new ServiceException("租赁非履约状态，无法转租");
        }

        if(Objects.equals(oldLease.getCusId(), req.getTargetCusId())) {
            throw new ServiceException("不能转租给自己");
        }

        if(oldLease.getContractEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("合同已到期，无法转租");
        }

        FlatContract oldContract = contractMapper.selectById(oldLease.getContractId());
        if (oldContract == null || oldContract.getDelStatus() == 1) {
            throw new ServiceException("原合同不存在");
        }

        if (oldContract.getVersion() != 2) {
            throw new ServiceException("原合同版本不匹配");
        }

        FlatCompany company = companyMapper.selectById(oldLease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

//        if("hn".equalsIgnoreCase(company.getSymbol()) && oldContract.getDurationType() == 0) {
//            throw new ServiceException("恒诺公司短租不允许转租");
//        }

        FlatRoom room = roomMapper.selectById(oldLease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 商铺不支持转租
        if(room.getKind() == 1) {
            throw new ServiceException("商铺不支持转租");
        }

        CusUser targetCusUser = cusUserMapper.selectFirst((query) ->
                query.eq(CusUser::getUserId, req.getTargetCusId()).eq(CusUser::getDelStatus, 0)
        );
        if (targetCusUser == null) {
            throw new ServiceException("转租目标用户不存在");
        }

        List<FlatPayPlan> oldPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getLeaseId, oldLease.getId())
                .gt(FlatPayPlan::getMoney, BigDecimal.ZERO)
        );
        for (FlatPayPlan plan : oldPlans) {
            if(plan.getEndTime().isBefore(LocalDateTime.now()) || plan.getBeginTime().isBefore(LocalDateTime.now())) {
                if(plan.getPayStatus() != 1) {
                    throw new ServiceException("当前或之前的支付计划未支付");
                }
            }
        }

        // 获取最新的水表值
        BigDecimal lastWaterValue = getLastWaterValue(oldLease);
        // 获取最新的电表值
        BigDecimal lastElectricValue = getLastElectricValue(oldLease);

        // prop
        FlatContractProp oldContractProp = contractPropMapper.selectFirst(q -> q
                .eq(FlatContractProp::getContractId, oldContract.getId())
                .eq(FlatContractProp::getDelStatus, 0)
        );
        if (oldContractProp == null || oldContractProp.getDelStatus() == 1) {
            throw new ServiceException("原合同属性不存在");
        }
        List<ContractProp> oldContractProps = JSON.parseArray(oldContractProp.getPropsData(), ContractProp.class);

        LocalDateTime subletBeginTime = oldLease.getContractCreateTime().plusMonths(oldLease.getContractMonthCount() - oldContract.getMonthCount());

        List<FlatPayPlan> newRentPlans = payPlanService.calculateRentPlans(
            company.getSymbol(), oldContract.getDurationType(), oldLease.getContractMonthMoney(),
            subletBeginTime.toLocalDate(), oldContract.getMonthCount(), oldContract.getPayPeriod()
        );

        List<FlatPayPlan> newEnergyPlans = payPlanService.calculateEnergyPlans(
                company.getSymbol(), subletBeginTime.toLocalDate(), oldContract.getMonthCount()
        );

        FlatContract newContract = contractService.addContract(
            room, company, targetCusUser, 1,
            subletBeginTime.toLocalDate(), subletBeginTime.toLocalDate(), oldContract.getMonthCount(),
            oldLease.getContractMonthMoney(), oldLease.getContractDepositMoney(), oldLease.getContractMonthPropertyMoney(),
            oldContract.getLiveCount(), oldContract.getDurationType(), oldContract.getPayPeriod(),
            req.getContactName(), req.getContactPhoneNumber(), req.getDeliveryAddress(),
            newRentPlans.isEmpty() ? null : newRentPlans.get(0), oldContractProps,
            maiUser
        );

        FlatLease newLease = new FlatLease();
        newLease.setFlatId(newContract.getFlatId());
        newLease.setRoomId(newContract.getRoomId());
        newLease.setCusId(newContract.getCusId());
        newLease.setButlerId(newContract.getButlerId());
        newLease.setPropertyId(room.getPropertyId());
        newLease.setCompanyId(newContract.getCompanyId());
        newLease.setContractId(newContract.getId());
        newLease.setPayPeriod(newContract.getPayPeriod());
        newLease.setStatus(0);
        newLease.setContractVersion(newContract.getVersion());
        newLease.setContractCreateTime(newContract.getCreateTime());
        newLease.setContractBeginTime(newContract.getBeginTime());
        newLease.setContractEndTime(newContract.getEndTime());
        newLease.setContractMonthMoney(newContract.getMonthMoney());
        newLease.setContractMonthCount(newContract.getMonthCount());
        newLease.setContractTotalMoney(newContract.getTotalMoney());
        newLease.setContractDepositMoney(newContract.getDepositMoney());
        newLease.setSettleChannel(newContract.getSettleChannel());
        newLease.setCheckInWaterValue(lastWaterValue);
        newLease.setCheckInElectricValue(lastElectricValue);
        newLease.setWaterAccountBalance(oldLease.getWaterAccountBalance());
        newLease.setElectricAccountBalance(oldLease.getElectricAccountBalance());
        newLease.setRenewFlag(0);
        newLease.setSubletFlag(1);
        newLease.setSubletFromLeaseId(oldLease.getId());
        newLease.setInvalidFlag(0);
        newLease.setCreateTime(newContract.getCreateTime());
        newLease.setCreateBy(maiUser.getUsername());
        newLease.setDelStatus(0);
        leaseMapper.insert(newLease);

        oldLease.setStatus(5);
        oldLease.setSubletToKeepLive(req.getKeepLive());
        oldLease.setSubletApplyTime(LocalDateTime.now());
        oldLease.setSubletToLeaseId(newLease.getId());
        oldLease.setUpdateBy(maiUser.getNickName());
        oldLease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(oldLease);

        contractMapper.update(q -> q
                .set(FlatContract::getLeaseId, newLease.getId())
                .eq(FlatContract::getId, newContract.getId())
        );

        if (!newRentPlans.isEmpty()) {
            for (FlatPayPlan plan : newRentPlans) {
                plan.setSubletFlag(1);
                plan.setSubletFromLeaseId(oldLease.getId());
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newRentPlans);
        }

        if (!newEnergyPlans.isEmpty()) {
            for (FlatPayPlan plan : newEnergyPlans) {
                plan.setSubletFlag(1);
                plan.setSubletFromLeaseId(oldLease.getId());
                plan.setLeaseId(newLease.getId());
                plan.setContractId(newContract.getId());
                plan.setFlatId(newLease.getFlatId());
                plan.setRoomId(newLease.getRoomId());
                plan.setCusId(newLease.getCusId());
                plan.setButlerId(newLease.getButlerId());
                plan.setPayStatus(0);
                plan.setInvalidFlag(0);
                plan.setCreateBy(newLease.getCreateBy());
                plan.setCreateTime(newLease.getCreateTime());
                plan.setDelStatus(0);
            }
            payPlanMapper.insert(newEnergyPlans);
        }
    }

    /**
     * 合同审核拒绝
     * @param contract
     * @param room
     * @param client 审核用户客户端[0=管理端;1=管家端]
     * @param userId
     * @param username
     */
    public void contractVerifyRejected(FlatContract contract, FlatRoom room, Integer client, Long userId, String username) {
        FlatLease lease = leaseMapper.selectById(contract.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        // 新的各类型支付计划标记为作废
        payPlanMapper.update(q -> q
                .set(FlatPayPlan::getInvalidFlag, 1)
                .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                .set(FlatPayPlan::getUpdateBy, username)
                .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
        );

        if(lease.getRenewFlag() == 1) { // 续租
            // 原来的恢复
            FlatLease oldLease = leaseMapper.selectById(lease.getRenewFromLeaseId());
            oldLease.setStatus(2);
            oldLease.setRenewToLeaseId(null);
            oldLease.setRenewApplyTime(null);
            oldLease.setUpdateBy(username);
            oldLease.setUpdateTime(LocalDateTime.now());
            leaseMapper.updateById(oldLease);
        } else if(lease.getSubletFlag() == 1) { // 转租
            // 原来的恢复
            FlatLease oldLease = leaseMapper.selectById(lease.getSubletFromLeaseId());
            oldLease.setStatus(2);
            oldLease.setSubletToKeepLive(null);
            oldLease.setSubletToLeaseId(null);
            oldLease.setSubletApplyTime(LocalDateTime.now());
            oldLease.setUpdateBy(username);
            oldLease.setUpdateTime(LocalDateTime.now());
            leaseMapper.updateById(oldLease);
        } else {
            // 房源状态恢复
            room.setStatus(0);
            room.setUpdateBy(username);
            room.setUpdateTime(LocalDateTime.now());
            roomMapper.updateById(room);
        }

        // 标记租赁记录已取消
        lease.setStatus(1);
        lease.setCancelTime(LocalDateTime.now());
        lease.setUpdateBy(username);
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        if(client == 0) {
            messageService.sysToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核驳回",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );
        } else if(client == 1) {
            messageService.butlerToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核驳回",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );
        }
    }

    /**
     * 合同审核通过
     * @param contract
     * @param room
     * @param client 审核用户客户端[0=管理端;1=管家端]
     * @param userId
     * @param username
     */
    public void contractVerifyPassed(FlatContract contract, FlatRoom room, Integer client, Long userId, String username) {
        FlatLease lease = leaseMapper.selectById(contract.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        if(client == 0) {
            messageService.sysToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核通过",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );

            messageService.sysToCus(
                    MessageService.TYPE_CONTRACT, userId, contract.getCusId(),
                    "合同已经生成，请尽快完成签署",
                    "/pages/user/agreement/info/index?id=" + contract.getId()
            );
        } else if(client == 1) {
            messageService.butlerToButler(
                MessageService.TYPE_CONTRACT, userId, contract.getButlerId(),
                "合同审核通过",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
            );

            messageService.butlerToCus(
                    MessageService.TYPE_CONTRACT, userId, contract.getCusId(),
                    "合同已经生成，请尽快完成签署",
                    "/pages/user/agreement/info/index?id=" + contract.getId()
            );
        }
    }

    /**
     * 用户拒绝签约
     * @param contract 合同
     * @param room 房源
     * @param cusUser 用户
     * @param operateTime 操作时间
     */
    public void contractSignRejected(FlatContract contract, FlatRoom room, CusUser cusUser, LocalDateTime operateTime) {
        FlatLease lease = leaseMapper.selectById(contract.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        // 新的各类型支付计划标记为作废
        payPlanMapper.update(q -> q
                .set(FlatPayPlan::getInvalidFlag, 1)
                .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                .set(FlatPayPlan::getUpdateBy, cusUser.getNickName())
                .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
        );

        if(lease.getRenewFlag() == 1) { // 续租
            // 原来的恢复
            FlatLease oldLease = leaseMapper.selectById(lease.getRenewFromLeaseId());
            oldLease.setStatus(2);
            oldLease.setRenewToLeaseId(null);
            oldLease.setRenewApplyTime(null);
            oldLease.setUpdateBy(cusUser.getNickName());
            oldLease.setUpdateTime(LocalDateTime.now());
            leaseMapper.updateById(oldLease);
        } else if(lease.getSubletFlag() == 1) { // 转租
            // 原来的恢复
            FlatLease oldLease = leaseMapper.selectById(lease.getSubletFromLeaseId());
            oldLease.setStatus(2);
            oldLease.setSubletToKeepLive(null);
            oldLease.setSubletToLeaseId(null);
            oldLease.setSubletApplyTime(LocalDateTime.now());
            oldLease.setUpdateBy(cusUser.getNickName());
            oldLease.setUpdateTime(LocalDateTime.now());
            leaseMapper.updateById(oldLease);
        } else {
            // 房源状态恢复
            room.setStatus(0);
            room.setUpdateBy(cusUser.getNickName());
            room.setUpdateTime(LocalDateTime.now());
            roomMapper.updateById(room);
        }

        lease.setStatus(1);
        lease.setCancelTime(operateTime);
        lease.setUpdateBy(cusUser.getNickName());
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        messageService.cusToButler(
                MessageService.TYPE_CONTRACT, cusUser.getUserId(), contract.getButlerId(),
                "用户拒绝签署合同",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
        );
    }

    /**
     * 用户已签约
     * @param contract 合同
     * @param room 房源
     * @param cusUser 用户
     * @param operateTime 操作时间
     */
    public void contractSignPassed(FlatContract contract, FlatRoom room, CusUser cusUser, LocalDateTime operateTime) {
        FlatLease lease = leaseMapper.selectById(contract.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        if(lease.getRenewFlag() == 1) { // 续租
            FlatLease oldLease = leaseMapper.selectById(lease.getRenewFromLeaseId());
            oldLease.setStatus(4);
            oldLease.setRenewFinishTime(LocalDateTime.now());
            oldLease.setUpdateBy(cusUser.getNickName());
            oldLease.setUpdateTime(LocalDateTime.now());
            leaseMapper.updateById(oldLease);

            contractMapper.update(q -> q
                    .set(FlatContract::getStatus, 6)
                    .set(FlatContract::getUpdateTime, LocalDateTime.now())
                    .set(FlatContract::getUpdateBy, cusUser.getNickName())
                    .eq(FlatContract::getId, oldLease.getContractId())
                    .eq(FlatContract::getDelStatus, 0)
            );

            // 同住人记录状态变更为已完成
            liveMapper.update(q -> q
                    .set(FlatLive::getStatus, 3)
                    .set(FlatLive::getUpdateTime, LocalDateTime.now())
                    .set(FlatLive::getUpdateBy, cusUser.getNickName())
                    .eq(FlatLive::getLeaseId, oldLease.getId())
                    .eq(FlatLive::getDelStatus, 0)
            );

            // 同住人合同变更为已完成
            contractMapper.update(q -> q
                    .set(FlatContract::getStatus, 6)
                    .set(FlatContract::getUpdateTime, LocalDateTime.now())
                    .set(FlatContract::getUpdateBy, cusUser.getNickName())
                    .eq(FlatContract::getLeaseId, oldLease.getId())
                    .eq(FlatContract::getType, 2)
                    .eq(FlatContract::getDelStatus, 0)
            );

            // 付款计划一定已经全部支付完成了，否则不允许发起续租，所以此处不需要对付款计划进行作废处理

            // 再次复制一下余额，防止从申请到签署期间余额有变化
            lease.setWaterAccountBalance(oldLease.getWaterAccountBalance());
            lease.setElectricAccountBalance(oldLease.getElectricAccountBalance());
        } else if(lease.getSubletFlag() == 1) { // 转租
            FlatLease oldLease = leaseMapper.selectById(lease.getSubletFromLeaseId());
            oldLease.setStatus(6);
            oldLease.setSubletFinishTime(LocalDateTime.now());
            oldLease.setUpdateBy(cusUser.getNickName());
            oldLease.setUpdateTime(LocalDateTime.now());
            leaseMapper.updateById(oldLease);

            contractMapper.update(q -> q
                    .set(FlatContract::getStatus, 6)
                    .set(FlatContract::getUpdateTime, LocalDateTime.now())
                    .set(FlatContract::getUpdateBy, cusUser.getNickName())
                    .eq(FlatContract::getId, oldLease.getContractId())
                    .eq(FlatContract::getDelStatus, 0)
            );

            // 判断转租的目标用户原来是否是同住人，如果是则标记同住人记录和对应的合同为已完成
            liveMapper.update(q -> q
                    .set(FlatLive::getStatus, 3)
                    .set(FlatLive::getUpdateTime, LocalDateTime.now())
                    .set(FlatLive::getUpdateBy, cusUser.getNickName())
                    .eq(FlatLive::getLeaseId, oldLease.getId())
                    .eq(FlatLive::getDelStatus, 0)
                    .eq(FlatLive::getLiveCusId, lease.getCusId())
            );
            contractMapper.update(q -> q
                    .set(FlatContract::getStatus, 6)
                    .set(FlatContract::getUpdateTime, LocalDateTime.now())
                    .set(FlatContract::getUpdateBy, cusUser.getNickName())
                    .eq(FlatContract::getLeaseId, oldLease.getId())
                    .eq(FlatContract::getType, 2)
                    .eq(FlatContract::getDelStatus, 0)
                    .eq(FlatContract::getCusId, lease.getCusId())
            );

            if(oldLease.getSubletToKeepLive() == 0) { // 不保留同住人
                // 同住人记录状态变更为已完成
                liveMapper.update(q -> q
                        .set(FlatLive::getStatus, 3)
                        .set(FlatLive::getUpdateTime, LocalDateTime.now())
                        .set(FlatLive::getUpdateBy, cusUser.getNickName())
                        .eq(FlatLive::getLeaseId, oldLease.getId())
                        .eq(FlatLive::getDelStatus, 0)
                        .ne(FlatLive::getStatus, 3)
                );

                // 同住人合同变更为已完成
                contractMapper.update(q -> q
                        .set(FlatContract::getStatus, 6)
                        .set(FlatContract::getUpdateTime, LocalDateTime.now())
                        .set(FlatContract::getUpdateBy, cusUser.getNickName())
                        .eq(FlatContract::getLeaseId, oldLease.getId())
                        .eq(FlatContract::getType, 2)
                        .eq(FlatContract::getDelStatus, 0)
                        .ne(FlatContract::getStatus, 6)
                );
            } else { // 保留同住人
                // 同住人记录转移到新的租赁记录中
                liveMapper.update(q -> q
                        .set(FlatLive::getLeaseId, lease.getId())
                        .set(FlatLive::getUpdateTime, LocalDateTime.now())
                        .set(FlatLive::getUpdateBy, cusUser.getNickName())
                        .eq(FlatLive::getLeaseId, oldLease.getId())
                        .eq(FlatLive::getDelStatus, 0)
                        .ne(FlatLive::getStatus, 3)
                );

                // 同住人合同变更转移到新的租赁记录中
                contractMapper.update(q -> q
                        .set(FlatContract::getLeaseId, lease.getId())
                        .set(FlatContract::getUpdateTime, LocalDateTime.now())
                        .set(FlatContract::getUpdateBy, cusUser.getNickName())
                        .eq(FlatContract::getLeaseId, oldLease.getId())
                        .eq(FlatContract::getType, 2)
                        .eq(FlatContract::getDelStatus, 0)
                        .ne(FlatContract::getStatus, 6)
                );
            }

            // 新的各类型付款计划中，先前时间的和包括当前时间的付款计划默认为固定付款
            List<FlatPayPlan> plans = payPlanMapper.selectList(q -> q
                    .eq(FlatPayPlan::getLeaseId, lease.getId())
                    .eq(FlatPayPlan::getDelStatus, 0)
            );
            for (FlatPayPlan plan : plans) {
                if(plan.getEndTime().isBefore(LocalDateTime.now()) || plan.getBeginTime().isBefore(LocalDateTime.now())) {
                    plan.setPayMode(2);
                    plan.setPayStatus(1);
                    plan.setPayTime(operateTime);
                    plan.setUpdateBy(cusUser.getNickName());
                    plan.setUpdateTime(LocalDateTime.now());
                    payPlanMapper.updateById(plan);
                }
            }

            // 原来的各类型付款计划中，非支付的标记为作废
            payPlanMapper.update(q -> q
                    .set(FlatPayPlan::getInvalidFlag, 1)
                    .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                    .set(FlatPayPlan::getUpdateBy, cusUser.getNickName())
                    .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                    .eq(FlatPayPlan::getLeaseId, oldLease.getId())
                    .eq(FlatPayPlan::getPayStatus, 0)
                    .eq(FlatPayPlan::getDelStatus, 0)
                    .eq(FlatPayPlan::getInvalidFlag, 0)
            );

            // 再次复制一下余额，防止从申请到签署期间余额有变化
            lease.setWaterAccountBalance(oldLease.getWaterAccountBalance());
            lease.setElectricAccountBalance(oldLease.getElectricAccountBalance());
        }

        lease.setStatus(2);
        lease.setContractSignTime(operateTime);
        lease.setUpdateBy(cusUser.getNickName());
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        messageService.cusToButler(
                MessageService.TYPE_CONTRACT, cusUser.getUserId(), contract.getButlerId(),
                "用户已签署合同",
                "/pages/butler/agreement/info/index?id=" + contract.getId()
        );
    }

    public List<LeaseListResp> queryRespList(LeaseQueryReq req) {
        return leaseMapper.selectRespList(req);
    }

    public TablePage<LeaseListResp> queryRespPage(LeaseQueryReq req) {
        return PageUtils.paginate(() -> leaseMapper.selectRespList(req));
    }

    public LeaseDetailResp queryRespDetail(Long id) {
        FlatLease lease = leaseMapper.selectById(id);
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        FlatRoom room = roomMapper.selectById(lease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        FlatCompany company = companyMapper.selectById(lease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        CusUser cusUser = cusUserMapper.selectById(lease.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在");
        }

        FlatContract contract = contractMapper.selectById(lease.getContractId());
        if(contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同不存在");
        }

        List<FlatPayPlan> rentPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 1)
        );

        List<FlatPayPlan> energyPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 2)
        );

        List<FlatPayPlan> shopPropertyPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, contract.getLeaseId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 3)
        );

        LiveQueryReq liveQueryReq = new LiveQueryReq();
        liveQueryReq.setLeaseId(lease.getId());
        List<LiveResp> lives = SpringUtils.getBean(LiveService.class).queryRespList(liveQueryReq);

        LeaseDetailResp resp = new LeaseDetailResp();
        BeanUtils.copyProperties(lease, resp);
        resp.setRoom(room);
        resp.setCompanyName(company.getCompanyName());
        resp.setCusUser(cusUser);
        resp.setContract(contract);
        resp.setRentPlans(rentPlans);
        resp.setEnergyPlans(energyPlans);
        resp.setShopPropertyPlans(shopPropertyPlans);
        resp.setLives(lives);
        resp.setTotalMoney(billMapper.statTotalMoney(lease.getId()));
        resp.setInvoicedMoney(billMapper.statInvoicedMoney(lease.getId()));
        resp.setCusUserIdCardFrontUrl(ossService.joinUrl(cusUser.getFrontUrl()));
        resp.setCusUserIdCardReverseUrl(ossService.joinUrl(cusUser.getReverseUrl()));

        return resp;
    }

    /**
     * 用户发起退租
     */
    @Transactional(rollbackFor = Exception.class)
    public void surrender(SurrenderApplyReq req, CusUser cusUser) {
        if(StringUtils.isBlank(req.getBankAccount()) && StringUtils.isBlank(req.getAlipayAccount())) {
            throw new ServiceException("银行账号或支付宝账号请至少填写一个");
        }

        FlatLease lease = leaseMapper.selectById(req.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        if(lease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁记录已作废");
        }

        // 判读租赁状态
        if (lease.getStatus() != 2) {
            throw new ServiceException("只有履约状态的租赁记录才可以发起退租");
        }

        if(!Objects.equals(lease.getCusId(), cusUser.getUserId())) {
            throw new ServiceException("只有承租方本人账号才可以发起退租");
        }

        // 查询房源
        FlatRoom room = roomMapper.selectById(lease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        //添加退租记录
        FlatSurrender surrender = new FlatSurrender();
        surrender.setFlatId(lease.getFlatId());
        surrender.setRoomId(lease.getRoomId());
        surrender.setCusId(lease.getCusId());
        surrender.setCusRealName(req.getCusRealName());
        surrender.setCusPhoneNumber(req.getCusPhoneNumber());
        surrender.setButlerId(lease.getButlerId());
        surrender.setContractId(lease.getContractId());
        surrender.setLeaseId(lease.getId());
        surrender.setReason(req.getReason());
        surrender.setBankAccount(req.getBankAccount());
        surrender.setOpenBankName(req.getOpenBankName());
        surrender.setOpenBankAddress(req.getOpenBankAddress());
        surrender.setAlipayAccount(req.getAlipayAccount());
        surrender.setSurrenderDate(req.getSurrenderDate());
        surrender.setStatus(0);
        surrender.setCreateTime(LocalDateTime.now());
        surrender.setCreateBy(cusUser.getNickName());
        surrender.setDelStatus(0);
        surrenderMapper.insert(surrender);

        // 更新状态
        lease.setStatus(7);
        lease.setSurrenderId(surrender.getId());
        lease.setSurrenderApplyTime(LocalDateTime.now());
        lease.setUpdateBy(cusUser.getNickName());
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(surrender.getId());
        log.setType("surrender");
        log.setRefuse("用户提交退租成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
        if (maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
            weChatPublicMessageService.sendMessage(
                    maiUser,
                    "aE-Y9ijSSnrq7_Wdm1f5z6M3iWb-vYnIBXnSZ1q-jSo",
                    null,
                    Map.of(
                            "thing1", Map.of("value", room.getName()),
                            "thing2", Map.of("value", cusUser.getRealName())
                    )
            );
        }
    }

    /**
     * 删除，只能删除未签署合同的租赁记录或已取消的租赁记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long leaseId, SysUser sysUser) {
        // 判断状态
        FlatLease lease = leaseMapper.selectById(leaseId);
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if (lease.getStatus() != 0 && lease.getStatus() != 1) {
            throw new ServiceException("只有未签署合同的租赁记录或已取消的租赁记录才可以删除");
        }

        lease.setDelStatus(1);
        lease.setUpdateBy(sysUser.getNickName());
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        // 删除对应的合同
        contractMapper.update(q -> q
                .set(FlatContract::getDelStatus, 1)
                .set(FlatContract::getUpdateBy, sysUser.getNickName())
                .set(FlatContract::getUpdateTime, LocalDateTime.now())
                .eq(FlatContract::getId, lease.getContractId())
                .eq(FlatContract::getDelStatus, 0)
        );

        // 删除支付计划
        payPlanMapper.update(q -> q
                .set(FlatPayPlan::getDelStatus, 1)
                .set(FlatPayPlan::getUpdateBy, sysUser.getNickName())
                .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
        );

        // 找不到房源的正在进行中的租赁记录
        if(leaseMapper.selectCount(q -> q
            .eq(FlatLease::getDelStatus, 0)
            .eq(FlatLease::getRoomId, lease.getRoomId())
            .in(FlatLease::getStatus, List.of(0, 2, 3, 5, 7))
        ) <= 0) {
            // 恢复房源的状态
            roomMapper.update(q -> q
                .set(FlatRoom::getStatus, 0)
                .set(FlatRoom::getUpdateBy, sysUser.getNickName())
                .set(FlatRoom::getUpdateTime, LocalDateTime.now())
                .eq(FlatRoom::getId, lease.getRoomId())
                .eq(FlatRoom::getDelStatus, 0)
                .eq(FlatRoom::getStatus, 1)
            );
        }
    }

    /**
     * 作废租赁记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void invalid(Long leaseId, SysUser sysUser) {
        // 判断状态
        FlatLease lease = leaseMapper.selectById(leaseId);
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        // 判断租赁记录是否为续租记录
        if (lease.getRenewFlag() == 1) {
            // 获取租赁记录对应的合同
            FlatContract contract = contractMapper.selectById(lease.getContractId());
            if (contract == null || contract.getDelStatus() == 1) {
                throw new ServiceException("合同不存在");
            }

            // 判断合同是否已签署或已完成
            if (contract.getStatus() == 4 || contract.getStatus() == 6) {
                // 已签署或已完成，执行正常作废流程
                doInvalid(lease, sysUser);
            } else {
                // 未签署，恢复原租赁记录状态
                // 1. 作废当前租赁记录
                leaseMapper.update(q -> q
                        .set(FlatLease::getInvalidFlag, 1)
                        .set(FlatLease::getInvalidTime, LocalDateTime.now())
                        .set(FlatLease::getInvalidSysUserId, sysUser.getUserId())
                        .set(FlatLease::getUpdateBy, sysUser.getNickName())
                        .set(FlatLease::getUpdateTime, LocalDateTime.now())
                        .eq(FlatLease::getId, leaseId)
                        .eq(FlatLease::getDelStatus, 0)
                        .eq(FlatLease::getInvalidFlag, 0)
                );

                // 2. 恢复原租赁记录状态为履约中并清除续租相关的字段数据
                FlatLease oldLease = leaseMapper.selectById(lease.getRenewFromLeaseId());
                if (oldLease != null && oldLease.getDelStatus() == 0) {
                    oldLease.setStatus(2); // 恢复为履约中
                    oldLease.setRenewToLeaseId(null);
                    oldLease.setRenewApplyTime(null);
                    oldLease.setUpdateBy(sysUser.getNickName());
                    oldLease.setUpdateTime(LocalDateTime.now());
                    leaseMapper.updateById(oldLease);
                }

                // 3. 作废当前租赁记录对应的合同
                contractMapper.update(q -> q
                        .set(FlatContract::getInvalidFlag, 1)
                        .set(FlatContract::getInvalidTime, LocalDateTime.now())
                        .set(FlatContract::getInvalidSysUserId, sysUser.getUserId())
                        .set(FlatContract::getUpdateBy, sysUser.getNickName())
                        .set(FlatContract::getUpdateTime, LocalDateTime.now())
                        .eq(FlatContract::getId, lease.getContractId())
                        .eq(FlatContract::getDelStatus, 0)
                        .eq(FlatContract::getInvalidFlag, 0)
                );

                // 4. 作废当前租赁记录对应的未支付的付款计划
                payPlanMapper.update(q -> q
                        .set(FlatPayPlan::getInvalidFlag, 1)
                        .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                        .set(FlatPayPlan::getUpdateBy, sysUser.getNickName())
                        .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                        .eq(FlatPayPlan::getLeaseId, leaseId)
                        .eq(FlatPayPlan::getDelStatus, 0)
                        .eq(FlatPayPlan::getInvalidFlag, 0)
                );

                // 5. 房源状态保持为使用状态，不需要修改
            }
        } else if (lease.getSubletFlag() == 1) { // 判断租赁记录是否为转租记录
            // 获取租赁记录对应的合同
            FlatContract contract = contractMapper.selectById(lease.getContractId());
            if (contract == null || contract.getDelStatus() == 1) {
                throw new ServiceException("合同不存在");
            }

            // 判断合同是否已签署或已完成
            if (contract.getStatus() == 4 || contract.getStatus() == 6) {
                // 已签署或已完成，执行正常作废流程
                doInvalid(lease, sysUser);
            } else {
                // 未签署，恢复原租赁记录状态
                // 1. 作废当前租赁记录
                leaseMapper.update(q -> q
                        .set(FlatLease::getInvalidFlag, 1)
                        .set(FlatLease::getInvalidTime, LocalDateTime.now())
                        .set(FlatLease::getInvalidSysUserId, sysUser.getUserId())
                        .set(FlatLease::getUpdateBy, sysUser.getNickName())
                        .set(FlatLease::getUpdateTime, LocalDateTime.now())
                        .eq(FlatLease::getId, leaseId)
                        .eq(FlatLease::getDelStatus, 0)
                        .eq(FlatLease::getInvalidFlag, 0)
                );

                // 2. 恢复原租赁记录状态为履约中并清除转租相关的字段数据
                FlatLease oldLease = leaseMapper.selectById(lease.getSubletFromLeaseId());
                if (oldLease != null && oldLease.getDelStatus() == 0) {
                    oldLease.setStatus(2); // 恢复为履约中
                    oldLease.setSubletToKeepLive(null);
                    oldLease.setSubletToLeaseId(null);
                    oldLease.setSubletApplyTime(null);
                    oldLease.setUpdateBy(sysUser.getNickName());
                    oldLease.setUpdateTime(LocalDateTime.now());
                    leaseMapper.updateById(oldLease);
                }

                // 3. 作废当前租赁记录对应的合同
                contractMapper.update(q -> q
                        .set(FlatContract::getInvalidFlag, 1)
                        .set(FlatContract::getInvalidTime, LocalDateTime.now())
                        .set(FlatContract::getInvalidSysUserId, sysUser.getUserId())
                        .set(FlatContract::getUpdateBy, sysUser.getNickName())
                        .set(FlatContract::getUpdateTime, LocalDateTime.now())
                        .eq(FlatContract::getId, lease.getContractId())
                        .eq(FlatContract::getDelStatus, 0)
                        .eq(FlatContract::getInvalidFlag, 0)
                );

                // 4. 作废当前租赁记录对应的未支付的付款计划
                payPlanMapper.update(q -> q
                        .set(FlatPayPlan::getInvalidFlag, 1)
                        .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                        .set(FlatPayPlan::getUpdateBy, sysUser.getNickName())
                        .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                        .eq(FlatPayPlan::getLeaseId, leaseId)
                        .eq(FlatPayPlan::getDelStatus, 0)
                        .eq(FlatPayPlan::getInvalidFlag, 0)
                );

                // 5. 房源状态保持为使用状态，不需要修改
            }
        } else {
            // 非续租或转租记录，执行正常作废流程
            doInvalid(lease, sysUser);
        }
    }

    /**
     * 执行作废操作
     */
    private void doInvalid(FlatLease lease, SysUser sysUser) {
        leaseMapper.update(q -> q
                .set(FlatLease::getInvalidFlag, 1)
                .set(FlatLease::getInvalidTime, LocalDateTime.now())
                .set(FlatLease::getInvalidSysUserId, sysUser.getUserId())
                .set(FlatLease::getUpdateBy, sysUser.getNickName())
                .set(FlatLease::getUpdateTime, LocalDateTime.now())
                .eq(FlatLease::getId, lease.getId())
                .eq(FlatLease::getDelStatus, 0)
                .eq(FlatLease::getInvalidFlag, 0)
        );

        contractMapper.update(q -> q
                .set(FlatContract::getInvalidFlag, 1)
                .set(FlatContract::getInvalidTime, LocalDateTime.now())
                .set(FlatContract::getInvalidSysUserId, sysUser.getUserId())
                .set(FlatContract::getUpdateBy, sysUser.getNickName())
                .set(FlatContract::getUpdateTime, LocalDateTime.now())
                .eq(FlatContract::getId, lease.getContractId())
                .eq(FlatContract::getDelStatus, 0)
                .eq(FlatContract::getInvalidFlag, 0)
        );

        payPlanMapper.update(q -> q
                .set(FlatPayPlan::getInvalidFlag, 1)
                .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                .set(FlatPayPlan::getUpdateBy, sysUser.getNickName())
                .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getInvalidFlag, 0)
        );

        roomMapper.update(q -> q
                .set(FlatRoom::getStatus, 0)
                .set(FlatRoom::getUpdateBy, sysUser.getNickName())
                .set(FlatRoom::getUpdateTime, LocalDateTime.now())
                .eq(FlatRoom::getId, lease.getRoomId())
                .eq(FlatRoom::getDelStatus, 0)
                .eq(FlatRoom::getStatus, 1)
        );
    }

    /**
     * 租赁记录导出
     *
     * @param req 查询条件
     * @return 租赁记录导出列表
     */
    public List<LeaseExportResp> exportList(LeaseQueryReq req) {
        return leaseMapper.selectExportList(req);
    }

}
