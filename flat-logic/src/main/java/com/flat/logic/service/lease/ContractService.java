package com.flat.logic.service.lease;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.logic.service.support.MessageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.common.utils.spring.SpringUtils;
import com.flat.logic.dto.req.lease.ContractQueryReq;
import com.flat.logic.dto.req.lease.ContractVerifyReq;
import com.flat.logic.dto.resp.lease.ContractDetailResp;
import com.flat.logic.dto.resp.lease.ContractListResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatContractLog;
import com.flat.logic.entity.lease.FlatContractProp;
import com.flat.logic.entity.lease.FlatPayPlan;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatContractLogMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatContractPropMapper;
import com.flat.logic.mapper.lease.FlatPayPlanMapper;
import com.flat.logic.model.ContractProp;
import com.flat.logic.model.SignFlowResult;
import com.flat.logic.service.cloud.ESignCloudService;
import com.flat.system.entity.SysUser;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.LocalDateTimeUtil;
import jakarta.annotation.Resource;

/**
 * 公寓合同Service业务层处理
 */
@Service
public class ContractService {

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatContractMapper contractMapper;

    @Resource
    private FlatContractPropMapper contractPropMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private FlatContractLogMapper contractLogMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private ESignCloudService eSignCloudService;

    @Resource
    private FlatPayPlanMapper payPlanMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    /**
     * 查询公寓合同列表
     */
    public TablePage<ContractListResp> queryRespPage(ContractQueryReq req) {
        return PageUtils.paginate(() -> contractMapper.selectRespList(req));
    }

    /**
     * 添加公寓合同
     */
    public FlatContract addContract(
            FlatRoom room, FlatCompany company, CusUser cusUser, Integer type,
            LocalDate deliveryDate, LocalDate beginDate, Integer monthCount,
            BigDecimal monthMoney, BigDecimal depositMoney, BigDecimal monthPropertyMoney,
            Integer liveCount, Integer durationType, Integer payPeriod,
            String contactName, String contactPhoneNumber, String deliveryAddress,
            FlatPayPlan firstRentPlan, List<ContractProp> props,
            MaiUser maiUser
    ) {
        LocalDate endDate = beginDate.plusMonths(monthCount).minusDays(1);

        // 签约时间
        LocalDateTime signTime = LocalDateTime.now();

        // 生成合同属性值列表
        putContractProps(
            props, room, company, cusUser, 
            deliveryDate, beginDate, monthCount,
            monthMoney, depositMoney, monthPropertyMoney,
            liveCount, durationType, payPeriod,
            contactName, contactPhoneNumber, deliveryAddress,
            firstRentPlan, signTime
        );

        String number = (room.getKind() == 0 ? "GY" : "SP") + LocalDateTimeUtil.format(signTime, "yyyyMMddHHmmss") + (int) (Math.random() * 9);

        FlatContract contract = new FlatContract();
        contract.setFlatId(room.getFlatId());
        contract.setRoomId(room.getId());
        contract.setRoomName(room.getName());
        contract.setCusId(cusUser.getUserId());
        contract.setCusRealName(cusUser.getRealName());
        contract.setCusPhoneNumber(cusUser.getPhoneNumber());
        contract.setButlerId(maiUser.getUserId());
        contract.setCompanyId(company.getId());
        contract.setType(type);
        contract.setNumber(number);
        contract.setName(company.getESignHouseTemplateName());
        contract.setBeginTime(LocalDateTimeUtil.beginOfDay(beginDate));
        contract.setEndTime(LocalDateTimeUtil.endOfDay(endDate));
        contract.setDeliveryTime(deliveryDate == null ? null : LocalDateTimeUtil.beginOfDay(deliveryDate));
        contract.setPayPeriod(payPeriod);
        contract.setMonthMoney(monthMoney == null ? null : monthMoney);
        contract.setMonthCount(monthCount);
        contract.setTotalMoney(monthMoney == null ? null : monthMoney.multiply(BigDecimal.valueOf(monthCount)));
        contract.setDepositMoney(depositMoney);
        contract.setMonthPropertyMoney(monthPropertyMoney);
        contract.setSignMode(0);
        if(company.getSymbol().equalsIgnoreCase("hn") && durationType == 0) {
            contract.setPayMode(1);
            contract.setSettleChannel(1);
        } else {
            contract.setPayMode(0);
            contract.setSettleChannel(0);
        }
        contract.setDurationType(durationType);
        contract.setContactName(contactName);
        contract.setContactPhoneNumber(contactPhoneNumber);
        contract.setDeliveryAddress(deliveryAddress);
        contract.setVersion(2);
        contract.setLiveCount(liveCount);
        contract.setStatus(0);
        contract.setCreateBy(maiUser.getUsername());
        contract.setCreateTime(LocalDateTime.now());
        contract.setDelStatus(0);
        contract.setInvalidFlag(0);
        contractMapper.insert(contract);

        FlatContractProp contractProp = new FlatContractProp();
        contractProp.setContractId(contract.getId());
        contractProp.setType(1);
        contractProp.setPropsData(JSON.toJSONString(props));
        contractProp.setCreateBy(maiUser.getUsername());
        contractProp.setCreateTime(LocalDateTime.now());
        contractProp.setDelStatus(0);
        contractPropMapper.insert(contractProp);

        FlatContractLog log = new FlatContractLog();
        log.setFlatId(room.getFlatId());
        log.setContractId(contract.getId());
        log.setType(0);
        log.setTypeName("合同发起成功");
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getUsername());
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getUsername());
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());
        contractLogMapper.insert(log);

        return contract;
    }

    public void verifyPass(FlatRoom room, FlatContract contract, MaiUser maiUser) {
        verifyPass(room, contract, 1, maiUser.getUserId(), maiUser.getUsername());
    }

    public void verifyPass(FlatRoom room, FlatContract contract, SysUser sysUser) {
        verifyPass(room, contract, 0, sysUser.getUserId(), sysUser.getUsername());
    }

    public void verifyReject(FlatRoom room, FlatContract contract, String refuse, MaiUser maiUser) {
        verifyReject(room, contract, refuse, 1, maiUser.getUserId(), maiUser.getUsername());
    }

    public void verifyReject(FlatRoom room, FlatContract contract, String refuse, SysUser sysUser) {
        verifyReject(room, contract, refuse, 0, sysUser.getUserId(), sysUser.getUsername());
    }

    /**
     * 审核通过
     */
    private void verifyPass(FlatRoom room, FlatContract contract, Integer client, Long userId, String username) {
        // 查询属性值
        FlatContractProp contractProp = contractPropMapper.selectFirst((q) -> q
                .eq(FlatContractProp::getContractId, contract.getId())
                .eq(FlatContractProp::getType, 1)
                .eq(FlatContractProp::getDelStatus, 0)
        );
        if (contractProp == null || contractProp.getDelStatus() == 1) {
            throw new ServiceException("合同属性未找到");
        }

        SignFlowResult signFlowResult = eSignCloudService.createSignFlow(
                room, contract,
                JSON.parseArray(contractProp.getPropsData(), ContractProp.class)
        );

        contract.setStatus(3); // 设置为已确认
        contract.setUpdateTime(LocalDateTime.now());
        contract.setUpdateBy(username);
        contract.setFlowId(signFlowResult.getFlowId());
        contract.setSignUrl(signFlowResult.getUserSignUrl());
        contract.setVerifyUserClient(client);
        contract.setVerifyUserId(userId);
        contract.setVerifyTime(LocalDateTime.now());
        contractMapper.updateById(contract);

        FlatContractLog log = new FlatContractLog();
        log.setFlatId(contract.getFlatId());
        log.setContractId(contract.getId());
        log.setType(3);
        log.setTypeName("合同审核通过");
        log.setOperateId(userId);
        log.setOperateName(username);
        log.setDelStatus(0);
        log.setCreateBy(username);
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());
        contractLogMapper.insert(log);
    }

    /**
     * 审核驳回
     */
    private void verifyReject(FlatRoom room, FlatContract contract, String refuse, Integer client, Long userId, String username) {
        if (StringUtils.isBlank(refuse)) {
            throw new ServiceException("请填写拒绝原因");
        }
        contract.setStatus(2); // 设置为已驳回
        contract.setUpdateTime(LocalDateTime.now());
        contract.setUpdateBy(username);
        contract.setRefuse(refuse);
        contract.setVerifyUserClient(client);
        contract.setVerifyUserId(userId);
        contract.setVerifyTime(LocalDateTime.now());
        contractMapper.updateById(contract);

        FlatContractLog log = new FlatContractLog();
        log.setFlatId(contract.getFlatId());
        log.setContractId(contract.getId());
        log.setType(2);
        log.setTypeName("合同已驳回");
        log.setOperateId(userId);
        log.setOperateName(username);
        log.setRefuse(refuse);
        log.setDelStatus(0);
        log.setCreateBy(username);
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());
        contractLogMapper.insert(log);
    }


    // 计算并返回合同属性值列表
    private void putContractProps(
        List<ContractProp> props,
        FlatRoom room, FlatCompany company, CusUser cusUser,
        LocalDate deliveryDate,
        LocalDate beginDate, Integer monthCount,
        BigDecimal monthMoney, BigDecimal depositMoney, BigDecimal monthPropertyMoney,
        Integer liveCount, Integer durationType, Integer payPeriod,
        String contactName, String contactPhoneNumber,String deliveryAddress,
        FlatPayPlan firstRentPlan, LocalDateTime signTime
    ) {
        LocalDate endDate = beginDate.plusMonths(monthCount).minusDays(1);
        for (ContractProp prop : props) {
            switch (prop.getType()) {
                case "text", "number":
                    if(StringUtils.isBlank(prop.getValue())) {
                        prop.setValue("");
                    }
                    break;
                case "check":
                    if(StringUtils.isBlank(prop.getValue())) {
                        prop.setValue("0");
                    }
                    break;
                case "cusTrueName":
                    if (StringUtils.isBlank(cusUser.getRealName())) {
                        throw new ServiceException("用户未实名认证");
                    }
                    prop.setValue(cusUser.getRealName());
                    break;
                case "cusIdCard":
                    if (StringUtils.isBlank(cusUser.getIdCard())) {
                        throw new ServiceException("用户未实名认证");
                    }
                    prop.setValue(cusUser.getIdCard());
                    break;
                case "cusPhone":
                    if (StringUtils.isBlank(cusUser.getPhoneNumber())) {
                        throw new ServiceException("用户未绑定手机号");
                    }
                    prop.setValue(cusUser.getPhoneNumber());
                    break;
                case "contactTrueName":
                    if (StringUtils.isNotBlank(contactName)) {
                        prop.setValue(contactName);
                    }
                    break;
                case "contactPhone":
                    if (StringUtils.isNotBlank(contactPhoneNumber)) {
                        prop.setValue(contactPhoneNumber);
                    }
                    break;
                case "deliveryAddress":
                    if (StringUtils.isNotBlank(deliveryAddress)) {
                        prop.setValue(deliveryAddress);
                    }
                    break;
                case "cusIdCardFront":
                    if (StringUtils.isBlank(cusUser.getFrontUrl())) {
                        throw new ServiceException("用户未实名认证");
                    }
                    prop.setValue(eSignCloudService.uploadFileByUrl(cusUser.getFrontUrl()));
                    break;
                case "cusIdCardReverse":
                    if (StringUtils.isBlank(cusUser.getReverseUrl())) {
                        throw new ServiceException("用户未实名认证");
                    }
                    prop.setValue(eSignCloudService.uploadFileByUrl(cusUser.getReverseUrl()));
                    break;
                case "compName":
                    prop.setValue(company.getCompanyName());
                    break;
                case "signYear":
                    prop.setValue(String.valueOf(signTime.getYear()));
                    break;
                case "signMonth":
                    prop.setValue(String.valueOf(signTime.getMonthValue()));
                    break;
                case "signDay":
                    prop.setValue(String.valueOf(signTime.getDayOfMonth()));
                    break;
                case "beginYear":
                    prop.setValue(String.valueOf(beginDate.getYear()));
                    break;
                case "beginMonth":
                    prop.setValue(String.valueOf(beginDate.getMonthValue()));
                    break;
                case "beginDay":
                    prop.setValue(String.valueOf(beginDate.getDayOfMonth()));
                    break;
                case "endYear":
                    prop.setValue(String.valueOf(endDate.getYear()));
                    break;
                case "endMonth":
                    prop.setValue(String.valueOf(endDate.getMonthValue()));
                    break;
                case "endDay":
                    prop.setValue(String.valueOf(endDate.getDayOfMonth()));
                    break;
                case "deliveryYear":
                    prop.setValue(String.valueOf(deliveryDate.getYear()));
                    break;
                case "deliveryMonth":
                    prop.setValue(String.valueOf(deliveryDate.getMonthValue()));
                    break;
                case "deliveryDay":
                    prop.setValue(String.valueOf(deliveryDate.getDayOfMonth()));
                    break;
                case "price":
                    prop.setValue(monthMoney == null ? "" :String.valueOf(monthMoney));
                    break;
                case "bigPrice":
                    prop.setValue(NumberChineseFormatter.format(monthMoney.doubleValue(), true, true));
                    break;
                case "deposit":
                    prop.setValue(depositMoney == null ? "" : String.valueOf(depositMoney));
                    break;
                case "bigDeposit":
                    prop.setValue(NumberChineseFormatter.format(depositMoney.doubleValue(), true, true));
                    break;
                case "propertyFee":
                    prop.setValue(monthPropertyMoney == null ? "" : String.valueOf(monthPropertyMoney));
                    break;
                case "bigPropertyFee":
                    prop.setValue(NumberChineseFormatter.format(monthPropertyMoney.doubleValue(), true, true));
                    break;
                case "yearPropertyFee":
                    prop.setValue(monthPropertyMoney == null ? "" : String.valueOf(monthPropertyMoney.multiply(BigDecimal.valueOf(12))));
                    break;
                case "bigYearPropertyFee":
                    prop.setValue(NumberChineseFormatter.format(monthPropertyMoney.multiply(BigDecimal.valueOf(12)).doubleValue(), true, true));
                    break;
                case "room":
                    prop.setValue(room.getName());
                    break;
                case "liveNumber":
                    prop.setValue(String.valueOf(liveCount));
                    break;
                case "payPeriod":
                    switch (payPeriod) {
                        case 0:
                            prop.setValue("月度");
                            break;
                        case 1:
                            prop.setValue("季度");
                            break;
                        case 2:
                            prop.setValue("半年度");
                            break;
                        case 3:
                            prop.setValue("年度");
                            break;
                        default:
                            prop.setValue("");
                            break;
                    }
                    break;
                case "firstPeriodBeginYear":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getBeginTime().getYear()));
                    }
                    break;
                case "firstPeriodBeginMonth":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getBeginTime().getMonthValue()));
                    }
                    break;
                case "firstPeriodBeginDay":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getBeginTime().getDayOfMonth()));
                    }
                    break;
                case "firstPeriodEndYear":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getEndTime().getYear()));
                    }
                    break;
                case "firstPeriodEndMonth":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getEndTime().getMonthValue()));
                    }
                    break;
                case "firstPeriodEndDay":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getEndTime().getDayOfMonth()));
                    }
                    break;
                case "firstPeriodMoney":
                    if(firstRentPlan != null) {
                        prop.setValue(String.valueOf(firstRentPlan.getMoney()));
                    }
                    break;
                case "firstPeriodBigMoney":
                    if(firstRentPlan != null) {
                        prop.setValue(NumberChineseFormatter.format(firstRentPlan.getMoney().doubleValue(), true, true));
                    }
                    break;
                default:
                    break;
            }
        }
    }


    public ContractDetailResp queryRespDetail(Long id) {
        FlatContract contract = contractMapper.selectById(id);
        if (contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同未找到");
        }

        FlatRoom room = roomMapper.selectById(contract.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源未找到");
        }

        CusUser cusUser = cusUserMapper.selectById(contract.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        MaiUser maiUser = maiUserMapper.selectById(contract.getButlerId());
        if (maiUser == null || maiUser.getDelStatus() == 1) {
            throw new ServiceException("管家未找到");
        }

        FlatCompany company = companyMapper.selectById(contract.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        FlatContractProp contractProp = contractPropMapper.selectFirst((q) -> q
                .eq(FlatContractProp::getContractId, contract.getId())
                .eq(FlatContractProp::getType, 1)
                .eq(FlatContractProp::getDelStatus, 0)
        );

        List<FlatPayPlan> rentPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, contract.getLeaseId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 1)
        );

        List<FlatPayPlan> energyPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, contract.getLeaseId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 2)
        );

        List<FlatPayPlan> shopPropertyPlans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, contract.getLeaseId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getType, 3)
        );

        ContractDetailResp detailResp = new ContractDetailResp();
        BeanUtils.copyProperties(contract, detailResp);
        detailResp.setRoom(room);
        detailResp.setCusUser(cusUser);
        detailResp.setMaiUser(maiUser);
        detailResp.setCompanyName(company.getCompanyName());
        if(contractProp != null) {
            detailResp.setProps(JSON.parseArray(contractProp.getPropsData(), ContractProp.class));
        }
        detailResp.setRentPlans(rentPlans);
        detailResp.setEnergyPlans(energyPlans);
        detailResp.setShopPropertyPlans(shopPropertyPlans);

        return detailResp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void verify(ContractVerifyReq req, SysUser sysUser) {
        // 查询合同
        FlatContract contract = contractMapper.selectById(req.getContractId());
        if (contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同不存在");
        }

        // 检查合同状态是否为待审核
        if (contract.getStatus() != 0) {
            throw new ServiceException("合同状态不正确，只能审核待签约的合同");
        }

        // 查询房间
        FlatRoom room = roomMapper.selectById(contract.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        if (req.getStatus() == 2) { // 审核驳回
            verifyReject(room, contract, req.getRefuse(), sysUser);

            if (contract.getType() == 1) { // 公寓房源租赁合同
                SpringUtils.getBean(LeaseService.class)
                    .contractVerifyRejected(contract, room, 0, sysUser.getUserId(), sysUser.getUsername());
            } else if (contract.getType() == 2) { // 同住人租赁合同
                SpringUtils.getBean(LiveService.class)
                    .contractVerifyRejected(contract, room, 0, sysUser.getUserId(), sysUser.getUsername());
            }
            return;
        }

        // 审核通过
        verifyPass(room, contract, sysUser);

        if (contract.getType() == 1) { // 公寓房源租赁合同
            SpringUtils.getBean(LeaseService.class)
                .contractVerifyPassed(contract, room, 0, sysUser.getUserId(), sysUser.getUsername());
        } else if (contract.getType() == 2) { // 同住人租赁合同
            SpringUtils.getBean(LiveService.class)
                .contractVerifyPassed(contract, room, 0, sysUser.getUserId(), sysUser.getUsername());
        }
    }

    /**
     * 审核合同
     *
     * @param req 审核请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void verify(ContractVerifyReq req, MaiUser maiUser) {
        // 从数据库中获取最新的用户数据，避免redis中没有最新的权限数据
        MaiUser selfUser = maiUserMapper.selectFirst(q -> q
                .eq(MaiUser::getUserId, maiUser.getUserId())
                .eq(MaiUser::getDelStatus, 0)
        );
        if(selfUser == null || StringUtils.isBlank(selfUser.getPermission())) {
            throw new ServiceException("您没有权限审核合同");
        }

        List<String> permissions = JSON.parseArray(selfUser.getPermission(), String.class);
        if(!permissions.contains("contract-verify")) {
            throw new ServiceException("您没有权限审核合同");
        }

        // 查询合同
        FlatContract contract = contractMapper.selectById(req.getContractId());
        if (contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同不存在");
        }

        // 检查合同状态是否为待审核
        if (contract.getStatus() != 0) {
            throw new ServiceException("合同状态不正确，只能审核待签约的合同");
        }

        // 查询房间
        FlatRoom room = roomMapper.selectById(contract.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        if (req.getStatus() == 2) { // 审核驳回
            verifyReject(room, contract, req.getRefuse(), maiUser);

            if (contract.getType() == 1) { // 公寓房源租赁合同
                SpringUtils.getBean(LeaseService.class)
                    .contractVerifyRejected(contract, room, 1, maiUser.getUserId(), maiUser.getNickName());
            } else if (contract.getType() == 2) { // 同住人租赁合同
                SpringUtils.getBean(LiveService.class)
                    .contractVerifyRejected(contract, room, 1, maiUser.getUserId(), maiUser.getNickName());
            }

            return;
        }

        // 审核通过
        verifyPass(room, contract, maiUser);

        if (contract.getType() == 1) { // 公寓房源租赁合同
            SpringUtils.getBean(LeaseService.class)
                .contractVerifyPassed(contract, room, 1, maiUser.getUserId(), maiUser.getNickName());
        } else if (contract.getType() == 2) { // 同住人租赁合同
            SpringUtils.getBean(LiveService.class)
                .contractVerifyPassed(contract, room, 1, maiUser.getUserId(), maiUser.getNickName());
        }
    }

    //处理用户签约
    @Transactional(rollbackFor = Exception.class)
    public void handleUserSign(String signFlowId, Long operateTimestamp, Integer signResult) {
        FlatContract contract = contractMapper.selectFirst((q) -> q
                .eq(FlatContract::getDelStatus, 0)
                .eq(FlatContract::getFlowId, signFlowId)
        );
        if (contract == null) {
            throw new ServiceException("合同不存在");
        }

        if(contract.getStatus() != 3) {
            throw new ServiceException("合同状态不正确");
        }

        FlatRoom room = roomMapper.selectById(contract.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        CusUser cusUser = cusUserMapper.selectById(contract.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在");
        }

        LocalDateTime operateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(operateTimestamp), ZoneOffset.ofHours(8));

        if(signResult == 2) { // 已完成签署
            contract.setStatus(4);
            contract.setSignFinishTime(operateTime);
            contract.setUpdateTime(LocalDateTime.now());
            contract.setUpdateBy(cusUser.getNickName());
            contractMapper.updateById(contract);

            FlatContractLog log = new FlatContractLog();
            log.setFlatId(contract.getFlatId());
            log.setContractId(contract.getId());
            log.setType(4);
            log.setTypeName("用户确认签署合同");
            log.setOperateId(cusUser.getUserId());
            log.setOperateName(cusUser.getNickName());
            log.setDelStatus(0);
            log.setCreateBy(cusUser.getNickName());
            log.setCreateTime(contract.getUpdateTime());
            contractLogMapper.insert(log);

            if(contract.getType() == 1) { // 公寓房源租赁合同
                SpringUtils.getBean(LeaseService.class).contractSignPassed(contract, room, cusUser, operateTime);
            } else if(contract.getType() == 2) { // 同住人租赁合同
                SpringUtils.getBean(LiveService.class).signPassed(contract, room, cusUser, operateTime);
            }

            // 合同签署成功提醒
            if (StringUtils.isNotBlank(cusUser.getWxPublicOpenId())) {
                weChatPublicMessageService.sendMessage(
                        cusUser,
                        "6qb8pgiYzjh-ZxQ5q-ZcWEJAia0UeASPiLoyQahT2o8",
                        null,
                        Map.of(
                                "thing1", Map.of("value", room.getName()),
                                "character_string2", Map.of("value", contract.getNumber()),
                                "time4", Map.of("value", contract.getBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ~ " + contract.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))),
                                "thing5", Map.of("value", cusUser.getRealName())
                        )
                );
            }

        } else if(signResult == 4) { // 已拒绝签署
            contract.setStatus(5);
            contract.setRefuse("用户拒绝签约");
            contract.setUpdateTime(LocalDateTime.now());
            contract.setUpdateBy(cusUser.getNickName());
            contractMapper.updateById(contract);

            FlatContractLog log = new FlatContractLog();
            log.setFlatId(contract.getFlatId());
            log.setContractId(contract.getId());
            log.setType(5);
            log.setTypeName("用户拒绝签署合同");
            log.setOperateId(cusUser.getUserId());
            log.setOperateName(cusUser.getNickName());
            log.setDelStatus(0);
            log.setRefuse("用户拒绝签约");
            log.setCreateBy(cusUser.getNickName());
            log.setCreateTime(contract.getUpdateTime());
            contractLogMapper.insert(log);

            if(contract.getType() == 1) { // 公寓房源租赁合同
                SpringUtils.getBean(LeaseService.class).contractSignRejected(contract, room, cusUser, operateTime);
            } else if(contract.getType() == 2) { // 同住人租赁合同
                SpringUtils.getBean(LiveService.class).signRejected(contract, room, cusUser, operateTime);
            }
        }


    }

}
