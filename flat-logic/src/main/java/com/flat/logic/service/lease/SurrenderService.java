package com.flat.logic.service.lease;

import cn.hutool.core.util.IdUtil;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.commons.BillConstants;
import com.flat.logic.dto.req.lease.SurrenderFundReq;
import com.flat.logic.dto.req.lease.SurrenderQueryReq;
import com.flat.logic.dto.resp.lease.SurrenderDetailResp;
import com.flat.logic.dto.resp.lease.SurrenderListResp;
import com.flat.logic.entity.account.FlatRelation;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.*;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.account.FlatRelationMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.*;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.model.RefundMoney;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SurrenderService {
    
    @Resource
    private FlatSurrenderMapper surrenderMapper;

    @Resource
    private FlatContractMapper contractMapper;

    @Resource
    private FlatPayPlanMapper payPlanMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatRelationMapper relationMapper;

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private SysConfigService sysConfigService;

    public SurrenderDetailResp queryDetail(Long id) {
        FlatSurrender surrender = surrenderMapper.selectById(id);
        if(surrender == null || surrender.getDelStatus() == 1) {
            throw new ServiceException("退租信息不存在");
        }

        FlatRoom room = roomMapper.selectById(surrender.getRoomId());
        if(room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源信息不存在");
        }

        SurrenderDetailResp resp = new SurrenderDetailResp();
        BeanUtils.copyProperties(surrender, resp);
        resp.setRoomName(room.getName());

        List<FlatMakeLog> logs = makeLogMapper.selectList(q -> q
                .eq(FlatMakeLog::getSourceId, surrender.getId())
                .eq(FlatMakeLog::getType, "surrender")
                .eq(FlatMakeLog::getDelStatus, 0)
        );
        resp.setLogs(logs);

        return resp;
    }

    public TablePage<SurrenderListResp> queryRespPage(SurrenderQueryReq queryReq) {
        return PageUtils.paginate(() -> surrenderMapper.selectRespList(queryReq));
    }

    /**
     * 计算退款金额
     */
    public RefundMoney calculateRefundMoney(Long id) {
        FlatSurrender surrender = surrenderMapper.selectById(id);
        if(surrender == null || surrender.getDelStatus() == 1) {
            throw new ServiceException("退租信息不存在");
        }

        if(surrender.getStatus() != 0) {
            throw new ServiceException("只有待审核状态的退租信息才能计算退款金额");
        }

        FlatLease lease = leaseMapper.selectById(surrender.getLeaseId());
        if(lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁信息不存在");
        }

        if(lease.getContractVersion() < 2) { // 老的租赁版本不支持线上交付，故不支持自动计算退款金额
            throw new ServiceException("房租支付方式不支持自动计算退款金额");
        }

        if(lease.getSubletFlag() == 1) { // 被转租的租赁不支持自动计算退款金额
            throw new ServiceException("被转租的租赁不支持自动计算退款金额");
        }

        RefundMoney refundMoney = new RefundMoney();

        // 查询已支付的租金缴费计划
        List<FlatPayPlan> rentPlans = payPlanMapper.selectList((q) -> q
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getPayStatus, 1)
                .eq(FlatPayPlan::getType, 1)
        );
        if(rentPlans.isEmpty()) {
            refundMoney.setRentMoney(BigDecimal.ZERO);
        } else {
            BigDecimal money = BigDecimal.ZERO;
            for(FlatPayPlan plan : rentPlans) {
                if(LocalDateTime.now().isBefore(plan.getBeginTime())) { // 该支付计划未开始，需要退还全额
                    money = money.add(plan.getMoney());
                } else if(LocalDateTime.now().isBefore(plan.getEndTime())) { // 该支付计划已开始，但未结束，需要退还剩余金额，剩余金额 = (总金额/总天数)*剩余天数
                    long remainDays = plan.getEndTime().toLocalDate().toEpochDay() - LocalDateTime.now().toLocalDate().toEpochDay();
                    long totalDays = plan.getEndTime().toLocalDate().toEpochDay() - plan.getBeginTime().toLocalDate().toEpochDay();
                    BigDecimal dayMoney = plan.getMoney().divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP);
                    money = money.add(dayMoney.multiply(BigDecimal.valueOf(remainDays)));
                }
            }
            refundMoney.setRentMoney(money);
        }

        BigDecimal energyMonthMoney = new BigDecimal(sysConfigService.queryConfigByKey("ENERGY_MONTH_MONEY"));

        // 查询已支付的能耗费缴费计划
        List<FlatPayPlan> energyPlans = payPlanMapper.selectList((q) -> q
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getPayStatus, 1)
                .eq(FlatPayPlan::getType, 2)
        );
        if(energyPlans.isEmpty()) {
            refundMoney.setEnergyMoney(BigDecimal.ZERO);
        } else {
            BigDecimal money = BigDecimal.ZERO;
            for(FlatPayPlan plan : energyPlans) {
                if(LocalDateTime.now().isBefore(plan.getBeginTime())) { // 该支付计划未开始，需要退还全额
                    money = money.add(plan.getMoney());
                } else if(LocalDateTime.now().isBefore(plan.getEndTime())) { // 该支付计划已开始，但未结束，需要退还剩余金额，剩余金额 = 总金额 - (总天数 - 剩余天数) * 5
                    long remainDays = plan.getEndTime().toLocalDate().toEpochDay() - LocalDateTime.now().toLocalDate().toEpochDay();
                    long totalDays = plan.getEndTime().toLocalDate().toEpochDay() - plan.getBeginTime().toLocalDate().toEpochDay();
                    BigDecimal remainMoney = energyMonthMoney.subtract(BigDecimal.valueOf(totalDays).subtract(BigDecimal.valueOf(remainDays)).multiply(BigDecimal.valueOf(5)));
                    money = money.add(remainMoney);
                }
            }
            refundMoney.setEnergyMoney(money);
        }

        // 查询已支付的商铺物业费缴费计划
        List<FlatPayPlan> shopPropertyPlans = payPlanMapper.selectList((q) -> q
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getPayStatus, 1)
                .eq(FlatPayPlan::getType, 3)
        );
        if(shopPropertyPlans.isEmpty()) {
            refundMoney.setPropertyMoney(BigDecimal.ZERO);
        } else {
            BigDecimal money = BigDecimal.ZERO;
            for(FlatPayPlan plan : shopPropertyPlans) {
                if(LocalDateTime.now().isBefore(plan.getBeginTime())) { // 该支付计划未开始，需要退还全额
                    money = money.add(plan.getMoney());
                } else if(LocalDateTime.now().isBefore(plan.getEndTime())) { // 该支付计划已开始，但未结束，需要退还剩余金额，剩余金额 = (总金额/总天数)*剩余天数
                    long remainDays = plan.getEndTime().toLocalDate().toEpochDay() - LocalDateTime.now().toLocalDate().toEpochDay();
                    long totalDays = plan.getEndTime().toLocalDate().toEpochDay() - plan.getBeginTime().toLocalDate().toEpochDay();
                    BigDecimal dayMoney = plan.getMoney().divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP);
                    money = money.add(dayMoney.multiply(BigDecimal.valueOf(remainDays)));
                }
            }
            refundMoney.setPropertyMoney(money);
        }

        if(lease.getWaterAccountBalance() == null) {
            refundMoney.setWaterMoney(BigDecimal.ZERO);
        } else {
            refundMoney.setWaterMoney(lease.getWaterAccountBalance());
        }

        if(lease.getElectricAccountBalance() == null) {
            refundMoney.setElectricMoney(BigDecimal.ZERO);
        } else {
            refundMoney.setElectricMoney(lease.getElectricAccountBalance());
        }

        return refundMoney;
    }


    @Transactional(rollbackFor = Exception.class)
    public void verifyFund(SurrenderFundReq fundReq, SysUser user) {
        FlatSurrender surrender = surrenderMapper.selectById(fundReq.getId());
        if(surrender == null || surrender.getDelStatus() == 1) {
            throw new ServiceException("退租信息不存在");
        }

        if(surrender.getStatus() != 0) {
            throw new ServiceException("非待审核状态");
        }

        FlatContract contract = contractMapper.selectById(surrender.getContractId());
        if(contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同信息不存在");
        }

        surrender.setStatus(2);
        surrender.setUpdateBy(user.getNickName());
        surrender.setPropertyMoney(fundReq.getPropertyCost());
        surrender.setRoomMoney(fundReq.getRoomCost());
        surrender.setEnergyMoney(fundReq.getEnergyCost());
        surrender.setWaterMoney(fundReq.getWaterCost());
        surrender.setDepositMoney(fundReq.getDepositCost());
        surrender.setElectricMoney(fundReq.getElectricCost());
        surrender.setOtherMoney(fundReq.getOtherCost());

        // 计算总费用
        BigDecimal onlineTotalMoney = BigDecimal.ZERO;
        BigDecimal offlineTotalMoney = BigDecimal.ZERO;

        // 20250222->客户需求：取消在线退款的方式，此处解决方式为把在线退款设置为0，钱全部走线下退款
//        if(contract.getVersion() != null && contract.getVersion() == 2 && surrender.getRoomMoney().compareTo(BigDecimal.ZERO) > 0) {
//            onlineTotalMoney = onlineTotalMoney.add(surrender.getRoomMoney());
//            surrender.setRoomMoneyBackType(1);
//        } else {
//            offlineTotalMoney = offlineTotalMoney.add(surrender.getRoomMoney());
//            surrender.setRoomMoneyBackType(2);
//        }
//
//        if(contract.getVersion() != null && contract.getVersion() == 2 && surrender.getEnergyMoney().compareTo(BigDecimal.ZERO) > 0) {
//            onlineTotalMoney = onlineTotalMoney.add(surrender.getEnergyMoney());
//            surrender.setEnergyMoneyBackType(1);
//        } else {
//            offlineTotalMoney = offlineTotalMoney.add(surrender.getEnergyMoney());
//            surrender.setEnergyMoneyBackType(2);
//        }
//
//        if(contract.getVersion() != null && contract.getVersion() == 2 && surrender.getWaterMoney().compareTo(BigDecimal.ZERO) > 0) {
//            onlineTotalMoney = onlineTotalMoney.add(surrender.getWaterMoney());
//            surrender.setWaterMoneyBackType(1);
//        } else {
//            offlineTotalMoney = offlineTotalMoney.add(surrender.getWaterMoney());
//            surrender.setWaterMoneyBackType(2);
//        }
//
//        if(contract.getVersion() != null && contract.getVersion() == 2 && surrender.getElectricMoney().compareTo(BigDecimal.ZERO) > 0) {
//            onlineTotalMoney = onlineTotalMoney.add(surrender.getElectricMoney());
//            surrender.setElectricMoneyBackType(1);
//        } else {
//            offlineTotalMoney = offlineTotalMoney.add(surrender.getElectricMoney());
//            surrender.setElectricMoneyBackType(2);
//        }

        offlineTotalMoney = offlineTotalMoney.add(surrender.getRoomMoney());
        surrender.setRoomMoneyBackType(2);

        offlineTotalMoney = offlineTotalMoney.add(surrender.getEnergyMoney());
        surrender.setEnergyMoneyBackType(2);

        offlineTotalMoney = offlineTotalMoney.add(surrender.getWaterMoney());
        surrender.setWaterMoneyBackType(2);

        offlineTotalMoney = offlineTotalMoney.add(surrender.getElectricMoney());
        surrender.setElectricMoneyBackType(2);

        offlineTotalMoney = offlineTotalMoney.add(surrender.getDepositMoney());
        offlineTotalMoney = offlineTotalMoney.add(surrender.getPropertyMoney());
        offlineTotalMoney = offlineTotalMoney.add(surrender.getOtherMoney());
        surrender.setDepositMoneyBackType(2);
        surrender.setPropertyMoneyBackType(2);
        surrender.setOtherMoneyBackType(2);
        surrender.setOnlineBackMoney(onlineTotalMoney);
        surrender.setOfflineBackMoney(offlineTotalMoney);
        surrenderMapper.updateById(surrender);

        // 记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(user.getUserId());
        log.setOperateName(user.getNickName());
        log.setSourceId(surrender.getId());
        log.setType("surrender");
        log.setRefuse("管理员确认退租款项成功");
        log.setDelStatus(0);
        log.setCreateBy(user.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);
    }

    @Transactional(rollbackFor = Exception.class)
    public void verifyMoney(Long surrenderId, SysUser user) {
        FlatSurrender surrender = surrenderMapper.selectById(surrenderId);
        if(surrender == null || surrender.getDelStatus() == 1) {
            throw new ServiceException("退租信息不存在");
        }

        if(surrender.getStatus() != 2) {
            throw new ServiceException("非确认款项状态，不可确认金额");
        }

        surrender.setStatus(3);
        surrender.setUpdateBy(user.getNickName());
        surrender.setUpdateTime(LocalDateTime.now());
        surrenderMapper.updateById(surrender);

        // 记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(user.getUserId());
        log.setOperateName(user.getNickName());
        log.setSourceId(surrender.getId());
        log.setType("surrender");
        log.setRefuse("管理员确认退款金额成功");
        log.setDelStatus(0);
        log.setCreateBy(user.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);
    }

    @Transactional(rollbackFor = Exception.class)
    public void verifyRemit(Long surrenderId, SysUser user) {
        FlatSurrender surrender = surrenderMapper.selectById(surrenderId);
        if(surrender == null || surrender.getDelStatus() == 1) {
            throw new ServiceException("退租信息不存在");
        }

        if(surrender.getStatus() != 3) {
            throw new ServiceException("非确认金额状态，不可确认打款");
        }

        surrender.setStatus(4);
        surrender.setUpdateBy(user.getNickName());
        surrenderMapper.updateById(surrender);

        List<FlatBill> refundBills = new ArrayList<>();

        if(surrender.getRoomMoneyBackType() == 1) {
            List<FlatBill> bills = billMapper.selectList(q -> q
                    .eq(FlatBill::getDelStatus, 0)
                    .eq(FlatBill::getCusId, surrender.getCusId())
                    .eq(FlatBill::getRoomId, surrender.getRoomId())
                    .eq(FlatBill::getPayTarget, BillConstants.PLAN)
                    .orderByDesc(FlatBill::getPayTime)
            );
            BigDecimal totalRefundMoney = surrender.getRoomMoney();
            for(FlatBill bill : bills) {
                if(totalRefundMoney.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                if(bill.getMoney().compareTo(totalRefundMoney) >= 0) {
                    bill.setRefundStatus(1);
                    bill.setRefundMoney(totalRefundMoney);
                    totalRefundMoney = BigDecimal.ZERO;
                } else {
                    bill.setRefundStatus(1);
                    bill.setRefundMoney(bill.getMoney());
                    totalRefundMoney = totalRefundMoney.subtract(bill.getMoney());
                }
                refundBills.add(bill);
            }

            if(totalRefundMoney.compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("房租费线上退款失败");
            }
        }

        if(surrender.getWaterMoneyBackType() == 1) {
            List<FlatBill> bills = billMapper.selectList(q -> q
                    .eq(FlatBill::getDelStatus, 0)
                    .eq(FlatBill::getCusId, surrender.getCusId())
                    .eq(FlatBill::getRoomId, surrender.getRoomId())
                    .eq(FlatBill::getPayTarget, BillConstants.WATER)
                    .orderByDesc(FlatBill::getPayTime)
            );
            BigDecimal totalRefundMoney = surrender.getWaterMoney();
            for(FlatBill bill : bills) {
                if(totalRefundMoney.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                if(bill.getMoney().compareTo(totalRefundMoney) >= 0) {
                    bill.setRefundStatus(1);
                    bill.setRefundMoney(totalRefundMoney);
                    totalRefundMoney = BigDecimal.ZERO;
                } else {
                    bill.setRefundStatus(1);
                    bill.setRefundMoney(bill.getMoney());
                    totalRefundMoney = totalRefundMoney.subtract(bill.getMoney());
                }
                refundBills.add(bill);
            }

            if(totalRefundMoney.compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("水费线上退款失败");
            }
        }

        if(surrender.getElectricMoneyBackType() == 1) {
            List<FlatBill> bills = billMapper.selectList(q -> q
                    .eq(FlatBill::getDelStatus, 0)
                    .eq(FlatBill::getCusId, surrender.getCusId())
                    .eq(FlatBill::getRoomId, surrender.getRoomId())
                    .eq(FlatBill::getPayTarget, BillConstants.ELECTRIC)
                    .orderByDesc(FlatBill::getPayTime)
            );
            BigDecimal totalRefundMoney = surrender.getElectricMoney();
            for(FlatBill bill : bills) {
                if(totalRefundMoney.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                if(bill.getMoney().compareTo(totalRefundMoney) >= 0) {
                    bill.setRefundStatus(1);
                    bill.setRefundMoney(totalRefundMoney);
                    totalRefundMoney = BigDecimal.ZERO;
                } else {
                    bill.setRefundStatus(1);
                    bill.setRefundMoney(bill.getMoney());
                    totalRefundMoney = totalRefundMoney.subtract(bill.getMoney());
                }
                refundBills.add(bill);
            }

            if(totalRefundMoney.compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("电费线上退款失败");
            }
        }

        if(!refundBills.isEmpty()) {
            // 查询企业列表并根据id分组
            Map<Long, FlatCompany> companyMap = companyMapper.selectList(q -> q
                    .eq(FlatCompany::getDelStatus, 0)
            ).stream().collect(Collectors.toMap(FlatCompany::getId, Function.identity()));
            for (FlatBill refundBill : refundBills) {
                FlatCompany company = companyMap.get(refundBill.getReceiveCompanyId());
                refundBill.setRefundOrderNo(IdUtil.fastSimpleUUID());
                refundBill.setRefundTime(LocalDateTime.now());
                try {
                    weChatPayService.refund(
                            company.getWxPayAppId(), company.getWxPayMchId(),
                            refundBill.getOrderNo(), refundBill.getRefundOrderNo(),
                            refundBill.getMoney().multiply(BigDecimal.valueOf(100)).intValue(),
                            refundBill.getRefundMoney().multiply(BigDecimal.valueOf(100)).intValue()
                    );
                    refundBill.setUpdateTime(LocalDateTime.now());
                    billMapper.updateById(refundBill);
                } catch (Exception e) {
                    throw new ServiceException("退款失败");
                }
            }
        }

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(user.getUserId());
        log.setOperateName(user.getNickName());
        log.setSourceId(surrender.getId());
        log.setType("surrender");
        log.setRefuse("管理员确认退租打款成功");
        log.setDelStatus(0);
        log.setCreateBy(user.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);
    }

    @Transactional(rollbackFor = Exception.class)
    public void verifyHasRemit(Long surrenderId, SysUser user) {
        FlatSurrender surrender = surrenderMapper.selectById(surrenderId);
        if(surrender == null || surrender.getDelStatus() == 1) {
            throw new ServiceException("退租信息不存在");
        }

        if(surrender.getStatus() != 4) {
            throw new ServiceException("非确认打款状态，不可确认已打款");
        }

        FlatLease lease = leaseMapper.selectById(surrender.getLeaseId());
        if(lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁信息不存在");
        }

        FlatContract contract = contractMapper.selectById(surrender.getContractId());
        if(contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同信息不存在");
        }

        surrender.setStatus(5);
        surrender.setUpdateBy(user.getNickName());
        surrenderMapper.updateById(surrender);

        //房源租户id清空，状态变脏房
        roomMapper.update(q -> q
                .set(FlatRoom::getStatus, 3)
                .eq(FlatRoom::getId, surrender.getRoomId())
                .eq(FlatRoom::getDelStatus, 0)
        );

        //租赁信息状态变为已退租
        lease.setStatus(8);
        lease.setSurrenderFinishTime(LocalDateTime.now());
        lease.setUpdateTime(LocalDateTime.now());
        lease.setUpdateBy(user.getNickName());
        leaseMapper.updateById(lease);

        // 合同变更状态为已完成
        contract.setStatus(6);
        contract.setUpdateTime(LocalDateTime.now());
        contract.setUpdateBy(user.getNickName());
        contractMapper.updateById(contract);

        // 同住人记录状态变更为已完成
        liveMapper.update(q -> q
            .set(FlatLive::getStatus, 3)
            .set(FlatLive::getUpdateTime, LocalDateTime.now())
            .set(FlatLive::getUpdateBy, user.getNickName())
            .eq(FlatLive::getLeaseId, lease.getId())
                .eq(FlatLive::getStatus, 2)
            .eq(FlatLive::getDelStatus, 0)
        );

        // 同住人合同变更为已完成
        contractMapper.update(q -> q
                .set(FlatContract::getStatus, 6)
                .set(FlatContract::getUpdateTime, LocalDateTime.now())
                .set(FlatContract::getUpdateBy, user.getNickName())
                .eq(FlatContract::getLeaseId, lease.getId())
                .eq(FlatContract::getType, 2)
                .eq(FlatContract::getDelStatus, 0)
        );

        //用户通知
        messageService.sysToCus(
                user.getUserId(),
                lease.getCusId(),
                "退租已完成",
                "/pages/user/retreatRent/info/index?id=" + surrender.getId()
        );

        //管家通知
        messageService.sysToButler(
                user.getUserId(),
                lease.getButlerId(),
                "退租已完成",
                "/pages/butler/home/<USER>/index?id=" + surrender.getId()
        );

        // 未支付的各类付款计划标记为作废
        payPlanMapper.update(q -> q
                .set(FlatPayPlan::getInvalidFlag, 1)
                .set(FlatPayPlan::getInvalidTime, LocalDateTime.now())
                .set(FlatPayPlan::getUpdateBy, user.getNickName())
                .set(FlatPayPlan::getUpdateTime, LocalDateTime.now())
                .eq(FlatPayPlan::getLeaseId, lease.getId())
                .eq(FlatPayPlan::getPayStatus, 0)
                .eq(FlatPayPlan::getInvalidFlag, 0)
        );

        //物业通知
        List<FlatRelation> relations = relationMapper.selectList(q -> q
                .eq(FlatRelation::getFlatId, surrender.getFlatId())
                .eq(FlatRelation::getType, 1)
                .eq(FlatRelation::getDelStatus, 0)
        );
        if (!relations.isEmpty() && relations.get(0).getRelationUserId() != null) {
            messageService.sysToProperty(
                    user.getUserId(),
                    relations.get(0).getRelationUserId(),
                    "退租已完成",
                    "/pages/property/rentInfo/index?id=" + surrender.getId()
            );
        }

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(user.getUserId());
        log.setOperateName(user.getNickName());
        log.setSourceId(surrender.getId());
        log.setType("surrender");
        log.setRefuse("管理员确认退租已打款成功");
        log.setDelStatus(0);
        log.setCreateBy(user.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);
    }
}
