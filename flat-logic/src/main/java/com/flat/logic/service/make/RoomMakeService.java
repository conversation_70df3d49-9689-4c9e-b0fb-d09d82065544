package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.VerifyReq;
import com.flat.logic.dto.req.make.RoomMakeAddReq;
import com.flat.logic.dto.req.make.RoomMakeQueryReq;
import com.flat.logic.dto.resp.make.RoomMakeDetailResp;
import com.flat.logic.dto.resp.make.RoomMakeListResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.make.FlatRoomMake;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.make.FlatRoomMakeMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公寓看房预约Service业务层处理
 */
@Service
public class RoomMakeService {

    @Resource
    private FlatRoomMakeMapper roomMakeMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private MessageService messageService;

    public TablePage<RoomMakeListResp> queryRespPage(RoomMakeQueryReq req) {
        return PageUtils.paginate(() -> roomMakeMapper.selectRespList(req));
    }

    public RoomMakeDetailResp queryRespDetail(Long id) {
        RoomMakeListResp listResp = roomMakeMapper.selectRespById(id);

        RoomMakeDetailResp detailResp = new RoomMakeDetailResp();
        BeanUtils.copyProperties(listResp, detailResp);
        List<FlatMakeLog> logs = makeLogMapper.selectList(q -> q
                .eq(FlatMakeLog::getSourceId, listResp.getId())
                .eq(FlatMakeLog::getType, "make_room")
                .eq(FlatMakeLog::getDelStatus, 0)
                .orderByAsc(FlatMakeLog::getCreateTime)
        );
        detailResp.setLogs(logs);

        return detailResp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(RoomMakeAddReq req, CusUser cusUser) {
        FlatRoom room = roomMapper.selectById(req.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        FlatRoomMake roomMake = new FlatRoomMake();
        BeanUtils.copyProperties(req, roomMake);
        roomMake.setFlatId(room.getFlatId());
        roomMake.setCusId(cusUser.getUserId());
        roomMake.setButlerId(room.getButlerId());
        roomMake.setStatus(0);
        roomMake.setCreateBy(cusUser.getNickName());
        roomMake.setCreateTime(LocalDateTime.now());
        roomMake.setDelStatus(0);
        roomMakeMapper.insert(roomMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(roomMake.getId());
        log.setType("make_room");
        log.setRefuse("新建预约看房");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        messageService.cusToButler(
                cusUser.getUserId(), room.getButlerId(),
                cusUser.getNickName() + "预约看房",
                "/pages/home/<USER>/info/index?id=" + roomMake.getId()
        );
    }

    public void cancel(Long roomMakeId, CusUser cusUser) {
        FlatRoomMake roomMake = roomMakeMapper.selectById(roomMakeId);
        if (roomMake == null || roomMake.getDelStatus() == 1) {
            throw new ServiceException("预约信息不存在");
        }

        roomMake.setStatus(1);
        roomMake.setCancelTime(LocalDateTime.now());
        roomMake.setUpdateBy(cusUser.getNickName());
        roomMake.setUpdateTime(LocalDateTime.now());
        roomMakeMapper.updateById(roomMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(roomMake.getId());
        log.setType("make_room");
        log.setRefuse("用户撤销预约看房");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        messageService.cusToButler(
                cusUser.getUserId(), roomMake.getButlerId(),
                cusUser.getNickName() + "撤销预约看房",
                "/pages/home/<USER>/info/index?id=" + roomMake.getId()
        );
    }

    public void verify(VerifyReq req, MaiUser maiUser) {
        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatRoomMake roomMake = roomMakeMapper.selectById(req.getId());
        if (roomMake == null || roomMake.getDelStatus() == 1) {
            throw new ServiceException("预约信息不存在");
        }

        if (roomMake.getStatus() == 1) {
            throw new ServiceException("预约已撤销");
        }

        if(roomMake.getStatus() != 0) {
            throw new ServiceException("该预约管家已审核");
        }

        if(req.getStatus() == 1) {
            roomMake.setStatus(3);
        } else {
            roomMake.setStatus(2);
            roomMake.setRefuse(req.getRefuse());
        }
        roomMake.setButlerId(maiUser.getUserId());
        roomMake.setButlerVerifyTime(LocalDateTime.now());
        roomMake.setUpdateBy(maiUser.getNickName());
        roomMake.setUpdateTime(LocalDateTime.now());
        roomMakeMapper.updateById(roomMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getNickName());
        log.setSourceId(roomMake.getId());
        log.setType("make_room");
        log.setRefuse(req.getStatus() == 1 ? "看房预约管家审核通过" : "看房预约管家审核未通过");
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        if(req.getStatus() == 1) { // 审核通过，通知用户
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    roomMake.getCusId(),
                    "看房预约管家审核通过",
                    "/pages/home/<USER>/info/index?id=" + roomMake.getId()
            );
        } else { // 审核未通过，通知用户
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    roomMake.getCusId(),
                    "看房预约管家审核未通过",
                    "/pages/home/<USER>/info/index?id=" + roomMake.getId()
            );
        }
    }

    public void remove(Long id, SysUser sysUser) {
        roomMakeMapper.update(q -> q
                .set(FlatRoomMake::getDelStatus, 1)
                .set(FlatRoomMake::getUpdateTime, LocalDateTime.now())
                .set(FlatRoomMake::getUpdateBy, sysUser.getUsername())
                .eq(FlatRoomMake::getId, id)
                .eq(FlatRoomMake::getDelStatus, 0)
        );
    }
}
