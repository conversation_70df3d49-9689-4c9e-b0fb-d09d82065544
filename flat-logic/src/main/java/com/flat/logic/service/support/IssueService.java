package com.flat.logic.service.support;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.support.IssueQueryReq;
import com.flat.logic.entity.support.FlatIssue;
import com.flat.logic.mapper.support.FlatIssueMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 常见问题Service业务层处理
 */
@Service
public class IssueService {

    @Resource
    private FlatIssueMapper issueMapper;

    public TablePage<FlatIssue> queryPage(IssueQueryReq req) {
        return PageUtils.paginate(() -> issueMapper.selectList(q -> q
                .like(StringUtils.isNotBlank(req.getIssue()), FlatIssue::getIssue, req.getIssue())
                .eq(FlatIssue::getDelStatus, 0)
        ));
    }

    public FlatIssue queryDetail(Long id) {
        FlatIssue issue = issueMapper.selectById(id);
        if (issue == null || issue.getDelStatus() == 1) {
            throw new ServiceException("常见问题不存在或已删除");
        }

        return issue;
    }

}
