package com.flat.logic.service.sundry;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.sundry.SundryProductAddReq;
import com.flat.logic.dto.req.sundry.SundryProductEditReq;
import com.flat.logic.dto.req.sundry.SundryProductQueryReq;
import com.flat.logic.dto.resp.sundry.SundryProductDetailResp;
import com.flat.logic.dto.resp.sundry.SundryProductListResp;
import com.flat.logic.entity.sundry.SundryCategory;
import com.flat.logic.entity.sundry.SundryInventoryLog;
import com.flat.logic.entity.sundry.SundryProduct;
import com.flat.logic.mapper.sundry.SundryCategoryMapper;
import com.flat.logic.mapper.sundry.SundryInventoryLogMapper;
import com.flat.logic.mapper.sundry.SundryProductMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

@Service
public class SundryProductService {

    @Resource
    private SundryProductMapper sundryProductMapper;

    @Resource
    private SundryCategoryMapper sundryCategoryMapper;

    @Resource
    private SundryInventoryLogMapper sundryInventoryLogMapper;

    @Transactional
    public void add(SundryProductAddReq req, SysUser user) {
        SundryProduct product = new SundryProduct();
        product.setCategoryId(req.getCategoryId());
        product.setNumber(req.getNumber());
        product.setName(req.getName());
        product.setUnit(req.getUnit());
        product.setUnitPrice(req.getUnitPrice());
        product.setOpeningInventory(req.getOpeningInventory());
        product.setDayConsumeAmount(req.getDayConsumeAmount());
        product.setAlarmThreshold(req.getAlarmThreshold()); // 设置告警阈值
        product.setTotalInventory(req.getOpeningInventory()); // 初始总库存等于期初库存
        product.setCreateBy(user.getUsername());
        product.setCreateTime(LocalDateTime.now());
        product.setDelStatus(0);
        sundryProductMapper.insert(product);
    }

    @Transactional
    public void edit(SundryProductEditReq req, SysUser user) {
        SundryProduct product = sundryProductMapper.selectById(req.getId());
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }

        product.setCategoryId(req.getCategoryId());
        product.setNumber(req.getNumber());
        product.setName(req.getName());
        product.setUnit(req.getUnit());
        product.setUnitPrice(req.getUnitPrice());
        product.setDayConsumeAmount(req.getDayConsumeAmount());
        product.setAlarmThreshold(req.getAlarmThreshold()); // 设置告警阈值
        product.setUpdateBy(user.getUsername());
        product.setUpdateTime(LocalDateTime.now());
        sundryProductMapper.updateById(product);
    }

    @Transactional
    public void delete(Long id, SysUser user) {
        SundryProduct product = sundryProductMapper.selectById(id);
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }

        // 逻辑删除
        product.setDelStatus(1);
        product.setUpdateBy(user.getUsername());
        product.setUpdateTime(LocalDateTime.now());
        sundryProductMapper.updateById(product);
    }

    public TablePage<SundryProductListResp> queryRespPage(SundryProductQueryReq req) {
        return PageUtils.paginate(() -> sundryProductMapper.selectRespList(req));
    }

    public List<SundryProductListResp> queryRespList(SundryProductQueryReq req) {
        return sundryProductMapper.selectRespList(req);
    }

    public SundryProductDetailResp detail(Long id) {
        SundryProduct product = sundryProductMapper.selectById(id);
        if (product == null || product.getDelStatus() == 1) {
            throw new ServiceException("物品库产品不存在");
        }

        SundryCategory category = sundryCategoryMapper.selectById(product.getCategoryId());
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("物品库类别不存在");
        }

        List<SundryInventoryLog> inventoryLogs = sundryInventoryLogMapper.selectList(q -> q
            .eq(SundryInventoryLog::getProductId, id)
            .eq(SundryInventoryLog::getDelStatus, 0)
            .orderByDesc(SundryInventoryLog::getCreateTime)
        );

        SundryProductDetailResp resp = new SundryProductDetailResp();
        BeanUtils.copyProperties(product, resp);
        resp.setCategoryName(category.getName());
        // 库存可用天数
        resp.setAvailableDays(product.getTotalInventory() / product.getDayConsumeAmount());
        // 设置告警状态
        resp.setAlarmStatus(product.getTotalInventory() <= product.getAlarmThreshold() ? 1 : 0);
        resp.setLogs(inventoryLogs);

        return resp;
    }

}
