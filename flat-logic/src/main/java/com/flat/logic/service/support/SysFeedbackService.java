package com.flat.logic.service.support;

import org.springframework.stereotype.Service;

/**
 * 反馈Service业务层处理
 */
@Service
public class SysFeedbackService {

    @Resource
    private SysFeedbackMapper sysFeedbackMapper;
//
//    /**
//     * 查询反馈
//     *
//     * @param id 反馈主键
//     * @return 反馈
//     */
//    public SysFeedback selectSysFeedbackById(Long id)
//    {
//        return sysFeedbackMapper.selectSysFeedbackById(id);
//    }
//
//    /**
//     * 查询反馈列表
//     *
//     * @param sysFeedback 反馈
//     * @return 反馈
//     */
//    public List<SysFeedback> selectSysFeedbackList(SysFeedback sysFeedback)
//    {
//        return sysFeedbackMapper.selectSysFeedbackList(sysFeedback);
//    }
//
//    /**
//     * 新增反馈
//     *
//     * @param sysFeedback 反馈
//     * @return 结果
//     */
//    public int insertSysFeedback(SysFeedback sysFeedback)
//    {
//        sysFeedback.setCreateTime(LocalDateTime.now());
//        return sysFeedbackMapper.insertSysFeedback(sysFeedback);
//    }
//
//    /**
//     * 修改反馈
//     *
//     * @param sysFeedback 反馈
//     * @return 结果
//     */
//    public int updateSysFeedback(SysFeedback sysFeedback)
//    {
//        return sysFeedbackMapper.updateSysFeedback(sysFeedback);
//    }
//
//    /**
//     * 批量删除反馈
//     *
//     * @param ids 需要删除的反馈主键
//     * @return 结果
//     */
//    public int deleteSysFeedbackByIds(Long[] ids)
//    {
//        return sysFeedbackMapper.deleteSysFeedbackByIds(ids);
//    }
//
//    /**
//     * 删除反馈信息
//     *
//     * @param id 反馈主键
//     * @return 结果
//     */
//    public int deleteSysFeedbackById(Long id)
//    {
//        return sysFeedbackMapper.deleteSysFeedbackById(id);
//    }
//
//    public int delStatus(Long[] ids) {
//        return sysFeedbackMapper.delStatus(ids);
//    }
}
