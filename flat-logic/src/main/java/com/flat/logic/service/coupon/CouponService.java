package com.flat.logic.service.coupon;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.flat.logic.entity.account.MaiUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.coupon.CouponAddByPhoneReq;
import com.flat.logic.dto.req.coupon.CouponAddReq;
import com.flat.logic.dto.req.coupon.CouponOwnerVerifyReq;
import com.flat.logic.dto.req.coupon.CouponQueryReq;
import com.flat.logic.dto.resp.coupon.CouponExportResp;
import com.flat.logic.dto.resp.coupon.CouponResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.coupon.Coupon;
import com.flat.logic.entity.coupon.CouponTemplate;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.coupon.CouponMapper;
import com.flat.logic.mapper.coupon.CouponTemplateMapper;
import com.flat.logic.model.coupon.CouponPhoneImport;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 优惠券服务
 */
@Slf4j
@Service
public class CouponService {

    @Resource
    private CouponTemplateMapper couponTemplateMapper;

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    public void add(CouponAddReq req, SysUser sysUser) {
        CouponTemplate couponTemplate = couponTemplateMapper.selectById(req.getTemplateId());
        if (couponTemplate == null || couponTemplate.getDelStatus() == 1) {
            throw new ServiceException("优惠券模板不存在");
        }

        CusUser cusUser = cusUserMapper.selectById(req.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在");
        }

        Coupon coupon = new Coupon();
        coupon.setTemplateId(req.getTemplateId());
        coupon.setCusId(cusUser.getUserId());
        coupon.setTarget(couponTemplate.getTarget());
        coupon.setCostTypeId(couponTemplate.getCostTypeId());
        coupon.setName(couponTemplate.getName());
        coupon.setValidBeginTime(couponTemplate.getValidBeginTime());
        coupon.setValidEndTime(couponTemplate.getValidEndTime());
        coupon.setAmount(couponTemplate.getAmount());
        // 设置审核状态为待审核
        coupon.setVerifyStatus(0);
        coupon.setCreateBy(sysUser.getUsername());
        coupon.setCreateTime(LocalDateTime.now());
        coupon.setDelStatus(0);
        couponMapper.insert(coupon);
    }

    public void remove(Long id, SysUser sysUser) {
        couponMapper.update(q -> q
                .set(Coupon::getDelStatus, 1)
                .set(Coupon::getUpdateTime, LocalDateTime.now())
                .set(Coupon::getUpdateBy, sysUser.getUsername())
                .eq(Coupon::getId, id)
                .eq(Coupon::getDelStatus, 0));
    }

    /**
     * 分页查询优惠券列表
     */
    public TablePage<CouponResp> queryPage(CouponQueryReq req) {
        return PageUtils.paginate(() -> couponMapper.selectCouponList(req));
    }

    /**
     * 查询优惠券详情
     */
    public CouponResp queryDetail(Long id) {
        CouponResp coupon = couponMapper.selectCouponDetail(id);
        if (coupon == null) {
            throw new ServiceException("优惠券不存在");
        }
        return coupon;
    }

    /**
     * 导出优惠券列表
     *
     * @param req 查询条件
     * @return 优惠券导出数据列表
     */
    public List<CouponExportResp> exportList(CouponQueryReq req) {
        List<CouponResp> respList = couponMapper.selectCouponList(req);
        List<CouponExportResp> exportList = new ArrayList<>();

        for (CouponResp resp : respList) {
            CouponExportResp exportResp = new CouponExportResp();
            exportResp.setId(resp.getId());
            exportResp.setName(resp.getName());

            // 设置优惠券类型名称
            if (resp.getTarget() != null) {
                if (resp.getTarget() == 1) {
                    exportResp.setTargetName("保洁预约");
                } else if (resp.getTarget() == 2) {
                    exportResp.setTargetName("自定义费用");
                } else {
                    exportResp.setTargetName("其他");
                }
            }

            // 费用类型名称待补充
            exportResp.setCostTypeName(resp.getCostTypeId() != null ? String.valueOf(resp.getCostTypeId()) : "");

            exportResp.setAmount(resp.getAmount());
            exportResp.setValidBeginTime(resp.getValidBeginTime());
            exportResp.setValidEndTime(resp.getValidEndTime());

            // 设置过期状态名称
            exportResp.setExpireStatus(resp.getExpireStatus());
            if (resp.getExpireStatus() != null) {
                if (resp.getExpireStatus() == 0) {
                    exportResp.setExpireStatusName("未生效");
                } else if (resp.getExpireStatus() == 1) {
                    exportResp.setExpireStatusName("正常");
                } else if (resp.getExpireStatus() == 2) {
                    exportResp.setExpireStatusName("已过期");
                } else {
                    exportResp.setExpireStatusName("未知");
                }
            }

            // 设置使用状态名称
            exportResp.setUseStatus(resp.getUseStatus());
            if (resp.getUseStatus() != null) {
                if (resp.getUseStatus() == 0) {
                    exportResp.setUseStatusName("未使用");
                } else if (resp.getUseStatus() == 1) {
                    exportResp.setUseStatusName("已使用");
                } else {
                    exportResp.setUseStatusName("未知");
                }
            }

            // 设置审核状态相关信息
            exportResp.setVerifyStatus(resp.getVerifyStatus());
            if (resp.getVerifyStatus() != null) {
                if (resp.getVerifyStatus() == 0) {
                    exportResp.setVerifyStatusName("待审核");
                } else if (resp.getVerifyStatus() == 1) {
                    exportResp.setVerifyStatusName("撤销");
                } else if (resp.getVerifyStatus() == 2) {
                    exportResp.setVerifyStatusName("业主拒绝");
                    exportResp.setVerifyRefuse(resp.getVerifyRefuse());
                } else if (resp.getVerifyStatus() == 3) {
                    exportResp.setVerifyStatusName("业主确认");
                } else {
                    exportResp.setVerifyStatusName("未知");
                }
            }
            exportResp.setOwnerVerifyName(resp.getOwnerVerifyName());
            exportResp.setOwnerVerifyTime(resp.getOwnerVerifyTime());
            exportResp.setVerifyRefuse(resp.getVerifyRefuse());

            exportResp.setUseTime(resp.getUseTime());
            exportResp.setUseBillId(resp.getUseBillId());
            exportResp.setCusRealName(resp.getCusRealName());
            exportResp.setCusPhoneNumber(resp.getCusPhoneNumber());
            exportResp.setCreateTime(resp.getCreateTime());
            exportResp.setCreateBy(resp.getCreateBy());

            exportList.add(exportResp);
        }

        return exportList;
    }

    /**
     * 根据手机号添加优惠券
     * @param req 请求参数
     * @param sysUser 操作人
     */
    public void addByPhone(CouponAddByPhoneReq req, SysUser sysUser) {
        // 查询优惠券模板
        CouponTemplate couponTemplate = couponTemplateMapper.selectById(req.getTemplateId());
        if (couponTemplate == null || couponTemplate.getDelStatus() == 1) {
            throw new ServiceException("优惠券模板不存在");
        }

        // 根据手机号查询用户
        CusUser cusUser = cusUserMapper.selectFirst(q -> q.eq(CusUser::getPhoneNumber, req.getPhoneNumber()));
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("未找到手机号对应的用户：" + req.getPhoneNumber());
        }

        // 构造优惠券对象并保存
        Coupon coupon = new Coupon();
        coupon.setTemplateId(req.getTemplateId());
        coupon.setCusId(cusUser.getUserId()); // 设置为查询到的用户ID
        coupon.setTarget(couponTemplate.getTarget());
        coupon.setCostTypeId(couponTemplate.getCostTypeId());
        coupon.setName(couponTemplate.getName());
        coupon.setValidBeginTime(couponTemplate.getValidBeginTime());
        coupon.setValidEndTime(couponTemplate.getValidEndTime());
        coupon.setAmount(couponTemplate.getAmount());
        coupon.setUseStatus(0);
        // 设置审核状态为待审核
        coupon.setVerifyStatus(0);
        coupon.setCreateBy(sysUser.getUsername());
        coupon.setCreateTime(LocalDateTime.now());
        coupon.setDelStatus(0);
        couponMapper.insert(coupon);
    }

    /**
     * 批量添加优惠券
     * @param templateId 优惠券模板ID
     * @param phoneImports Excel导入的手机号列表
     * @param sysUser 操作人
     * @return 成功添加的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchAdd(Long templateId, List<CouponPhoneImport> phoneImports, SysUser sysUser) {
        // 检查优惠券模板是否存在
        CouponTemplate couponTemplate = couponTemplateMapper.selectById(templateId);
        if (couponTemplate == null || couponTemplate.getDelStatus() == 1) {
            throw new ServiceException("优惠券模板不存在");
        }

        int successCount = 0;

        for (CouponPhoneImport phoneImport : phoneImports) {
            String phoneNumber = phoneImport.getPhoneNumber();

            // 验证手机号格式
            if (StringUtils.isBlank(phoneNumber)) {
                continue;
            }

            try {
                // 查询用户
                CusUser cusUser = cusUserMapper.selectFirst(q -> q.eq(CusUser::getPhoneNumber, phoneNumber));
                if (cusUser == null || cusUser.getDelStatus() == 1) {
                    continue;
                }

                // 创建优惠券
                Coupon coupon = new Coupon();
                coupon.setTemplateId(templateId);
                coupon.setCusId(cusUser.getUserId());
                coupon.setTarget(couponTemplate.getTarget());
                coupon.setCostTypeId(couponTemplate.getCostTypeId());
                coupon.setName(couponTemplate.getName());
                coupon.setValidBeginTime(couponTemplate.getValidBeginTime());
                coupon.setValidEndTime(couponTemplate.getValidEndTime());
                coupon.setAmount(couponTemplate.getAmount());
                coupon.setUseStatus(0);
                // 设置审核状态为待审核
                coupon.setVerifyStatus(0);
                coupon.setCreateBy(sysUser.getUsername());
                coupon.setCreateTime(LocalDateTime.now());
                coupon.setDelStatus(0);
                couponMapper.insert(coupon);

                successCount++;
            } catch (Exception e) {
                // 记录错误但继续处理
                log.warn("处理手机号 " + phoneImport.getPhoneNumber() + " 时出错: " + e.getMessage(), e);
            }
        }

        return successCount;
    }

    /**
     * 撤销优惠券
     * @param couponId 优惠券ID
     * @param sysUser 操作人
     */
    public void cancel(Long couponId, SysUser sysUser) {
        Coupon coupon = couponMapper.selectById(couponId);
        if (coupon == null || coupon.getDelStatus() == 1) {
            throw new ServiceException("优惠券不存在");
        }

        // 只有待审核状态的优惠券才能撤销
        if (coupon.getVerifyStatus() != 0) {
            throw new ServiceException("只有待审核状态的优惠券才能撤销");
        }

        // 更新优惠券状态为撤销
        coupon.setVerifyStatus(1);
        coupon.setUpdateBy(sysUser.getUsername());
        coupon.setUpdateTime(LocalDateTime.now());
        couponMapper.updateById(coupon);
    }

    /**
     * 业主审核优惠券
     * @param req 优惠券业主审核请求
     * @param maiUser 业主用户
     */
    public void ownerVerify(CouponOwnerVerifyReq req, MaiUser maiUser) {
        //判断用户类型是否是业主
        if(!"OWNER".equalsIgnoreCase(maiUser.getUserType())) {
            throw new ServiceException("只有业主才能审核优惠券");
        }

        Coupon coupon = couponMapper.selectById(req.getId());
        if (coupon == null || coupon.getDelStatus() == 1) {
            throw new ServiceException("优惠券不存在");
        }

        // 只有待审核状态的优惠券才能审核
        if (coupon.getVerifyStatus() != 0) {
            throw new ServiceException("只有待审核状态的优惠券才能审核");
        }

        // 根据审核操作类型处理
        if (req.getAction() == 1) {
            // 确认操作
            // 更新优惠券状态为业主确认
            coupon.setVerifyStatus(3);
            coupon.setOwnerVerifyId(maiUser.getUserId());
            coupon.setOwnerVerifyTime(LocalDateTime.now());
            coupon.setUpdateBy(maiUser.getUsername());
            coupon.setUpdateTime(LocalDateTime.now());
        } else if (req.getAction() == 2) {
            // 拒绝操作
            // 拒绝原因不能为空
            if (req.getRefuseReason() == null || req.getRefuseReason().isBlank()) {
                throw new ServiceException("拒绝原因不能为空");
            }

            // 更新优惠券状态为业主拒绝
            coupon.setVerifyStatus(2);
            coupon.setOwnerVerifyId(maiUser.getUserId());
            coupon.setOwnerVerifyTime(LocalDateTime.now());
            coupon.setVerifyRefuse(req.getRefuseReason());
            coupon.setUpdateBy(maiUser.getUsername());
            coupon.setUpdateTime(LocalDateTime.now());
        } else {
            throw new ServiceException("无效的审核操作类型");
        }

        couponMapper.updateById(coupon);
    }

    // public void use(CouponUseReq req, SysUser sysUser) {
    //     Coupon coupon = couponMapper.selectById(req.getId());
    //     if (coupon == null || coupon.getDelStatus() == 1) {
    //         throw new ServiceException("优惠券不存在");
    //     }

    //     if (coupon.getValidBeginTime().isAfter(LocalDateTime.now())) {
    //         throw new ServiceException("优惠券未开始");
    //     }

    //     if (coupon.getValidEndTime().isBefore(LocalDateTime.now())) {
    //         throw new ServiceException("优惠券已过期");
    //     }

    //     if (coupon.getUseStatus() == 1) {
    //         throw new ServiceException("优惠券已使用");
    //     }

    //     if (coupon.getTarget() == 1) {
    //         throw new ServiceException("保洁券不能直接核销");
    //     }

    //     coupon.setUseStatus(1);
    //     coupon.setUseTime(LocalDateTime.now());
    //     coupon.setUpdateBy(sysUser.getUsername());
    //     coupon.setUpdateTime(LocalDateTime.now());
    //     couponMapper.updateById(coupon);
    // }
}
