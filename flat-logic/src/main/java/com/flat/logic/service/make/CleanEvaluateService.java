package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.make.CleanEvaluateAddReq;
import com.flat.logic.dto.req.make.CleanEvaluateQueryReq;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.make.FlatCleanEvaluate;
import com.flat.logic.entity.make.FlatCleanMake;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.make.FlatCleanEvaluateMapper;
import com.flat.logic.mapper.make.FlatCleanMakeMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 公寓保洁评价Service业务层处理
 */
@Service
public class CleanEvaluateService {

    @Resource
    private FlatCleanEvaluateMapper cleanEvaluateMapper;

    @Resource
    private FlatCleanMakeMapper cleanMakeMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private MessageService messageService;
    

    /**
     * 查询公寓保洁评价
     * 
     * @param id 公寓保洁评价主键
     * @return 公寓保洁评价
     */
    public FlatCleanEvaluate queryDetail(Long id) {
        FlatCleanEvaluate evaluate = cleanEvaluateMapper.selectById(id);
        if (evaluate == null || evaluate.getDelStatus() == 1) {
            throw new ServiceException("公寓保洁评价不存在");
        }

        return evaluate;
    }

    /**
     * 查询公寓保洁评价列表
     */
    public TablePage<FlatCleanEvaluate> queryPage(CleanEvaluateQueryReq req) {
        return PageUtils.paginate(
                () -> cleanEvaluateMapper.selectList(q -> q
                        .eq(req.getRoomId() != null, FlatCleanEvaluate::getRoomId, req.getRoomId())
                        .eq(req.getCusId() != null, FlatCleanEvaluate::getCusId, req.getCusId())
                        .eq(req.getButlerId() != null, FlatCleanEvaluate::getButlerId, req.getButlerId())
                        .eq(req.getPropertyId() != null, FlatCleanEvaluate::getPropertyId, req.getPropertyId())
                        .eq(req.getCleanMakeId() != null, FlatCleanEvaluate::getCleanMakeId, req.getCleanMakeId())
                        .eq(req.getLeaseId() != null, FlatCleanEvaluate::getLeaseId, req.getLeaseId())
                        .eq(FlatCleanEvaluate::getDelStatus, 0)
                )
        );
    }


    public FlatCleanEvaluate queryDetailByCleanMakeId(Long cleanMakeId) {
        return cleanEvaluateMapper.selectFirst(q -> q
                .eq(FlatCleanEvaluate::getCleanMakeId, cleanMakeId)
                .eq(FlatCleanEvaluate::getDelStatus, 0)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(CleanEvaluateAddReq req, CusUser cusUser) {
        // 先查询相关记录
        FlatCleanMake cleanMake = cleanMakeMapper.selectById(req.getCleanMakeId());
        if (cleanMake == null || cleanMake.getDelStatus() == 1) {
            throw new ServiceException("保洁预约不存在");
        }

        FlatLease lease = leaseMapper.selectById(cleanMake.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        FlatRoom room = roomMapper.selectById(lease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        if(cleanMake.getEvaluateFlag() == 1) {
            throw new ServiceException("该保洁预约已评价");
        }

        // 添加评价
        FlatCleanEvaluate evaluate = new FlatCleanEvaluate();
        BeanUtils.copyProperties(req, evaluate);
        evaluate.setFlatId(cleanMake.getFlatId());
        evaluate.setRoomId(room.getId());
        evaluate.setCusId(cusUser.getUserId());
        evaluate.setLeaseId(lease.getId());
        evaluate.setButlerId(cleanMake.getButlerId());
        evaluate.setPropertyId(cleanMake.getPropertyId());
        evaluate.setStaffId(cleanMake.getStaffId());
        evaluate.setEvaluateTime(LocalDateTime.now());
        evaluate.setCreateBy(cusUser.getNickName());
        evaluate.setCreateTime(LocalDateTime.now());
        evaluate.setDelStatus(0);
        cleanEvaluateMapper.insert(evaluate);

        cleanMake.setEvaluateFlag(1);
        cleanMake.setUpdateBy(cusUser.getNickName());
        cleanMake.setUpdateTime(LocalDateTime.now());
        cleanMakeMapper.updateById(cleanMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(cleanMake.getId());
        log.setType("make_clean");
        log.setRefuse("用户评价成功");
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        log.setDelStatus(0);
        makeLogMapper.insert(log);

        // 发送消息给管家
        messageService.cusToButler(
                cusUser.getUserId(), cleanMake.getButlerId(),
                cusUser.getNickName() + "保洁评价完成",
                "/pages/butler/home/<USER>/info/index?id=" + cleanMake.getId() + "&type=1"
        );

        // 发送消息给物业
        messageService.cusToProperty(
                cusUser.getUserId(), cleanMake.getPropertyId(),
                cusUser.getNickName() + "保洁评价完成",
                "/pages/property/info/index?id=" + cleanMake.getId() + "&type=1"
        );
    }

    public void remove(Long id, SysUser user) {
        cleanEvaluateMapper.update(q -> q
                .set(FlatCleanEvaluate::getDelStatus, 1)
                .set(FlatCleanEvaluate::getUpdateBy, user.getNickName())
                .set(FlatCleanEvaluate::getUpdateTime, LocalDateTime.now())
                .eq(FlatCleanEvaluate::getId, id)
                .eq(FlatCleanEvaluate::getDelStatus, 0)
        );
    }
}
