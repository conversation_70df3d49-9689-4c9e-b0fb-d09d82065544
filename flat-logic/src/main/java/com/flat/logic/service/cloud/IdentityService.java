package com.flat.logic.service.cloud;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Service
public class IdentityService {

    @Value("${identity.ocr.url}")
    private String ocrUrl;

    @Value("${identity.ocr.appKey}")
    private String ocrAppKey;

    @Value("${identity.ocr.appSecret}")
    private String ocrAppSecret;

    @Value("${identity.ocr.appCode}")
    private String ocrAppCode;

    @Value("${identity.factor.url}")
    private String factorUrl;

    @Value("${identity.factor.appKey}")
    private String factorAppKey;

    @Value("${identity.factor.appSecret}")
    private String factorAppSecret;

    @Value("${identity.factor.appCode}")
    private String factorAppCode;

    public JSONObject recognizeIdCard(String imageUrl) {
        String body = "image=" + imageUrl;
        URL url;
        try {
            url = new URL(ocrUrl);
        } catch (MalformedURLException e) {
            throw new ServiceException("", e);
        }
        HttpURLConnection httpURLCon;
        try {
            httpURLCon = (HttpURLConnection) url.openConnection();
            httpURLCon.setRequestMethod("POST");
            httpURLCon.setRequestProperty("Authorization", "APPCODE " + ocrAppCode);
        } catch (IOException e) {
            throw new ServiceException("", e);
        }

        int httpCode;
        try {
            byte[] postDataBytes = body.getBytes(StandardCharsets.UTF_8);
            httpURLCon.setDoOutput(true);
            OutputStream out = httpURLCon.getOutputStream();
            out.write(postDataBytes);
            out.close();
            httpCode = httpURLCon.getResponseCode();
        } catch (Exception e) {
            throw new ServiceException("", e);
        }

        if (httpCode == 200) {
            String json;
            try {
                json = read(httpURLCon.getInputStream());
            } catch (IOException e) {
                throw new ServiceException("", e);
            }
            return JSON.parseObject(json);
        } else {
            Map<String, List<String>> map = httpURLCon.getHeaderFields();
            String error = map.get("X-Ca-Error-Message").get(0);
            throw new ServiceException(error);
        }
    }

    public JSONObject checkIDCard(String name, String idCard) {
        URL url;
        try {
            url = new URL(factorUrl + "?idCard=" + idCard + "&name=" + URLEncoder.encode(name, StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new ServiceException("", e);
        }
        HttpURLConnection httpURLCon;
        try {
            httpURLCon = (HttpURLConnection) url.openConnection();
            httpURLCon.setRequestMethod("GET");
            httpURLCon.setRequestProperty("Authorization", "APPCODE " + factorAppCode);
        } catch (IOException e) {
            throw new ServiceException("", e);
        }

        int httpCode;
        try {
            httpCode = httpURLCon.getResponseCode();
        } catch (Exception e) {
            throw new ServiceException("", e);
        }

        if (httpCode == 200) {
            String json;
            try {
                json = read(httpURLCon.getInputStream());
            } catch (IOException e) {
                throw new ServiceException("", e);
            }
            return JSON.parseObject(json);
        } else {
            Map<String, List<String>> map = httpURLCon.getHeaderFields();
            String error = map.get("X-Ca-Error-Message").get(0);
            throw new ServiceException(error);
        }
    }

    private String read(InputStream is) throws IOException {
        StringBuilder sb = new StringBuilder();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String line;
        while ((line = br.readLine()) != null) {
            line = new String(line.getBytes(), StandardCharsets.UTF_8);
            sb.append(line);
        }
        br.close();
        return sb.toString();
    }
}
