package com.flat.logic.service.statistics;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.flat.logic.mapper.statistics.StatisticsMapper;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.flat.logic.dto.req.statistics.DateRangeStatisticsReq;
import com.flat.logic.dto.req.statistics.RoomInvoiceStatisticsQueryReq;
import com.flat.logic.dto.req.statistics.YearlyStatisticsReq;
import com.flat.logic.dto.resp.statistics.DailyFeeStatisticsResp;
import com.flat.logic.dto.resp.statistics.DailyStatisticsDataItem;
import com.flat.logic.dto.resp.statistics.DateRangeStatisticsResp;
import com.flat.logic.dto.resp.statistics.InvoiceStatisticsResp;
import com.flat.logic.dto.resp.statistics.MonthlyStatisticsDataItem;
import com.flat.logic.dto.resp.statistics.OverallStatisticsResp;
import com.flat.logic.dto.resp.statistics.RoomInvoiceStatisticsResp;
import com.flat.logic.dto.resp.statistics.SimpleLeaseStatisticsResp;
import com.flat.logic.dto.resp.statistics.TodayStatisticsResp;
import com.flat.logic.dto.resp.statistics.YearlyStatisticsResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.entity.lease.FlatPayPlan;
import com.flat.logic.entity.make.FlatCleanMake;
import com.flat.logic.entity.make.FlatPublicMake;
import com.flat.logic.entity.make.FlatRepairMake;
import com.flat.logic.entity.make.FlatRoomMake;
import com.flat.logic.entity.make.FlatVisitMake;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.finance.FlatBalanceLogMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.mapper.lease.FlatPayPlanMapper;
import com.flat.logic.mapper.make.FlatCleanMakeMapper;
import com.flat.logic.mapper.make.FlatPublicMakeMapper;
import com.flat.logic.mapper.make.FlatRepairMakeMapper;
import com.flat.logic.mapper.make.FlatRoomMakeMapper;
import com.flat.logic.mapper.make.FlatVisitMakeMapper;

import jakarta.annotation.Resource;

/**
 * 统计服务
 */
@Service
public class StatisticsService {

    @Resource
    private FlatRepairMakeMapper repairMakeMapper;

    @Resource
    private FlatCleanMakeMapper cleanMakeMapper;

    @Resource
    private FlatPublicMakeMapper publicMakeMapper;

    @Resource
    private FlatRoomMakeMapper roomMakeMapper;

    @Resource
    private FlatVisitMakeMapper visitMakeMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private FlatBalanceLogMapper balanceLogMapper;

    @Resource
    private FlatRoomMapper flatRoomMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatPayPlanMapper flatPayPlanMapper;

    @Resource
    private FlatCompanyMapper flatCompanyMapper;

    @Resource
    private FlatContractMapper flatContractMapper;

    @Resource
    private FlatLiveMapper flatLiveMapper;

    @Resource
    private StatisticsMapper statisticsMapper;

    /**
     * 获取今日数据统计
     *
     * @return 今日数据统计
     */
    public TodayStatisticsResp getTodayStatistics() {
        TodayStatisticsResp resp = new TodayStatisticsResp();

        // 统计今日报修预约数量
        resp.setRepairCount(repairMakeMapper.selectCount(q -> q
                .apply("DATE(create_time) = CURDATE()")
                .eq(FlatRepairMake::getDelStatus, 0)));

        // 统计今日保洁预约数量
        resp.setCleanCount(cleanMakeMapper.selectCount(q -> q
                .apply("DATE(create_time) = CURDATE()")
                .eq(FlatCleanMake::getDelStatus, 0)));

        // 统计今日公共设施预约数量
        resp.setPublicCount(publicMakeMapper.selectCount(q -> q
                .apply("DATE(create_time) = CURDATE()")
                .eq(FlatPublicMake::getDelStatus, 0)));

        // 统计今日看房预约数量
        resp.setRoomCount(roomMakeMapper.selectCount(q -> q
                .apply("DATE(create_time) = CURDATE()")
                .eq(FlatRoomMake::getDelStatus, 0)));

        // 统计今日访客预约数量
        resp.setVisitCount(visitMakeMapper.selectCount(q -> q
                .apply("DATE(create_time) = CURDATE()")
                .eq(FlatVisitMake::getDelStatus, 0)));

        // 统计今日租赁数量
        resp.setLeaseCount(leaseMapper.selectCount(q -> q
                .apply("DATE(create_time) = CURDATE()")
                .eq(FlatLease::getDelStatus, 0)));  // 状态为2表示正在租赁中

        return resp;
    }

    /**
     * 获取过去20天（包括今天）的每日费用统计
     *
     * @return 每日费用统计列表
     */
    public List<DailyFeeStatisticsResp> getLast20DaysFeeStatistics() {
        List<DailyFeeStatisticsResp> result = new ArrayList<>();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(19); // 获取20天前的日期

        // 遍历20天的日期
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            DailyFeeStatisticsResp dailyStats = new DailyFeeStatisticsResp();
            dailyStats.setDate(date);

            // 统计当天保洁费用
            BigDecimal cleanFee = billMapper.selectCleanFeeByDate(date);
            dailyStats.setCleanFee(cleanFee != null ? cleanFee : BigDecimal.ZERO);

            // 统计当天水费
            BigDecimal waterFee = balanceLogMapper.selectWaterFeeByDate(date);
            dailyStats.setWaterFee(waterFee != null ? waterFee : BigDecimal.ZERO);

            // 统计当天电费
            BigDecimal electricityFee = balanceLogMapper.selectElectricityFeeByDate(date);
            dailyStats.setElectricityFee(electricityFee != null ? electricityFee : BigDecimal.ZERO);

            result.add(dailyStats);
        }

        return result;
    }

    public InvoiceStatisticsResp getInvoiceStatistics() {
        InvoiceStatisticsResp resp = new InvoiceStatisticsResp();
        resp.setTotalMoney(billMapper.statTotalMoney(null));
        resp.setInvoicedMoney(billMapper.statInvoicedMoney(null));
        return resp;
    }

    /**
     * 查询房间账单发票统计信息
     *
     * @param req 查询条件
     * @return 房间账单发票统计信息列表
     */
    public List<RoomInvoiceStatisticsResp> getRoomInvoiceStatistics(RoomInvoiceStatisticsQueryReq req) {
        return statisticsMapper.selectRoomInvoiceStatistics(req);
    }

    /**
     * 获取整体统计数据
     *
     * @return 整体统计数据
     */
    public OverallStatisticsResp getOverallStatistics() {
        OverallStatisticsResp resp = new OverallStatisticsResp();

        // 房间总数
        resp.setTotalRoomCount(flatRoomMapper.selectCount(q -> q.eq(FlatRoom::getDelStatus, 0)));

        // 公寓总数（房屋种类 kind=0 表示住房）
        resp.setTotalApartmentCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getKind, 0)
                .eq(FlatRoom::getDelStatus, 0)));

        // 商铺总数（房屋种类 kind=1 表示商铺）
        resp.setTotalShopCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getKind, 1)
                .eq(FlatRoom::getDelStatus, 0)));

        // 空闲房间数量（status=0 表示空闲）
        resp.setFreeRoomCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getStatus, 0)
                .eq(FlatRoom::getDelStatus, 0)));

        // 已出租房间数量（status=1 表示已出租）
        resp.setRentedRoomCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getStatus, 1)
                .eq(FlatRoom::getDelStatus, 0)));

        // 脏房数量（status=3 表示脏房）
        resp.setDirtyRoomCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getStatus, 3)
                .eq(FlatRoom::getDelStatus, 0)));

        // 维修中房间数量（status=2 表示维修中）
        resp.setRepairingRoomCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getStatus, 2)
                .eq(FlatRoom::getDelStatus, 0)));

        // 占用状态的房间数量（status=4 表示占用）
        resp.setOccupiedRoomCount(flatRoomMapper.selectCount(q -> q
                .eq(FlatRoom::getStatus, 4)
                .eq(FlatRoom::getDelStatus, 0)));

        // 房租总收入（付款计划类型 payPlanType=1 表示租金计划）
        QueryWrapper<FlatBill> rentQuery = new QueryWrapper<>();
        rentQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "plan")
                .eq("pay_plan_type", 1)
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill rentResult = billMapper.selectOne(rentQuery);
        BigDecimal totalRentIncome = (rentResult != null) ? rentResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalRentIncome(totalRentIncome);

        // 水费总收入
        QueryWrapper<FlatBill> waterQuery = new QueryWrapper<>();
        waterQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "water")
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill waterResult = billMapper.selectOne(waterQuery);
        BigDecimal totalWaterFeeIncome = (waterResult != null) ? waterResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalWaterFeeIncome(totalWaterFeeIncome);

        // 电费总收入
        QueryWrapper<FlatBill> electricQuery = new QueryWrapper<>();
        electricQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "electric")
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill electricResult = billMapper.selectOne(electricQuery);
        BigDecimal totalElectricityFeeIncome = (electricResult != null) ? electricResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalElectricityFeeIncome(totalElectricityFeeIncome);

        // 能耗费总收入（付款计划类型 payPlanType=2 表示能耗费计划）
        QueryWrapper<FlatBill> energyQuery = new QueryWrapper<>();
        energyQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "plan")
                .eq("pay_plan_type", 2)
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill energyResult = billMapper.selectOne(energyQuery);
        BigDecimal totalEnergyFeeIncome = (energyResult != null) ? energyResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalEnergyFeeIncome(totalEnergyFeeIncome);

        // 保洁费总收入
        QueryWrapper<FlatBill> cleanQuery = new QueryWrapper<>();
        cleanQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "clean")
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill cleanResult = billMapper.selectOne(cleanQuery);
        BigDecimal totalCleaningFeeIncome = (cleanResult != null) ? cleanResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalCleaningFeeIncome(totalCleaningFeeIncome);

        // 个性化费用总收入
        QueryWrapper<FlatBill> costQuery = new QueryWrapper<>();
        costQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "cost")
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill costResult = billMapper.selectOne(costQuery);
        BigDecimal totalCustomizedFeeIncome = (costResult != null) ? costResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalCustomizedFeeIncome(totalCustomizedFeeIncome);

        // 物业费总收入（付款计划类型 payPlanType=3 表示物业费计划）
        QueryWrapper<FlatBill> propertyQuery = new QueryWrapper<>();
        propertyQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", "plan")
                .eq("pay_plan_type", 3)
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill propertyResult = billMapper.selectOne(propertyQuery);
        BigDecimal totalPropertyFeeIncome = (propertyResult != null) ? propertyResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalPropertyFeeIncome(totalPropertyFeeIncome);

        // 所有费用总收入
        QueryWrapper<FlatBill> allQuery = new QueryWrapper<>();
        allQuery.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_status", 1)
                .eq("del_status", 0);
        FlatBill allResult = billMapper.selectOne(allQuery);
        BigDecimal totalAllFeeIncome = (allResult != null) ? allResult.getMoney() : BigDecimal.ZERO;
        resp.setTotalAllFeeIncome(totalAllFeeIncome);

        // 总注册用户
        resp.setTotalRegisteredUsers(cusUserMapper.selectCount(q -> q.eq(CusUser::getDelStatus, 0)));

        // 实名认证人数（is_real_name=1 表示已实名认证）
        resp.setVerifiedUserCount(cusUserMapper.selectCount(q -> q
                .eq(CusUser::getIsReal, 1)
                .eq(CusUser::getDelStatus, 0)));

        // 未实名人数
        resp.setUnverifiedUserCount(cusUserMapper.selectCount(q -> q
                .eq(CusUser::getIsReal, 0)
                .eq(CusUser::getDelStatus, 0)));

        // 同住人数量
        resp.setCohabitantCount(flatLiveMapper.selectCount(q -> q.eq(FlatLive::getDelStatus, 0)));

        // 合同数量
        resp.setContractCount(flatContractMapper.selectCount(q -> q.eq(FlatContract::getDelStatus, 0)));

        return resp;
    }

    /**
     * 获取日期范围内每日收入统计
     *
     * @param req 日期范围请求
     * @return 日期范围统计结果
     */
    public DateRangeStatisticsResp getStatisticsByDateRange(DateRangeStatisticsReq req) {
        DateRangeStatisticsResp resp = new DateRangeStatisticsResp();

        // 生成日期范围内的所有日期
        List<LocalDate> dateRange = generateDateRange(req.getStartDate(), req.getEndDate());

        // 初始化每日收入列表
        List<DailyStatisticsDataItem> dailyRentIncome = new ArrayList<>();
        List<DailyStatisticsDataItem> dailyWaterFeeIncome = new ArrayList<>();
        List<DailyStatisticsDataItem> dailyElectricityFeeIncome = new ArrayList<>();
        List<DailyStatisticsDataItem> dailyEnergyFeeIncome = new ArrayList<>();
        List<DailyStatisticsDataItem> dailyCleaningFeeIncome = new ArrayList<>();
        List<DailyStatisticsDataItem> dailyCustomizedFeeIncome = new ArrayList<>();
        List<DailyStatisticsDataItem> dailyPropertyFeeIncome = new ArrayList<>();

        // 遍历每一天，获取当天的统计数据
        for (LocalDate date : dateRange) {
            // 房租收入（付款计划类型 payPlanType=1 表示租金计划）
            BigDecimal rentIncome = getDailyIncome(date, "plan", 1);
            dailyRentIncome.add(new DailyStatisticsDataItem(date, rentIncome));

            // 水费收入
            BigDecimal waterFeeIncome = getDailyIncome(date, "water", null);
            dailyWaterFeeIncome.add(new DailyStatisticsDataItem(date, waterFeeIncome));

            // 电费收入
            BigDecimal electricityFeeIncome = getDailyIncome(date, "electric", null);
            dailyElectricityFeeIncome.add(new DailyStatisticsDataItem(date, electricityFeeIncome));

            // 能耗费收入（付款计划类型 payPlanType=2 表示能耗费计划）
            BigDecimal energyFeeIncome = getDailyIncome(date, "plan", 2);
            dailyEnergyFeeIncome.add(new DailyStatisticsDataItem(date, energyFeeIncome));

            // 保洁费收入
            BigDecimal cleaningFeeIncome = getDailyIncome(date, "clean", null);
            dailyCleaningFeeIncome.add(new DailyStatisticsDataItem(date, cleaningFeeIncome));

            // 个性化费用收入
            BigDecimal customizedFeeIncome = getDailyIncome(date, "cost", null);
            dailyCustomizedFeeIncome.add(new DailyStatisticsDataItem(date, customizedFeeIncome));

            // 物业费收入（付款计划类型 payPlanType=3 表示物业费计划）
            BigDecimal propertyFeeIncome = getDailyIncome(date, "plan", 3);
            dailyPropertyFeeIncome.add(new DailyStatisticsDataItem(date, propertyFeeIncome));
        }

        // 设置返回结果
        resp.setDailyRentIncome(dailyRentIncome);
        resp.setDailyWaterFeeIncome(dailyWaterFeeIncome);
        resp.setDailyElectricityFeeIncome(dailyElectricityFeeIncome);
        resp.setDailyEnergyFeeIncome(dailyEnergyFeeIncome);
        resp.setDailyCleaningFeeIncome(dailyCleaningFeeIncome);
        resp.setDailyCustomizedFeeIncome(dailyCustomizedFeeIncome);
        resp.setDailyPropertyFeeIncome(dailyPropertyFeeIncome);

        return resp;
    }

    /**
     * 获取指定日期的特定类型收入
     *
     * @param date 日期
     * @param payTarget 付款目标类型
     * @param payPlanType 付款计划类型（可为null）
     * @return 当日收入
     */
    private BigDecimal getDailyIncome(LocalDate date, String payTarget, Integer payPlanType) {
        QueryWrapper<FlatBill> query = new QueryWrapper<>();
        query.select("IFNULL(SUM(money), 0) as money")
             .eq("pay_target", payTarget)
             .eq("pay_status", 1)
             .eq("del_status", 0)
             .ge("create_time", date.atStartOfDay())
             .lt("create_time", date.plusDays(1).atStartOfDay());

        // 如果payPlanType不为null，则添加付款计划类型条件
        if (payPlanType != null) {
            query.eq("pay_plan_type", payPlanType);
        }

        FlatBill result = billMapper.selectOne(query);
        return (result != null) ? result.getMoney() : BigDecimal.ZERO;
    }

    /**
     * 生成起止日期之间的所有日期列表（包含起止日期）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期列表
     */
    private List<LocalDate> generateDateRange(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dateRange = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            dateRange.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }

        return dateRange;
    }

    /**
     * 获取指定年份每月收入统计
     *
     * @param req 年度统计请求
     * @return 年度统计结果
     */
    public YearlyStatisticsResp getStatisticsByYear(YearlyStatisticsReq req) {
        YearlyStatisticsResp resp = new YearlyStatisticsResp();

        // 初始化每月收入列表
        List<MonthlyStatisticsDataItem> monthlyRentIncome = new ArrayList<>();
        List<MonthlyStatisticsDataItem> monthlyWaterFeeIncome = new ArrayList<>();
        List<MonthlyStatisticsDataItem> monthlyElectricityFeeIncome = new ArrayList<>();
        List<MonthlyStatisticsDataItem> monthlyEnergyFeeIncome = new ArrayList<>();
        List<MonthlyStatisticsDataItem> monthlyCleaningFeeIncome = new ArrayList<>();
        List<MonthlyStatisticsDataItem> monthlyCustomizedFeeIncome = new ArrayList<>();
        List<MonthlyStatisticsDataItem> monthlyPropertyFeeIncome = new ArrayList<>();

        // 遍历每个月，获取当月的统计数据
        for (int month = 1; month <= 12; month++) {
            // 获取当月第一天和最后一天
            LocalDate firstDayOfMonth = LocalDate.of(req.getYear(), month, 1);
            LocalDate lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth());

            // 房租收入（付款计划类型 payPlanType=1 表示租金计划）
            BigDecimal rentIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "plan", 1);
            monthlyRentIncome.add(new MonthlyStatisticsDataItem(month, rentIncome));

            // 水费收入
            BigDecimal waterFeeIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "water", null);
            monthlyWaterFeeIncome.add(new MonthlyStatisticsDataItem(month, waterFeeIncome));

            // 电费收入
            BigDecimal electricityFeeIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "electric", null);
            monthlyElectricityFeeIncome.add(new MonthlyStatisticsDataItem(month, electricityFeeIncome));

            // 能耗费收入（付款计划类型 payPlanType=2 表示能耗费计划）
            BigDecimal energyFeeIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "plan", 2);
            monthlyEnergyFeeIncome.add(new MonthlyStatisticsDataItem(month, energyFeeIncome));

            // 保洁费收入
            BigDecimal cleaningFeeIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "clean", null);
            monthlyCleaningFeeIncome.add(new MonthlyStatisticsDataItem(month, cleaningFeeIncome));

            // 个性化费用收入
            BigDecimal customizedFeeIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "cost", null);
            monthlyCustomizedFeeIncome.add(new MonthlyStatisticsDataItem(month, customizedFeeIncome));

            // 物业费收入（付款计划类型 payPlanType=3 表示物业费计划）
            BigDecimal propertyFeeIncome = getMonthlyIncome(firstDayOfMonth, lastDayOfMonth, "plan", 3);
            monthlyPropertyFeeIncome.add(new MonthlyStatisticsDataItem(month, propertyFeeIncome));
        }

        // 设置返回结果
        resp.setMonthlyRentIncome(monthlyRentIncome);
        resp.setMonthlyWaterFeeIncome(monthlyWaterFeeIncome);
        resp.setMonthlyElectricityFeeIncome(monthlyElectricityFeeIncome);
        resp.setMonthlyEnergyFeeIncome(monthlyEnergyFeeIncome);
        resp.setMonthlyCleaningFeeIncome(monthlyCleaningFeeIncome);
        resp.setMonthlyCustomizedFeeIncome(monthlyCustomizedFeeIncome);
        resp.setMonthlyPropertyFeeIncome(monthlyPropertyFeeIncome);

        return resp;
    }

    /**
     * 获取指定月份内的收入
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param payTarget 支付对象
     * @param payPlanType 支付计划类型
     * @return 指定月份内的收入
     */
    private BigDecimal getMonthlyIncome(LocalDate startDate, LocalDate endDate, String payTarget, Integer payPlanType) {
        QueryWrapper<FlatBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(SUM(money), 0) as money")
                .eq("pay_target", payTarget)
                .eq("pay_status", 1)
                .eq("del_status", 0)
                .ge("pay_time", startDate)
                .le("pay_time", endDate);

        if (payPlanType != null) {
            queryWrapper.eq("pay_plan_type", payPlanType);
        }

        FlatBill bill = billMapper.selectOne(queryWrapper);
        return bill != null ? bill.getMoney() : BigDecimal.ZERO;
    }

    /**
     * 根据用户ID查询用户的多个租赁记录相关数据
     *
     * @param cusId 用户ID
     * @return 用户的租赁记录统计信息列表
     */
    public List<SimpleLeaseStatisticsResp> getLeaseStatisticsByCusId(Long cusId) {
        List<SimpleLeaseStatisticsResp> resultList = new ArrayList<>();

        // 查询用户所有未删除的租赁记录
        List<FlatLease> leaseList = leaseMapper.selectList(q -> q
                .eq(FlatLease::getCusId, cusId)
                .eq(FlatLease::getDelStatus, 0)
                .in(FlatLease::getStatus, 0, 2, 3, 5, 7)
        );

        if (leaseList == null || leaseList.isEmpty()) {
            return resultList;
        }

        // 处理每条租赁记录
        for (FlatLease lease : leaseList) {
            SimpleLeaseStatisticsResp resp = new SimpleLeaseStatisticsResp();

            // 设置租赁基本信息
            resp.setLeaseId(lease.getId());
            resp.setCusId(lease.getCusId());
            resp.setRoomId(lease.getRoomId());
            resp.setPayPeriod(lease.getPayPeriod());

            // 查询房间信息
            FlatRoom room = flatRoomMapper.selectById(lease.getRoomId());
            if (room != null) {
                resp.setRoomName(room.getName());
                resp.setRoomKind(room.getKind());
                resp.setRoomNo(room.getRoomNo());
            }

            // 设置公司信息
            FlatCompany company = flatCompanyMapper.selectById(lease.getCompanyId());
            if (company != null) {
                resp.setCompanySymbol(company.getSymbol());
                resp.setCompanyName(company.getCompanyName());
            }

            // 查询合同信息，计算剩余天数
            FlatContract contract = flatContractMapper.selectById(lease.getContractId());

            if (contract != null && contract.getEndTime() != null) {
                LocalDateTime endDateTime = contract.getEndTime();
                LocalDate endDate = endDateTime.toLocalDate();
                LocalDate now = LocalDate.now();

                if (now.isAfter(endDate)) {
                    // 合同已过期
                    resp.setRemainingDays(-1);
                } else {
                    // 计算剩余天数
                    resp.setRemainingDays((int)java.time.temporal.ChronoUnit.DAYS.between(now, endDate));
                }
            } else {
                resp.setRemainingDays(null);
            }

            resp.setWaterBalance(lease.getWaterAccountBalance());
            resp.setElectricBalance(lease.getElectricAccountBalance());

            // 此处需要查询当前房租周期、当前能耗费周期、当前物业费周期
            // 当前日期
            LocalDate today = LocalDate.now();

            // 查询当前周期的房租付款计划
            Integer rentPaidStatus = -1; // 默认为-1，表示周期不存在
            QueryWrapper<FlatPayPlan> rentPlanQuery = new QueryWrapper<>();
            rentPlanQuery.eq("lease_id", lease.getId())
                    .eq("type", 1) // 1=租金计划
                    .eq("invalid_flag", 0) // 未作废
                    .eq("del_status", 0) // 未删除
                    .apply("DATE_FORMAT(begin_time, '%Y-%m-%d') <= DATE_FORMAT({0}, '%Y-%m-%d')", today) // 开始日期小于等于今天
                    .apply("DATE_FORMAT(end_time, '%Y-%m-%d') >= DATE_FORMAT({0}, '%Y-%m-%d')", today) // 结束日期大于等于今天
                    .orderByDesc("id") // 按ID降序排序，获取最新的
                    .last("LIMIT 1");

            FlatPayPlan rentPlan = flatPayPlanMapper.selectOne(rentPlanQuery);
            if (rentPlan != null) {
                rentPaidStatus = rentPlan.getPayStatus(); // 0=未支付;1=已支付
            }
            resp.setRentPaidStatus(rentPaidStatus);

            // 查询当前周期的能耗费付款计划
            Integer energyPaidStatus = -1; // 默认为-1，表示周期不存在
            QueryWrapper<FlatPayPlan> energyPlanQuery = new QueryWrapper<>();
            energyPlanQuery.eq("lease_id", lease.getId())
                    .eq("type", 2) // 2=能耗费计划
                    .eq("invalid_flag", 0) // 未作废
                    .eq("del_status", 0) // 未删除
                    .apply("DATE_FORMAT(begin_time, '%Y-%m-%d') <= DATE_FORMAT({0}, '%Y-%m-%d')", today) // 开始日期小于等于今天
                    .apply("DATE_FORMAT(end_time, '%Y-%m-%d') >= DATE_FORMAT({0}, '%Y-%m-%d')", today) // 结束日期大于等于今天
                    .orderByDesc("id") // 按ID降序排序，获取最新的
                    .last("LIMIT 1");

            FlatPayPlan energyPlan = flatPayPlanMapper.selectOne(energyPlanQuery);
            if (energyPlan != null) {
                energyPaidStatus = energyPlan.getPayStatus(); // 0=未支付;1=已支付
            }
            resp.setEnergyPaidStatus(energyPaidStatus);

            // 查询当前周期的物业费付款计划（商铺）
            Integer shopPropertyPaidStatus = -1; // 默认为-1，表示周期不存在
            QueryWrapper<FlatPayPlan> propertyPlanQuery = new QueryWrapper<>();
            propertyPlanQuery.eq("lease_id", lease.getId())
                    .eq("type", 3) // 3=物业费计划(商铺)
                    .eq("invalid_flag", 0) // 未作废
                    .eq("del_status", 0) // 未删除
                    .apply("DATE_FORMAT(begin_time, '%Y-%m-%d') <= DATE_FORMAT({0}, '%Y-%m-%d')", today) // 开始日期小于等于今天
                    .apply("DATE_FORMAT(end_time, '%Y-%m-%d') >= DATE_FORMAT({0}, '%Y-%m-%d')", today) // 结束日期大于等于今天
                    .orderByDesc("id") // 按ID降序排序，获取最新的
                    .last("LIMIT 1");

            FlatPayPlan propertyPlan = flatPayPlanMapper.selectOne(propertyPlanQuery);
            if (propertyPlan != null) {
                shopPropertyPaidStatus = propertyPlan.getPayStatus(); // 0=未支付;1=已支付
            }
            resp.setShopPropertyPaidStatus(shopPropertyPaidStatus);

            resultList.add(resp);
        }

        return resultList;
    }


}
