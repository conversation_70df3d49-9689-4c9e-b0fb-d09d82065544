package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.VerifyReq;
import com.flat.logic.dto.req.make.VisitMakeAddReq;
import com.flat.logic.dto.req.make.VisitMakeQueryReq;
import com.flat.logic.dto.resp.make.VisitMakeDetailResp;
import com.flat.logic.dto.resp.make.VisitMakeListResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.entity.make.FlatVisitMake;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.mapper.make.FlatVisitMakeMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公寓访客Service业务层处理
 */
@Service
public class VisitMakeService {

    @Resource
    private FlatVisitMakeMapper visitMakeMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private MessageService messageService;

    public VisitMakeDetailResp queryRespDetail(Long id) {
        FlatVisitMake visitMake = visitMakeMapper.selectById(id);
        if (visitMake == null || visitMake.getDelStatus() == 1) {
            throw new RuntimeException("访客记录不存在");
        }

        FlatRoom room = roomMapper.selectById(visitMake.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new RuntimeException("房源不存在");
        }

        VisitMakeDetailResp response = new VisitMakeDetailResp();
        BeanUtils.copyProperties(visitMake, response);
        response.setRoomName(room.getName());

        String icon = sysConfigService.queryConfigByKey("VISITOR_ICON");

        CusUser cusUser = cusUserMapper.selectById(visitMake.getCusId());
        if (ObjectUtils.isNotEmpty(cusUser) && ObjectUtils.isNotEmpty(cusUser.getNickName())) {
            response.setCusRealName(cusUser.getRealName());
        }
        response.setIcon(icon);
        response.setTitle("预约访客");

        List<FlatMakeLog> logs = makeLogMapper.selectList(q -> q
                .eq(FlatMakeLog::getSourceId, visitMake.getId())
                .eq(FlatMakeLog::getType, "make_visitor")
                .eq(FlatMakeLog::getDelStatus, 0)
                .orderByAsc(FlatMakeLog::getCreateTime)
        );
        response.setLogs(logs);

        return response;
    }

    public TablePage<VisitMakeListResp> queryRespPage(VisitMakeQueryReq req) {
        String icon = sysConfigService.queryConfigByKey("VISITOR_ICON");
        return PageUtils.paginate(
                () -> visitMakeMapper.selectRespList(req),
                (e) -> {
                    e.setIcon(icon);
                    e.setTitle("预约访客");
                    return e;
                }
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(VisitMakeAddReq req, CusUser cusUser) {
        FlatRoom room = roomMapper.selectById(req.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        FlatLease lease = leaseMapper.selectFirst(q -> q
                .eq(FlatLease::getRoomId, req.getRoomId())
                .eq(FlatLease::getCusId, cusUser.getUserId())
                .eq(FlatLease::getDelStatus, 0)
                .eq(FlatLease::getStatus, 2)
        );

        FlatLive live = liveMapper.selectFirst(q -> q
                .eq(FlatLive::getRoomId, req.getRoomId())
                .eq(FlatLive::getLiveCusId, cusUser.getUserId())
                .eq(FlatLive::getDelStatus, 0)
                .eq(FlatLive::getStatus, 2)
        );

        if(lease == null && live == null) {
            throw new ServiceException("未找到租赁或同住记录");
        }

        FlatVisitMake visitMake = new FlatVisitMake();
        BeanUtils.copyProperties(req, visitMake);
        visitMake.setFlatId(room.getFlatId());
        if(lease != null) {
            visitMake.setApplyCusType(1);
        } else {
            visitMake.setApplyCusType(2);
        }
        visitMake.setCusId(cusUser.getUserId());
        visitMake.setLeaseId(lease != null ? lease.getId() : live.getLeaseId());
        visitMake.setButlerId(room.getButlerId());
        visitMake.setPropertyId(room.getPropertyId());
        visitMake.setStatus(0);
        visitMake.setCusApplyTime(LocalDateTime.now());
        visitMake.setCreateBy(cusUser.getNickName());
        visitMake.setCreateTime(LocalDateTime.now());
        visitMake.setDelStatus(0);
        visitMakeMapper.insert(visitMake);

        // 记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(visitMake.getId());
        log.setType("make_visitor");
        log.setRefuse("预约访客成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        messageService.cusToButler(
                cusUser.getUserId(), room.getButlerId(),
                cusUser.getNickName() + "预约访客",
                "/pages/butler/home/<USER>/info/index?id=" + visitMake.getId() + "&type=4"
        );

    }

    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long visitMakeId, CusUser cusUser) {
        // 只能撤销待审核未支付的订单
        FlatVisitMake visitMake = visitMakeMapper.selectById(visitMakeId);
        if(visitMake == null) {
            throw new ServiceException("预约访客信息不存在");
        }

        if(visitMake.getStatus() == 1) {
            throw new ServiceException("访客预约已撤销");
        }

        if(visitMake.getStatus() != 0) {
            throw new ServiceException("访客预约已处理，无法撤销");
        }

        visitMake.setUpdateBy(cusUser.getNickName());
        visitMake.setStatus(1);
        visitMake.setCancelTime(LocalDateTime.now());
        visitMakeMapper.updateById(visitMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(visitMake.getId());
        log.setType("make_visitor");
        log.setRefuse("预约访客用户撤销成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);
    }

    public void verify(VerifyReq req, MaiUser maiUser) {
        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatVisitMake visitMake = visitMakeMapper.selectById(req.getId());
        if (visitMake == null || visitMake.getDelStatus() == 1) {
            throw new ServiceException("预约信息不存在");
        }

        if (visitMake.getStatus() == 1) {
            throw new ServiceException("预约已撤销");
        }

        if(visitMake.getStatus() != 0) {
            throw new ServiceException("该预约管家已审核");
        }

        if(req.getStatus() == 1) {
            visitMake.setStatus(3);
        } else {
            visitMake.setStatus(2);
            visitMake.setRefuse(req.getRefuse());
        }
        visitMake.setButlerId(maiUser.getUserId());
        visitMake.setButlerVerifyTime(LocalDateTime.now());
        visitMake.setUpdateBy(maiUser.getNickName());
        visitMake.setUpdateTime(LocalDateTime.now());
        visitMakeMapper.updateById(visitMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getNickName());
        log.setSourceId(visitMake.getId());
        log.setType("make_visitor");
        log.setRefuse(req.getStatus() == 1 ? "访客预约管家审核通过" : "访客预约管家审核未通过");
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        if(req.getStatus() == 1) { // 审核通过，通知用户
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    visitMake.getCusId(),
                    "访客预约管家审核通过",
                    "/pages/user/viewRecord/info/index?id=" + visitMake.getId() + "&type=4"
            );
        } else { // 审核未通过，通知用户
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    visitMake.getCusId(),
                    "访客预约管家审核未通过",
                    "/pages/user/viewRecord/info/index?id=" + visitMake.getId() + "&type=4"
            );
        }
    }

    public void remove(Long id, SysUser user) {
        visitMakeMapper.update(q -> q
                .set(FlatVisitMake::getDelStatus, 1)
                .set(FlatVisitMake::getUpdateTime, LocalDateTime.now())
                .set(FlatVisitMake::getUpdateBy, user.getUsername())
                .eq(FlatVisitMake::getId, id)
                .eq(FlatVisitMake::getDelStatus, 0)
        );
    }
}
