package com.flat.logic.service.make;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.common.utils.bean.BeanUtils;
import com.flat.logic.commons.BillConstants;
import com.flat.logic.dto.req.VerifyReq;
import com.flat.logic.dto.req.make.CleanMakeAddReq;
import com.flat.logic.dto.req.make.CleanMakePayReq;
import com.flat.logic.dto.req.make.CleanMakePropertyVerifyReq;
import com.flat.logic.dto.req.make.CleanMakeQueryReq;
import com.flat.logic.dto.resp.flat.RoomResp;
import com.flat.logic.dto.resp.make.CleanMakeDetailResp;
import com.flat.logic.dto.resp.make.CleanMakeListResp;
import com.flat.logic.dto.resp.make.CleanMakePayResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.coupon.Coupon;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.entity.make.FlatCleanMake;
import com.flat.logic.entity.make.FlatCleanType;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.coupon.CouponMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.mapper.make.FlatCleanMakeMapper;
import com.flat.logic.mapper.make.FlatCleanTypeMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.logic.service.finance.BillService;
import com.flat.logic.service.flat.RoomService;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.Resource;

/**
 * 公寓预约保洁Service业务层处理
 */
@Service
public class CleanMakeService {

    @Resource
    private FlatCleanMakeMapper cleanMakeMapper;

    @Resource
    private FlatCleanTypeMapper cleanTypeMapper;

    @Resource
    private RoomService roomService;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private CleanEvaluateService cleanEvaluateService;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private BillService billService;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    public TablePage<CleanMakeListResp> queryRespPage(CleanMakeQueryReq req) {
        String icon = sysConfigService.queryConfigByKey("CLEAN_ICON");
        return PageUtils.paginate(
                () -> cleanMakeMapper.selectRespList(req),
                (e) -> {
                    e.setIcon(icon);
                    e.setTitle("预约保洁");
                    return e;
                }
        );
    }

    public CleanMakeDetailResp queryRespDetail(Long id) {
        CleanMakeListResp listResp = cleanMakeMapper.selectRespById(id);
        if (listResp == null) {
            throw new ServiceException("保洁预约记录不存在");
        }

        String icon = sysConfigService.queryConfigByKey("CLEAN_ICON");

        CleanMakeDetailResp response = new CleanMakeDetailResp();
        BeanUtils.copyProperties(listResp, response);
        response.setIcon(icon);
        RoomResp roomResponse = roomService.queryRespDetail(listResp.getRoomId());
        if (roomResponse != null) {
            response.setRoomResp(roomResponse);
        }
        response.setIcon(icon);
        response.setTitle("预约保洁");
        response.setEvaluate(cleanEvaluateService.queryDetailByCleanMakeId(response.getId()));

        List<FlatMakeLog> logs = makeLogMapper.selectList(q -> q
                .eq(FlatMakeLog::getSourceId, response.getId())
                .eq(FlatMakeLog::getType, "make_clean")
                .eq(FlatMakeLog::getDelStatus, 0)
                .orderByAsc(FlatMakeLog::getCreateTime)
        );
        if (logs != null && !logs.isEmpty()) {
            response.setLogs(logs);
        }

        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public FlatCleanMake add(CleanMakeAddReq req, CusUser cusUser) {
        FlatRoom room = roomMapper.selectById(req.getRoomId());
        if(room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房间信息不存在");
        }

        FlatCleanType cleanType = cleanTypeMapper.selectById(req.getCleanTypeId());
        if(cleanType == null || cleanType.getDelStatus() == 1) {
            throw new ServiceException("保洁项目不存在");
        }

        FlatLease lease = leaseMapper.selectFirst(q -> q
                .eq(FlatLease::getRoomId, req.getRoomId())
                .eq(FlatLease::getCusId, cusUser.getUserId())
                .eq(FlatLease::getDelStatus, 0)
                .eq(FlatLease::getStatus, 2)
        );

        if(lease != null && lease.getInvalidFlag() == 1) {
            throw new ServiceException("该租赁已作废");
        }

        FlatLive live = liveMapper.selectFirst(q -> q
                .eq(FlatLive::getRoomId, req.getRoomId())
                .eq(FlatLive::getLiveCusId, cusUser.getUserId())
                .eq(FlatLive::getDelStatus, 0)
                .eq(FlatLive::getStatus, 2)
        );

        if(lease == null && live == null) {
            throw new ServiceException("未找到租赁或同住记录");
        }

        FlatCleanMake cleanMake = new FlatCleanMake();
        BeanUtils.copyProperties(req, cleanMake);
        cleanMake.setFlatId(room.getFlatId());
        if(lease != null) {
            cleanMake.setApplyCusType(1);
        } else {
            cleanMake.setApplyCusType(2);
        }
        cleanMake.setCusId(cusUser.getUserId());
        cleanMake.setLeaseId(lease != null ? lease.getId() : live.getLeaseId());
        cleanMake.setButlerId(room.getButlerId());
        cleanMake.setPropertyId(room.getPropertyId());
        cleanMake.setMakeTime(LocalDateTime.now());
        cleanMake.setNeedPayFlag(cleanType.getNeedPayFlag());
        if(cleanType.getNeedPayFlag() == 1) {
            cleanMake.setMoney(cleanType.getPrice());
        }

        cleanMake.setPayStatus(0);
        cleanMake.setStatus(0);
        cleanMake.setMakeClient(0);
        cleanMake.setEvaluateFlag(0);
        cleanMake.setCreateBy(cusUser.getNickName());
        cleanMake.setCreateTime(LocalDateTime.now());
        cleanMake.setDelStatus(0);
        cleanMakeMapper.insert(cleanMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(cleanMake.getId());
        log.setType("make_clean");
        log.setRefuse("预约保洁提交成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        // 消息通知
        messageService.cusToButler(
                cusUser.getUserId(),
                room.getButlerId(),
                "预约保洁",
                "/pages/butler/home/<USER>/info/index?id=" + cleanMake.getId() + "&type=1"
        );

        return cleanMake;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long cleanMakeId, CusUser cusUser) {
        // 只能撤销待审核未支付的订单
        FlatCleanMake flatClean = cleanMakeMapper.selectById(cleanMakeId);
        if(flatClean == null) {
            throw new ServiceException("预约保洁信息不存在");
        }

        if(flatClean.getStatus() == 1) {
            throw new ServiceException("保洁预约已撤销");
        }

        if(flatClean.getStatus() != 0) {
            throw new ServiceException("保洁预约已处理，无法撤销");
        }

        if (flatClean.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，无法撤销");
        }

        flatClean.setUpdateBy(cusUser.getNickName());
        flatClean.setStatus(1);
        flatClean.setCancelTime(LocalDateTime.now());
        cleanMakeMapper.updateById(flatClean);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(flatClean.getId());
        log.setType("make_clean");
        log.setRefuse("预约保洁撤销成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        //撤销成功后删除账单
        billMapper.update(wrapper -> wrapper
                .eq(FlatBill::getCleanMakeId, flatClean.getId())
                .eq(FlatBill::getPayStatus, 0)
                .eq(FlatBill::getPayTarget, BillConstants.CLEAN)
                .eq(FlatBill::getCusId, cusUser.getUserId())
                .eq(FlatBill::getDelStatus, 0)
                .set(FlatBill::getDelStatus, 1)
                .set(FlatBill::getUpdateTime, LocalDateTime.now())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void verifyForButler(VerifyReq req, MaiUser maiUser) {
        if(!"BUTLER".equalsIgnoreCase(maiUser.getUserType())) {
            throw new ServiceException("只有管家用户才能审核");
        }

        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatCleanMake cleanMake = cleanMakeMapper.selectById(req.getId());
        if(cleanMake == null) {
            throw new ServiceException("保洁预约信息不存在");
        }

        if(cleanMake.getStatus() == 1) {
            throw new ServiceException("保洁预约已撤销");
        }

        if(cleanMake.getNeedPayFlag() == 1 && cleanMake.getPayStatus() == 0) {
            throw new ServiceException("账单未支付，无法审核");
        }

        if(cleanMake.getStatus() != 0) {
            throw new ServiceException("保洁预约管家已审核");
        }

        if(req.getStatus() == 1) {
            cleanMake.setStatus(3);
        } else {
            cleanMake.setStatus(2);
            cleanMake.setRefuse(req.getRefuse());
        }
        cleanMake.setButlerId(maiUser.getUserId());
        cleanMake.setButlerVerifyTime(LocalDateTime.now());
        cleanMake.setUpdateBy(maiUser.getNickName());
        cleanMake.setUpdateTime(LocalDateTime.now());
        cleanMakeMapper.updateById(cleanMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getNickName());
        log.setSourceId(cleanMake.getId());
        log.setType("make_clean");
        log.setRefuse(req.getStatus() == 1 ? "保洁预约管家审核通过" : "保洁预约管家审核未通过");
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        if(req.getStatus() == 1) { // 审核通过，通知用户和物业
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    cleanMake.getCusId(),
                    "保洁预约管家审核通过",
                    "/pages/user/viewRecord/info/index?id=" + cleanMake.getId() + "&type=1"
            );

            messageService.butlerToProperty(
                    maiUser.getUserId(),
                    cleanMake.getPropertyId(),
                    "保洁预约管家审核通过",
                    "/pages/property/info/index?id=" + cleanMake.getId() + "&type=1"
            );
        } else { // 审核未通过，通知用户
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    cleanMake.getCusId(),
                    "保洁预约管家审核未通过",
                    "/pages/user/viewRecord/info/index?id=" + cleanMake.getId() + "&type=1"
            );
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void verifyForProperty(CleanMakePropertyVerifyReq req, MaiUser maiUser) {
        if(!"PROPERTY".equalsIgnoreCase(maiUser.getUserType())) {
            throw new ServiceException("只有物业用户才能审核");
        }

        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatCleanMake cleanMake = cleanMakeMapper.selectById(req.getId());
        if(cleanMake == null) {
            throw new ServiceException("保洁预约信息不存在");
        }

        if(cleanMake.getStatus() == 1) {
            throw new ServiceException("保洁预约已撤销");
        }

        if(cleanMake.getNeedPayFlag() == 1 && cleanMake.getPayStatus() == 0) {
            throw new ServiceException("账单未支付，无法审核");
        }

        if(cleanMake.getStatus() > 3) {
            throw new ServiceException("保洁预约物业已审核");
        }

        if(cleanMake.getStatus() != 3) {
            throw new ServiceException("保洁预约管家未审核通过，物业无法审核");
        }

        if(req.getStatus() == 1) {
            cleanMake.setStatus(5);
        } else {
            cleanMake.setStatus(4);
            cleanMake.setRefuse(req.getRefuse());
        }
        cleanMake.setFinishTime(req.getFinishTime());
        cleanMake.setFinishUrl(req.getFinishUrl());
        cleanMake.setStaffId(req.getStaffId());
        cleanMake.setPropertyId(maiUser.getUserId());
        cleanMake.setPropertyVerifyTime(LocalDateTime.now());
        cleanMake.setUpdateBy(maiUser.getNickName());
        cleanMake.setUpdateTime(LocalDateTime.now());
        cleanMakeMapper.updateById(cleanMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getNickName());
        log.setSourceId(cleanMake.getId());
        log.setType("make_clean");
        log.setRefuse(req.getStatus() == 1 ? "保洁预约物业审核通过" : "保洁预约物业审核未通过");
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        if(req.getStatus() == 1) { // 审核通过，通知管家和用户
            messageService.propertyToCus(
                    maiUser.getUserId(),
                    cleanMake.getCusId(),
                    "保洁预约物业审核通过",
                    "/pages/user/viewRecord/info/index?id=" + cleanMake.getId() + "&type=1"
            );

            messageService.propertyToButler(
                    maiUser.getUserId(),
                    cleanMake.getButlerId(),
                    "保洁预约物业审核通过",
                    "/pages/butler/home/<USER>/info/index?id=" + cleanMake.getId() + "&type=1"
            );
        } else { // 审核未通过，通知用户和管家
            messageService.propertyToCus(
                    maiUser.getUserId(),
                    cleanMake.getCusId(),
                    "保洁预约物业审核未通过",
                    "/pages/user/viewRecord/info/index?id=" + cleanMake.getId() + "&type=1"
            );

            messageService.propertyToButler(
                    maiUser.getUserId(),
                    cleanMake.getButlerId(),
                    "保洁预约物业审核未通过",
                    "/pages/butler/home/<USER>/info/index?id=" + cleanMake.getId() + "&type=1"
            );
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public CleanMakePayResp pay(CleanMakePayReq req, CusUser cusUser) {
        FlatCleanMake cleanMake = cleanMakeMapper.selectById(req.getCleanId());
        if(cleanMake == null) {
            throw new ServiceException("预约保洁信息不存在");
        }

        if(cleanMake.getStatus() == 1) {
            throw new ServiceException("保洁预约已撤销");
        }

        if(cleanMake.getNeedPayFlag() == 0) {
            throw new ServiceException("该预约无需支付");
        }

        if (cleanMake.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，无需重复支付");
        }

        FlatCompany company = companyMapper.selectBySymbol("by");
        if (company == null) {
            throw new ServiceException("收款企业不存在");
        }

        // 优惠券处理
        BigDecimal discountAmount = BigDecimal.ZERO;
        Coupon coupon = null;
        if (req.getCouponId() != null) {
            coupon = couponMapper.selectById(req.getCouponId());
            if (coupon == null || coupon.getDelStatus() == 1) {
                throw new ServiceException("优惠券不存在");
            }
            
            if (!Objects.equals(coupon.getCusId(), cusUser.getUserId())) {
                throw new ServiceException("不能使用他人的优惠券");
            }

            if (coupon.getVerifyStatus() != 3) {
                throw new ServiceException("优惠券未审核通过");
            }
            
            if (coupon.getTarget() != 1) {
                throw new ServiceException("只能使用保洁券");
            }
            
            if (coupon.getUseStatus() == 1) {
                throw new ServiceException("优惠券已使用");
            }
            
            if (coupon.getValidBeginTime().isAfter(LocalDateTime.now())) {
                throw new ServiceException("优惠券未开始");
            }
            
            if (coupon.getValidEndTime().isBefore(LocalDateTime.now())) {
                throw new ServiceException("优惠券已过期");
            }
            
            BigDecimal cleanMakeMoney = cleanMake.getMoney();
            BigDecimal couponAmount = new BigDecimal(coupon.getAmount());
            
            if (couponAmount.compareTo(cleanMakeMoney) > 0) {
                throw new ServiceException("优惠券额度不能大于保洁费");
            }
            
            discountAmount = couponAmount;
        }

        // 查找账单
        FlatBill bill = billMapper.selectFirst(q -> q
                .eq(FlatBill::getCleanMakeId, cleanMake.getId())
                .eq(FlatBill::getPayTarget, BillConstants.CLEAN)
                .eq(FlatBill::getCusId, cusUser.getUserId())
                .eq(FlatBill::getDelStatus, 0)
        );
        
        BigDecimal cleanMakeMoney = cleanMake.getMoney();
        BigDecimal actualMoney = cleanMakeMoney.subtract(discountAmount);
        
        if(bill == null) {
            String outTradeNo = "CLN" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);
            bill = new FlatBill();
            bill.setOrderNo(outTradeNo);
            bill.setMoney(actualMoney);
            bill.setPayChannel(1);
            bill.setCusId(cleanMake.getCusId()); // 属于用户的账单
            bill.setPayTarget(BillConstants.CLEAN);
            bill.setReadStatus(0);
            bill.setFlatId(cleanMake.getFlatId());
            bill.setRoomId(cleanMake.getRoomId());
            bill.setLeaseId(cleanMake.getLeaseId());
            bill.setUseCouponFlag(0);
            bill.setPayMode(0);
            bill.setPayStatus(0);
            bill.setInvoiceStatus(0);
            bill.setCleanMakeId(cleanMake.getId());
            bill.setCreateBy(cusUser.getNickName());
            bill.setCreateTime(LocalDateTime.now());
            bill.setDelStatus(0);
            bill.setName("保洁预约付款");
            bill.setReceiveCompanyId(company.getId());
            billMapper.insert(bill);
        } else {
            if(bill.getPayStatus() == 1) {
                throw new ServiceException("账单已支付，请勿重复支付");
            }
            // 更新账单金额
            bill.setMoney(actualMoney);
            billMapper.updateById(bill);
        }
        
        // 如果使用了优惠券且优惠券金额能够完全抵扣保洁费
        if (coupon != null && discountAmount.compareTo(cleanMakeMoney) >= 0) {
            // 更新优惠券状态
            coupon.setUseStatus(1);
            coupon.setUseTime(LocalDateTime.now());
            coupon.setUseBillId(bill.getId());
            coupon.setUpdateBy(cusUser.getNickName());
            coupon.setUpdateTime(LocalDateTime.now());
            couponMapper.updateById(coupon);
            
            // 更新账单状态为已支付
            bill.setPayStatus(1);
            bill.setUseCouponFlag(1);
            bill.setCouponId(coupon.getId());
            bill.setPayTime(LocalDateTime.now());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);
            
            // 更新保洁预约状态为已支付
            cleanMake.setPayStatus(1);
            cleanMake.setPayMode(2); // 2表示优惠券支付
            cleanMake.setUpdateTime(LocalDateTime.now());
            cleanMakeMapper.updateById(cleanMake);
            
            // 记录操作流程
            FlatMakeLog log = new FlatMakeLog();
            log.setOperateId(bill.getCusId());
            log.setOperateName(bill.getCreateBy());
            log.setSourceId(bill.getCleanMakeId());
            log.setType("make_clean");
            log.setRefuse("预约保洁使用优惠券支付成功");
            log.setDelStatus(0);
            log.setCreateBy(cusUser.getNickName());
            log.setCreateTime(LocalDateTime.now());
            makeLogMapper.insert(log);
            
            // 返回不需要支付的响应
            return CleanMakePayResp.noPay();
        } else if (coupon != null) {
            bill.setUseCouponFlag(1);
            bill.setCouponId(coupon.getId());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);

            // 如果使用了优惠券但不能完全抵扣，先更新优惠券状态
            coupon.setUseStatus(1);
            coupon.setUseTime(LocalDateTime.now());
            coupon.setUseBillId(bill.getId());
            coupon.setUpdateBy(cusUser.getNickName());
            coupon.setUpdateTime(LocalDateTime.now());
            couponMapper.updateById(coupon);
        }
        
        // 需要调用微信支付
        WxPayMpOrderResult wxPayResult = billService.pay(bill, company, cusUser);
        return CleanMakePayResp.needPay(wxPayResult);
    }

    /**
     * 处理支付
     * @param outTradeNo 商户订单号
     * @param xmlData 微信支付回调结果
     * @throws WxPayException 微信支付异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlePay(String outTradeNo, String xmlData) throws WxPayException {
        //根据outTradeNo查询账单
        FlatPayLog payLog = payLogMapper.selectFirst(q -> q
                .eq(FlatPayLog::getOutTradeNo, outTradeNo)
                .eq(FlatPayLog::getChannel, 0)
                .eq(FlatPayLog::getDelStatus, 0)
        );
        if(payLog == null) {
            throw new ServiceException("渠道支付记录未找到");
        }

        FlatBill bill = billMapper.selectById(payLog.getBillId());
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单未找到");
        }

        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        FlatCleanMake cleanMake = cleanMakeMapper.selectById(bill.getCleanMakeId());
        if (cleanMake == null || cleanMake.getDelStatus() == 1) {
            throw new ServiceException("保洁预约记录未找到");
        }

        FlatCompany company = companyMapper.selectById(bill.getReceiveCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        CusUser cusUser = cusUserMapper.selectById(cleanMake.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        WxPayOrderNotifyResult result = weChatPayService.parseOrderNotifyResult(company.getWxPayAppId(), company.getWxPayMchId(), xmlData);
        if(!"SUCCESS".equalsIgnoreCase(result.getResultCode())) {
            throw new ServiceException("支付失败");
        }

        payLog.setPayStatus(1);
        payLog.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payLog.setTransactionNo(result.getTransactionId());
        payLog.setUpdateTime(LocalDateTime.now());
        payLogMapper.updateById(payLog);

        bill.setPayStatus(1);
        bill.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        bill.setTransactionNo(result.getTransactionId());
        bill.setUpdateTime(LocalDateTime.now());
        billMapper.updateById(bill);

        cleanMake.setPayStatus(1);
        cleanMake.setPayMode(0);
        cleanMake.setUpdateTime(LocalDateTime.now());
        cleanMakeMapper.updateById(cleanMake);

        // 记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(bill.getCusId());
        log.setOperateName(bill.getCreateBy());
        log.setSourceId(bill.getCleanMakeId());
        log.setType("make_clean");
        log.setRefuse("预约保洁支付成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        FlatRoom room = roomMapper.selectById(cleanMake.getRoomId());
        if(room != null && room.getDelStatus() == 0) {
            MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
            if (maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
                weChatPublicMessageService.sendMessage(
                        maiUser,
                        "J7YI_Nm72sBdHdtRoxdNAKbZ0r-tjn7A9E49vt9rsVg",
                        "/pages/butler/home/<USER>/info/index?id=" + cleanMake.getId() + "&type=1",
                        Map.of(
                                "thing2", Map.of("value", room.getName()),
                                "thing3", Map.of("value", cusUser.getRealName()),
                                "phone_number4", Map.of("value", cusUser.getPhoneNumber())
                        )
                );

                weChatPublicMessageService.sendMessage(
                        maiUser,
                        "cAq85XSX6f_rdjvcoC1sZVB8ls7isKDkBRECICtxGN0",
                        null,
                        Map.of(
                                "thing10", Map.of("value", room.getName()),
                                "thing11", Map.of("value", cusUser.getRealName()),
                                "amount6", Map.of("value", bill.getMoney().toString()),
                                "thing14", Map.of("value", "保洁预约支付"),
                                "time5", Map.of("value", bill.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        )
                );
            }
        }
    }

    public void remove(Long id, SysUser sysUser) {
        cleanMakeMapper.update(q -> q
                .set(FlatCleanMake::getDelStatus, 1)
                .set(FlatCleanMake::getUpdateTime, LocalDateTime.now())
                .set(FlatCleanMake::getUpdateBy, sysUser.getUsername())
                .eq(FlatCleanMake::getId, id)
                .eq(FlatCleanMake::getDelStatus, 0)
        );
    }
}
