package com.flat.logic.service.finance;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.finance.InvoiceApplyReq;
import com.flat.logic.dto.req.finance.InvoiceQueryReq;
import com.flat.logic.dto.resp.finance.InvoiceDetailResp;
import com.flat.logic.dto.resp.finance.InvoiceListResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatInvoice;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatInvoiceMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 公寓发票Service业务层处理
 */
@Service
public class InvoiceService {

    @Resource
    private FlatInvoiceMapper invoiceMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    public TablePage<InvoiceListResp> queryPage(InvoiceQueryReq req) {
        return PageUtils.paginate(() -> invoiceMapper.selectRespList(req));
    }

    public InvoiceDetailResp queryDetail(Long id) {
        InvoiceListResp listResp = invoiceMapper.selectRespById(id);
        if(listResp == null) {
            throw new ServiceException("发票信息不存在");
        }

        InvoiceDetailResp detailResp = new InvoiceDetailResp();
        BeanUtils.copyProperties(listResp, detailResp);

        List<FlatBill> bills = billMapper.selectList(q -> q
            .eq(FlatBill::getInvoiceStatus, 1)
            .eq(FlatBill::getInvoiceId, id)
        );

        detailResp.setBills(bills);
        return detailResp;
    }

    public void apply(InvoiceApplyReq req, CusUser cusUser) {
        if(req.getRiseType() == 0) {
            if(StringUtils.isBlank(req.getDutyNo())) {
                throw new ServiceException("纳税人识别号不能为空");
            }

            if(StringUtils.isBlank(req.getRegisterAddress())) {
                throw new ServiceException("注册地址不能为空");
            }

            if(StringUtils.isBlank(req.getRegisterPhoneNumber())) {
                throw new ServiceException("注册电话不能为空");
            }

            if(StringUtils.isBlank(req.getOpenBank())) {
                throw new ServiceException("开户行不能为空");
            }

            if(StringUtils.isBlank(req.getBankAccount())) {
                throw new ServiceException("银行账号不能为空");
            }
        }

        List<FlatBill> bills = billMapper.selectList(q -> q
                .eq(FlatBill::getCusId, cusUser.getUserId())
                .eq(FlatBill::getInvoiceStatus, 0)
                .in(FlatBill::getId, req.getBillIds())
                .eq(FlatBill::getDelStatus, 0)
                .eq(FlatBill::getPayStatus, 1)
        );
        if (bills.size() != req.getBillIds().size()) {
            throw new ServiceException("选择的账单中包含未支付的账单或已开发票的账单");
        }

        // 检查账单是否是水费、电费、租金和能耗费
        // 检查账单支付时间是否是上个月
        for (FlatBill bill : bills) {
            if(!bill.getPayTarget().equals("water") && !bill.getPayTarget().equals("electric") && !bill.getPayTarget().equals("plan")) {
                throw new ServiceException("账单不属于水费、电费、租金和能耗费");
            }

            if(bill.getPayTime().isBefore(LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).minusMonths(1))) {
                throw new ServiceException("账单支付时间不是上个月及以前");
            }
        }

        // 判断所有账单是不是属于同一个租赁记录
        Long leaseId = bills.get(0).getLeaseId();
        for (FlatBill bill : bills) {
            if (!bill.getLeaseId().equals(leaseId)) {
                throw new ServiceException("选择的账单不属于同一个租赁记录");
            }
        }

        // 判断租赁记录ID是否有效
        FlatLease lease = leaseMapper.selectById(leaseId);
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        FlatInvoice invoice = new FlatInvoice();
        BeanUtils.copyProperties(req, invoice);
        invoice.setFlatId(lease.getFlatId());
        invoice.setRoomId(lease.getRoomId());
        invoice.setCusId(cusUser.getUserId());
        invoice.setLeaseId(leaseId);
        invoice.setCompanyId(lease.getCompanyId());
        invoice.setStatus(0);
        invoice.setCreateBy(cusUser.getNickName());
        invoice.setCreateTime(LocalDateTime.now());
        invoice.setDelStatus(0);
        invoice.setAmount(bills.stream().map(FlatBill::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        invoiceMapper.insert(invoice);

        for (FlatBill bill : bills) {
            bill.setInvoiceStatus(1);
            bill.setInvoiceId(invoice.getId());
            bill.setUpdateBy(cusUser.getNickName());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);
        }
    }

    // 标记开票
    public void confirm(Long id, SysUser sysUser) {
        FlatInvoice invoice = invoiceMapper.selectById(id);
        if(invoice == null || invoice.getDelStatus() == 1) {
            throw new ServiceException("发票信息不存在");
        }

        if(invoice.getStatus() != 0) {
            throw new ServiceException("发票已开票");
        }

        invoice.setStatus(1);
        invoice.setOpenTime(LocalDateTime.now());
        invoice.setUpdateBy(sysUser.getUsername());
        invoice.setUpdateTime(LocalDateTime.now());
        invoiceMapper.updateById(invoice);
    }

    /**
     * 删除发票申请
     *
     * @param id 发票ID
     * @param sysUser 系统用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id, SysUser sysUser) {
        // 判断发票是否存在
        FlatInvoice invoice = invoiceMapper.selectById(id);
        if (invoice == null || invoice.getDelStatus() == 1) {
            throw new ServiceException("发票信息不存在");
        }

        // 判断发票状态，已开票的发票不能删除
        if (invoice.getStatus() == 1) {
            throw new ServiceException("已开票的发票不能删除");
        }

        // 查找关联的账单
        List<FlatBill> bills = billMapper.selectList(q -> q
                .eq(FlatBill::getInvoiceId, id)
                .eq(FlatBill::getInvoiceStatus, 1)
                .eq(FlatBill::getDelStatus, 0)
        );

        // 更新账单的发票状态
        for (FlatBill bill : bills) {
            bill.setInvoiceStatus(0);
            bill.setInvoiceId(null);
            bill.setUpdateBy(sysUser.getNickName());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);
        }

        // 执行发票的逻辑删除
        invoice.setDelStatus(1);
        invoice.setUpdateBy(sysUser.getNickName());
        invoice.setUpdateTime(LocalDateTime.now());
        invoiceMapper.updateById(invoice);
    }
}
