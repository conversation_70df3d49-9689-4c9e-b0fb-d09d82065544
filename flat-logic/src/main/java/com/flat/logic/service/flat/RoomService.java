package com.flat.logic.service.flat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.flat.BlockFloorQueryReq;
import com.flat.logic.dto.req.flat.RoomAccountNoEditReq;
import com.flat.logic.dto.req.flat.RoomAddReq;
import com.flat.logic.dto.req.flat.RoomEditReq;
import com.flat.logic.dto.req.flat.RoomQueryReq;
import com.flat.logic.dto.req.flat.RoomStateQueryReq;
import com.flat.logic.dto.req.flat.RoomStatusChangeReq;
import com.flat.logic.dto.req.flat.RoomTreeSelectReq;
import com.flat.logic.dto.resp.NormalTreeResp;
import com.flat.logic.dto.resp.flat.RoomResp;
import com.flat.logic.dto.resp.flat.RoomStateFloorResp;
import com.flat.logic.dto.resp.flat.RoomStateItemResp;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.flat.FlatRoomLabel;
import com.flat.logic.mapper.flat.FlatRoomLabelMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.model.RoomStateModel;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 公寓房间Service业务层处理
 */
@Service
public class RoomService {

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatRoomLabelMapper roomLabelMapper;

    private void putLabel(RoomResp resp, List<FlatRoomLabel> labels) {
        if(StringUtils.isBlank(resp.getLabel())) {
            return;
        }

        String[] labelIds = resp.getLabel().split(",");
        resp.setLabelIds(Stream.of(labelIds).map(Long::parseLong).collect(Collectors.toList()));
        resp.setLabelNames(
                labels.stream()
                        .filter(label -> resp.getLabelIds().contains(label.getId()))
                        .map(FlatRoomLabel::getName)
                        .collect(Collectors.toList())
        );
    }

    private void putLabel(RoomResp resp) {
        List<FlatRoomLabel> labels = roomLabelMapper.selectList(q -> q
                .eq(FlatRoomLabel::getDelStatus, 0)
                .eq(FlatRoomLabel::getType, 1)
        );
        putLabel(resp, labels);
    }

    private void putLabels(List<RoomResp> respList) {
        List<FlatRoomLabel> labels = roomLabelMapper.selectList(q -> q
                .eq(FlatRoomLabel::getDelStatus, 0)
                .eq(FlatRoomLabel::getType, 1)
        );
        for (RoomResp resp : respList) {
            putLabel(resp, labels);
        }
    }

    /**
     * 获取所有楼栋号
     */
    public List<String> getAllBlockNos() {
        return roomMapper.selectDistinctBlockNos();
    }

    /**
     * 根据楼栋号获取所有楼层号
     */
    public List<Integer> getFloorsByBlock(BlockFloorQueryReq req) {
        if (StringUtils.isBlank(req.getBlockNo())) {
            throw new ServiceException("楼栋号不能为空");
        }
        return roomMapper.selectDistinctFloorsByBlock(req.getBlockNo());
    }

    public List<RoomResp> queryRespList(RoomQueryReq req) {
        List<RoomResp> respList = roomMapper.selectRespList(req);
        putLabels(respList);
        return respList;
    }

    public TablePage<RoomResp> queryRespPage(RoomQueryReq req) {
        TablePage<RoomResp> result = PageUtils.paginate(() -> roomMapper.selectRespList(req));
        putLabels(result.getRows());
        return result;
    }

    public RoomResp queryRespDetail(Long id) {
        RoomResp roomResp = roomMapper.selectRespById(id);
        if(roomResp == null || roomResp.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }
        putLabel(roomResp);
        return roomResp;
    }


    public TablePage<RoomResp> queryNotFreePage(String content) {
        TablePage<RoomResp> result = PageUtils.paginate(() -> roomMapper.selectNotFreeList(content));
        putLabels(result.getRows());
        return result;
    }

    public List<NormalTreeResp> selectTree(RoomTreeSelectReq req) {
        List<FlatRoom> allRooms = roomMapper.selectList((query) -> query
                .eq(FlatRoom::getDelStatus, 0)
                .eq(req.getFlatId() != null, FlatRoom::getFlatId, req.getFlatId())
                .eq(req.getKind() != null, FlatRoom::getKind, req.getKind())
        );
        if(allRooms.isEmpty()) {
            return List.of();
        }

        //先找到所有栋/幢/楼号
        return allRooms.stream().map(FlatRoom::getBlockNo).distinct().sorted().map(block -> {
            NormalTreeResp blockNode = new NormalTreeResp();
            blockNode.setText(block);
            blockNode.setValue(block);
            blockNode.setChildren(
                    allRooms.stream()
                            .filter(room -> room.getBlockNo().equals(block))
                            .map(FlatRoom::getFloorNo)
                            .distinct()
                            .sorted()
                            .map(floor -> {
                                NormalTreeResp floorNode = new NormalTreeResp();
                                floorNode.setText(floor.toString());
                                floorNode.setValue(floor.toString());
                                floorNode.setChildren(
                                        allRooms.stream()
                                                .filter(room -> room.getBlockNo().equals(block) && room.getFloorNo().equals(floor))
                                                .map(room -> {
                                                    NormalTreeResp roomNode = new NormalTreeResp();
                                                    roomNode.setText(room.getRoomNo());
                                                    roomNode.setValue(room.getId().toString());
                                                    return roomNode;
                                                }).collect(Collectors.toList())
                                );
                                return floorNode;
                            }).collect(Collectors.toList())
            );
            return blockNode;
        }).collect(Collectors.toList());
    }

    /**
     * 添加房源
     */
    public void add(RoomAddReq req, SysUser user) {
        FlatRoom room = new FlatRoom();
        // 复制属性
        BeanUtils.copyProperties(req, room);
        // 设置初始状态为空闲
        room.setStatus(0);
        // 设置删除状态为未删除
        room.setDelStatus(0);
        // 设置创建人
        room.setCreateBy(user.getUsername());
        room.setCreateTime(LocalDateTime.now());
        roomMapper.insert(room);
    }

    /**
     * 编辑房源
     */
    public void edit(RoomEditReq req, SysUser user) {
        FlatRoom room = roomMapper.selectById(req.getId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 不允许修改房屋种类
        if (!room.getKind().equals(req.getKind())) {
            throw new ServiceException("房屋种类不允许修改");
        }

        BeanUtils.copyProperties(req, room);
        // 设置更新人
        room.setUpdateBy(user.getUsername());
        room.setUpdateTime(LocalDateTime.now());
        roomMapper.updateById(room);
    }

    /**
     * 删除房源
     */
    public void remove(Long id, SysUser user) {
        FlatRoom room = roomMapper.selectById(id);
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }
        // 逻辑删除
        room.setDelStatus(1);
        // 设置更新人
        room.setUpdateBy(user.getUsername());
        room.setUpdateTime(LocalDateTime.now());
        roomMapper.updateById(room);
    }

    /**
     * 清理脏房（将脏房状态改为空闲）
     */
    public void finishClean(Long id, SysUser user) {
        FlatRoom room = roomMapper.selectById(id);
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }
        if (room.getStatus() != 3) {
            throw new ServiceException("只能清理脏房状态的房源");
        }
        room.setStatus(0);
        // 设置更新人
        room.setUpdateBy(user.getUsername());
        room.setUpdateTime(LocalDateTime.now());
        roomMapper.updateById(room);
    }

    /**
     * 修改房源状态
     *
     * @param req 请求参数
     * @param user 操作用户
     */
    public void changeStatus(RoomStatusChangeReq req, SysUser user) {
        // 修改的状态只能是0,2,3,4
        if (!req.getStatus().equals(0) && !req.getStatus().equals(2) && !req.getStatus().equals(3) && !req.getStatus().equals(4)) {
            throw new ServiceException("不支持的状态");
        }

        FlatRoom room = roomMapper.selectById(req.getId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 如果是已租赁状态，则不允许修改
        if (room.getStatus() == 1) {
            throw new ServiceException("房源已被租赁");
        }

        // 如果当前状态已经是目标状态，则不需要修改
        if (room.getStatus().equals(req.getStatus())) {
            return;
        }

        // 更新状态
        room.setId(req.getId());
        room.setStatus(req.getStatus());
        room.setRemark(req.getRemark());
        room.setUpdateBy(user.getUsername());
        room.setUpdateTime(LocalDateTime.now());

        roomMapper.updateById(room);
    }

    public void editAccountNo(RoomAccountNoEditReq req, SysUser user) {
        FlatRoom room = roomMapper.selectById(req.getId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        // 检查水表户号是否已经存在
        FlatRoom existWater = roomMapper.selectOne(q -> q
                .eq(FlatRoom::getWaterAccountNo, req.getWaterAccountNo())
                .eq(FlatRoom::getDelStatus, 0)
                .ne(FlatRoom::getId, req.getId())
        );
        if (existWater != null) {
            throw new ServiceException("水表户号已被使用");
        }

        // 检查电表户号是否已经存在
        FlatRoom existElectric = roomMapper.selectOne(q -> q
                .eq(FlatRoom::getElectricAccountNo, req.getElectricAccountNo())
                .eq(FlatRoom::getDelStatus, 0)
                .ne(FlatRoom::getId, req.getId())
        );
        if (existElectric != null) {
            throw new ServiceException("电表户号已被使用");
        }

        // 检查通过后修改
        room.setId(req.getId());
        room.setWaterAccountNo(req.getWaterAccountNo());
        room.setElectricAccountNo(req.getElectricAccountNo());
        room.setUpdateBy(user.getUsername());
        room.setUpdateTime(LocalDateTime.now());
        roomMapper.updateById(room);
    }

    public List<RoomStateFloorResp> queryStates(RoomStateQueryReq req) {
        List<RoomStateModel> stateModels = roomMapper.selectStates(req);

        List<RoomStateFloorResp> floorResps = new ArrayList<>();
        for (RoomStateModel stateModel : stateModels) {
            Optional<RoomStateFloorResp> first = floorResps.stream().filter(resp ->
                    resp.getBlock().equals(stateModel.getBlockNo())
                            && Objects.equals(resp.getFloor(), stateModel.getFloorNo())
            ).findFirst();
            RoomStateFloorResp floorResp;
            if (first.isPresent()) {
                floorResp = first.get();
            } else {
                floorResp = new RoomStateFloorResp();
                floorResp.setBlock(stateModel.getBlockNo());
                floorResp.setFloor(stateModel.getFloorNo());
                floorResp.setSouth(new ArrayList<>());
                floorResp.setNorth(new ArrayList<>());
                floorResps.add(floorResp);
            }

            RoomStateItemResp itemResp = new RoomStateItemResp();
            itemResp.setKind(stateModel.getRoomKind());
            itemResp.setNumber(stateModel.getRoomNo());
            itemResp.setStatus(stateModel.getRoomStatus());
            itemResp.setTenant(stateModel.getCusRealName());
            itemResp.setCompanyName(stateModel.getCompanyName());

            switch (stateModel.getOrientation()) {
                case 0:
                    floorResp.getSouth().add(itemResp);
                    break;
                case 1:
                    floorResp.getNorth().add(itemResp);
                    break;
            }
        }

        return floorResps;
    }
}
