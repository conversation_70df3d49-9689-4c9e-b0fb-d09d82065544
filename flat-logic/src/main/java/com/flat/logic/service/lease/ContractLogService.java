package com.flat.logic.service.lease;

import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.lease.ContractLogQueryReq;
import com.flat.logic.entity.lease.FlatContractLog;
import com.flat.logic.mapper.lease.FlatContractLogMapper;

import jakarta.annotation.Resource;

/**
 * 公寓合同流程记录Service业务层处理
 */
@Service
public class ContractLogService {

    @Resource
    private FlatContractLogMapper flatContractLogMapper;

//    /**
//     * 查询公寓合同流程记录
//     *
//     * @param id 公寓合同流程记录主键
//     * @return 公寓合同流程记录
//     */
//    public FlatContractLog selectFlatContractLogById(Long id)
//    {
//        return flatContractLogMapper.selectFlatContractLogById(id);
//    }

    /**
     * 查询公寓合同流程记录列表
     */
    public TablePage<FlatContractLog> selectPage(ContractLogQueryReq req) {
        return PageUtils.paginate(() -> flatContractLogMapper.selectList(req));
    }

//    /**
//     * 新增公寓合同流程记录
//     *
//     * @param flatContractLog 公寓合同流程记录
//     * @return 结果
//     */
//    public int insertFlatContractLog(FlatContractLog flatContractLog)
//    {
//        flatContractLog.setCreateTime(LocalDateTime.now());
//        return flatContractLogMapper.insertFlatContractLog(flatContractLog);
//    }
//
//    /**
//     * 修改公寓合同流程记录
//     *
//     * @param flatContractLog 公寓合同流程记录
//     * @return 结果
//     */
//    public int updateFlatContractLog(FlatContractLog flatContractLog)
//    {
//        flatContractLog.setUpdateTime(LocalDateTime.now());
//        return flatContractLogMapper.updateFlatContractLog(flatContractLog);
//    }
//
//    /**
//     * 批量删除公寓合同流程记录
//     *
//     * @param ids 需要删除的公寓合同流程记录主键
//     * @return 结果
//     */
//    public int deleteFlatContractLogByIds(Long[] ids)
//    {
//        return flatContractLogMapper.deleteFlatContractLogByIds(ids);
//    }
//
//    /**
//     * 删除公寓合同流程记录信息
//     *
//     * @param id 公寓合同流程记录主键
//     * @return 结果
//     */
//    public int deleteFlatContractLogById(Long id)
//    {
//        return flatContractLogMapper.deleteFlatContractLogById(id);
//    }
//
//
//    public int delStatus(Long[] ids) {
//        return flatContractLogMapper.delStatus(ids);
//    }
}
