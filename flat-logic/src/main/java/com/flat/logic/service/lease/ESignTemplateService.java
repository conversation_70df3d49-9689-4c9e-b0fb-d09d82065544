package com.flat.logic.service.lease;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.flat.logic.esign.comm.ESignHttpHelper;
import com.flat.logic.esign.enums.ESignRequestType;

@Service
public class ESignTemplateService {

    @Value("${eSign.appId}")
    private String eSignAppId;

    @Value("${eSign.appSecret}")
    private String eSignAppSecret;

    @Value("${eSign.apiUrl}")
    private String eSignApiUrl;

    @Value("${eSign.orgId}")
    private String eSignOrgId;

    private JSONObject request(String apiPath, ESignRequestType requestType, String jsonParam) {
        //生成签名鉴权方式的的header
        Map<String, String> header = ESignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId, eSignAppSecret, ESignRequestType.GET, apiPath, jsonParam);
        //发起接口请求
        return ESignHttpHelper.doCommHttp(eSignApiUrl, apiPath, requestType, jsonParam, header);
    }

    public JSONObject queryTemplateList() {
        String apiPath = "/v3/sign-templates?orgId=" + eSignOrgId + "&pageNum=1&pageSize=10";

        //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
        return request(apiPath, ESignRequestType.GET, "{}");
    }

    public JSONObject queryTemplateDetail(String signTemplateId) {
        String apiPath = "/v3/sign-templates/detail?orgId=" + eSignOrgId + "&signTemplateId=2d617931324f49ceb4da7cd55a769b0c";
        return request(apiPath, ESignRequestType.GET, "{}");
    }

}
