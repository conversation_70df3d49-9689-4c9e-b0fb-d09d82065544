package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.make.PublicPeriodAddReq;
import com.flat.logic.dto.req.make.PublicPeriodEditReq;
import com.flat.logic.dto.req.make.PublicPeriodQueryReq;
import com.flat.logic.dto.resp.make.PublicPeriodResp;
import com.flat.logic.entity.make.FlatPublic;
import com.flat.logic.entity.make.FlatPublicPeriod;
import com.flat.logic.mapper.make.FlatPublicMapper;
import com.flat.logic.mapper.make.FlatPublicPeriodMapper;
import com.flat.system.entity.SysUser;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class PublicPeriodService {

    @Resource
    private FlatPublicPeriodMapper publicPeriodMapper;

    @Resource
    private FlatPublicMapper publicMapper;

    /**
     * 添加时间段
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(PublicPeriodAddReq req, SysUser user) {
        FlatPublic pub = publicMapper.selectById(req.getPublicId());
        if (pub == null || pub.getDelStatus() == 1) {
            throw new ServiceException("公共设施不存在");
        }

        // 检查时间是否合法
        if (req.getBeginTime().isAfter(req.getEndTime())) {
            throw new ServiceException("开始时间不能晚于结束时间");
        }

        // 检查是否与现有时间段重叠
        if (publicPeriodMapper.selectOverlapCount(req.getPublicId(), req.getBeginTime(), req.getEndTime(), null) > 0) {
            throw new ServiceException("时间段重叠");
        }

        FlatPublicPeriod period = new FlatPublicPeriod();
        period.setFlatId(pub.getFlatId());
        period.setPublicId(req.getPublicId());
        period.setBeginTime(req.getBeginTime());
        period.setEndTime(req.getEndTime());
        period.setPeopleCount(req.getPeopleCount());
        period.setDescription(req.getDescription());
        period.setCreateBy(user.getUsername());
        period.setCreateTime(LocalDateTime.now());
        period.setDelStatus(0);

        publicPeriodMapper.insert(period);
    }

    /**
     * 修改时间段
     */
    @Transactional(rollbackFor = Exception.class)
    public void edit(PublicPeriodEditReq req, SysUser user) {
        FlatPublicPeriod period = publicPeriodMapper.selectById(req.getId());
        if (period == null || period.getDelStatus() == 1) {
            throw new ServiceException("时间段不存在");
        }

        // 检查时间是否合法
        if (req.getBeginTime().isAfter(req.getEndTime())) {
            throw new ServiceException("开始时间不能晚于结束时间");
        }

        // 检查是否与现有时间段重叠
        if (publicPeriodMapper.selectOverlapCount(req.getPublicId(), req.getBeginTime(), req.getEndTime(), req.getId()) > 0) {
            throw new ServiceException("时间段重叠");
        }

        period.setPublicId(req.getPublicId());
        period.setBeginTime(req.getBeginTime());
        period.setEndTime(req.getEndTime());
        period.setPeopleCount(req.getPeopleCount());
        period.setDescription(req.getDescription());
        period.setUpdateBy(user.getUsername());
        period.setUpdateTime(LocalDateTime.now());

        publicPeriodMapper.updateById(period);
    }

    /**
     * 分页查询时间段
     */
    public TablePage<PublicPeriodResp> queryPage(PublicPeriodQueryReq req) {
        return PageUtils.paginate(() -> publicPeriodMapper.selectRespList(req));
    }

    /**
     * 查询时间段列表
     */
    public List<PublicPeriodResp> queryList(PublicPeriodQueryReq req) {
        if(req.getMakeDate() == null) {
            req.setMakeDate(LocalDate.now());
        }
        return publicPeriodMapper.selectRespList(req);
    }

    /**
     * 查询时间段详情
     */
    public PublicPeriodResp queryDetail(Long id) {
        return publicPeriodMapper.selectRespById(id);
    }

    /**
     * 删除时间段
     */
    public void remove(Long id, SysUser user) {
        FlatPublicPeriod period = publicPeriodMapper.selectById(id);
        if (period == null || period.getDelStatus() == 1) {
            throw new ServiceException("时间段不存在");
        }

        period.setDelStatus(1);
        period.setUpdateBy(user.getUsername());
        period.setUpdateTime(LocalDateTime.now());

        publicPeriodMapper.updateById(period);
    }
}
