package com.flat.logic.service.flat;

import com.flat.common.exception.ServiceException;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 企业服务类
 */
@Service
public class CompanyService {

    @Resource
    private FlatCompanyMapper flatCompanyMapper;

    /**
     * 查询所有企业记录
     *
     * @return 企业列表
     */
    public List<FlatCompany> queryList() {
        return flatCompanyMapper.selectList(q -> q
                .eq(FlatCompany::getDelStatus, 0)
        );
    }

    /**
     * 根据ID查询企业详情
     *
     * @param id 企业ID
     * @return 企业信息
     */
    public FlatCompany queryDetail(Long id) {
        FlatCompany company = flatCompanyMapper.selectById(id);
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }
        return company;
    }
}
