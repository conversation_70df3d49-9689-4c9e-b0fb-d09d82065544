package com.flat.logic.service.make;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.make.PublicAddReq;
import com.flat.logic.dto.req.make.PublicEditReq;
import com.flat.logic.dto.req.make.PublicQueryReq;
import com.flat.logic.dto.resp.make.PublicResp;
import com.flat.logic.entity.make.FlatPublic;
import com.flat.logic.mapper.make.FlatPublicMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

@Service
public class PublicService {

    @Resource
    private FlatPublicMapper publicMapper;

    @Transactional(rollbackFor = Exception.class)
    public void add(PublicAddReq req, SysUser user) {
        if(req.getPrice() == null) {
            req.setPrice(BigDecimal.ZERO);
        }

        if(req.getNeedPayFlag() == 1 && BigDecimal.ZERO.compareTo(req.getPrice()) <= 0) {
            throw new ServiceException("费用必须大于0");
        }

        // 判断名称是否重复
        if (publicMapper.selectCount(q -> q
                .eq(FlatPublic::getName, req.getName())
                .eq(FlatPublic::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("公用设施名称已存在");
        }

        FlatPublic entity = new FlatPublic();
        BeanUtils.copyProperties(req, entity);
        entity.setCreateBy(user.getUsername());
        entity.setCreateTime(LocalDateTime.now());
        entity.setDelStatus(0);
        publicMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void edit(PublicEditReq req, SysUser user) {
        FlatPublic entity = publicMapper.selectById(req.getId());
        if (entity == null) {
            throw new ServiceException("公用设施不存在");
        }

        // 判断名称是否重复
        if (publicMapper.selectCount(q -> q
                .eq(FlatPublic::getName, req.getName())
                .ne(FlatPublic::getId, req.getId())
                .eq(FlatPublic::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("公用设施名称已存在");
        }

        BeanUtils.copyProperties(req, entity);
        entity.setUpdateBy(user.getUsername());
        entity.setUpdateTime(LocalDateTime.now());
        publicMapper.updateById(entity);
    }

    public List<PublicResp> queryList(PublicQueryReq req) {
        return publicMapper.selectRespList(req);
    }

    /**
     * 分页查询公共区域列表
     */
    public TablePage<PublicResp> queryPage(PublicQueryReq req) {
        return PageUtils.paginate(() -> publicMapper.selectRespList(req));
    }

    // 根据ID查询详情
    public PublicResp queryDetail(Long id) {
        return publicMapper.selectRespById(id);
    }

    // 删除
    public void remove(Long id, SysUser user) {
        publicMapper.update(q -> q
                .set(FlatPublic::getDelStatus, 1)
                .set(FlatPublic::getUpdateTime, LocalDateTime.now())
                .set(FlatPublic::getUpdateBy, user.getUsername())
                .eq(FlatPublic::getId, id)
                .eq(FlatPublic::getDelStatus, 0)
        );
    }
}
