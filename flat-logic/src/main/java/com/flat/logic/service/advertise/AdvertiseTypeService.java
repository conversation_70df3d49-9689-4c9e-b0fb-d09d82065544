package com.flat.logic.service.advertise;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.advertise.AdvertiseTypeAddReq;
import com.flat.logic.dto.req.advertise.AdvertiseTypeEditReq;
import com.flat.logic.dto.req.advertise.AdvertiseTypeQueryReq;
import com.flat.logic.entity.advertise.FlatAdvertise;
import com.flat.logic.entity.advertise.FlatAdvertiseType;
import com.flat.logic.mapper.advertise.FlatAdvertiseMapper;
import com.flat.logic.mapper.advertise.FlatAdvertiseTypeMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 公寓广告分类
 */
@Service
public class AdvertiseTypeService {

    @Resource
    private FlatAdvertiseTypeMapper advertiseTypeMapper;

    @Resource
    private FlatAdvertiseMapper advertiseMapper;

    public List<FlatAdvertiseType> queryList(AdvertiseTypeQueryReq req) {
        return advertiseTypeMapper.selectList(q -> q
                .eq(FlatAdvertiseType::getDelStatus, 0)
                .like(StringUtils.isNotBlank(req.getName()), FlatAdvertiseType::getName, req.getName())
                .eq(StringUtils.isNotBlank(req.getPosition()), FlatAdvertiseType::getPosition, req.getPosition())
                .orderByDesc(FlatAdvertiseType::getId)
        );
    }

    public TablePage<FlatAdvertiseType> queryPage(AdvertiseTypeQueryReq req) {
        return PageUtils.paginate(() -> advertiseTypeMapper.selectList(q -> q
                .eq(FlatAdvertiseType::getDelStatus, 0)
                .like(StringUtils.isNotBlank(req.getName()), FlatAdvertiseType::getName, req.getName())
                .eq(StringUtils.isNotBlank(req.getPosition()), FlatAdvertiseType::getPosition, req.getPosition())
                .orderByDesc(FlatAdvertiseType::getId)
        ));
    }

    /**
     * 查询公寓广告分类
     *
     * @param id 公寓广告分类主键
     * @return 公寓广告分类
     */
    public FlatAdvertiseType queryDetail(Long id) {
        FlatAdvertiseType advertiseType = advertiseTypeMapper.selectById(id);
        if (advertiseType == null || advertiseType.getDelStatus() == 1) {
            throw new ServiceException("广告分类不存在");
        }

        return advertiseType;
    }

    /**
     * 添加广告分类
     */
    public void add(AdvertiseTypeAddReq req, SysUser sysUser) {
        // 检查相同位置是否已存在相同名称的分类
        long count = advertiseTypeMapper.selectCount(q -> q
                .eq(FlatAdvertiseType::getDelStatus, 0)
                .eq(FlatAdvertiseType::getName, req.getName())
                .eq(FlatAdvertiseType::getPosition, req.getPosition()));
        if (count > 0) {
            throw new ServiceException("该位置已存在相同名称的广告分类");
        }

        FlatAdvertiseType advertiseType = new FlatAdvertiseType();
        BeanUtils.copyProperties(req, advertiseType);
        advertiseType.setCreateBy(sysUser.getUsername());
        advertiseType.setCreateTime(LocalDateTime.now());
        advertiseType.setDelStatus(0);
        advertiseTypeMapper.insert(advertiseType);
    }

    /**
     * 修改广告分类
     */
    public void edit(AdvertiseTypeEditReq req, SysUser sysUser) {
        // 验证广告分类是否存在
        FlatAdvertiseType type = advertiseTypeMapper.selectById(req.getId());
        if (type == null || type.getDelStatus() == 1) {
            throw new ServiceException("广告分类不存在");
        }

        // 检查相同位置是否已存在相同名称的其他分类
        long count = advertiseTypeMapper.selectCount(q -> q
                .eq(FlatAdvertiseType::getDelStatus, 0)
                .eq(FlatAdvertiseType::getName, req.getName())
                .eq(FlatAdvertiseType::getPosition, req.getPosition())
                .ne(FlatAdvertiseType::getId, req.getId()));
        if (count > 0) {
            throw new ServiceException("该位置已存在相同名称的广告分类");
        }

        BeanUtils.copyProperties(req, type);
        type.setUpdateBy(sysUser.getUsername());
        type.setUpdateTime(LocalDateTime.now());
        advertiseTypeMapper.updateById(type);
    }

    /**
     * 删除广告分类
     */
    public void remove(Long id, SysUser sysUser) {
        // 验证广告分类是否存在
        FlatAdvertiseType advertiseType = advertiseTypeMapper.selectById(id);
        if (advertiseType == null || advertiseType.getDelStatus() == 1) {
            throw new ServiceException("广告分类不存在");
        }

        // 检查是否有广告使用该分类
        long count = advertiseMapper.selectCount(q -> q
                .eq(FlatAdvertise::getDelStatus, 0)
                .eq(FlatAdvertise::getAdvertiseTypeId, id));
        if (count > 0) {
            throw new ServiceException("该分类下存在广告，无法删除");
        }

        // 逻辑删除
        FlatAdvertiseType updateType = new FlatAdvertiseType();
        updateType.setId(id);
        updateType.setDelStatus(1);
        advertiseTypeMapper.updateById(updateType);
    }
}
