package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.make.RepairEvaluateAddReq;
import com.flat.logic.dto.req.make.RepairEvaluateQueryReq;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.make.FlatRepairEvaluate;
import com.flat.logic.entity.make.FlatRepairMake;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.make.FlatRepairEvaluateMapper;
import com.flat.logic.mapper.make.FlatRepairMakeMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 公寓报修评价Service业务层处理
 */
@Service
public class RepairEvaluateService {

    @Resource
    private FlatRepairEvaluateMapper repairEvaluateMapper;

    @Resource
    private FlatRepairMakeMapper repairMakeMapper;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private MessageService messageService;

    public FlatRepairEvaluate queryDetail(Long id) {
        FlatRepairEvaluate evaluate = repairEvaluateMapper.selectById(id);
        if(evaluate == null || evaluate.getDelStatus() == 1) {
            throw new ServiceException("保修评价记录不存在");
        }
        return evaluate;
    }


    public TablePage<FlatRepairEvaluate> queryPage(RepairEvaluateQueryReq req) {
        return PageUtils.paginate(
                () -> repairEvaluateMapper.selectList(q -> q
                        .eq(req.getFlatId() != null, FlatRepairEvaluate::getFlatId, req.getFlatId())
                        .eq(req.getRoomId() != null, FlatRepairEvaluate::getRoomId, req.getRoomId())
                        .eq(req.getCusId() != null, FlatRepairEvaluate::getCusId, req.getCusId())
                        .eq(req.getLeaseId() != null, FlatRepairEvaluate::getLeaseId, req.getLeaseId())
                        .eq(req.getRepairMakeId() != null, FlatRepairEvaluate::getRepairMakeId, req.getRepairMakeId())
                        .eq(req.getButlerId() != null, FlatRepairEvaluate::getButlerId, req.getButlerId())
                        .eq(req.getPropertyId() != null, FlatRepairEvaluate::getPropertyId, req.getPropertyId())
                        .eq(req.getStaffId() != null, FlatRepairEvaluate::getStaffId, req.getStaffId())
                )
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(RepairEvaluateAddReq req, CusUser user) {
        FlatRepairMake repairMake = repairMakeMapper.selectById(req.getRepairMakeId());
        if(repairMake == null || repairMake.getDelStatus() == 1) {
            throw new ServiceException("报修预约记录不存在");
        }

        if(repairMake.getEvaluateFlag() == 1) {
            throw new ServiceException("该报修预约已评价");
        }

        FlatRepairEvaluate evaluate = new FlatRepairEvaluate();
        BeanUtils.copyProperties(req, evaluate);
        evaluate.setFlatId(repairMake.getFlatId());
        evaluate.setRoomId(repairMake.getRoomId());
        evaluate.setCusId(repairMake.getCusId());
        evaluate.setLeaseId(repairMake.getLeaseId());
        evaluate.setButlerId(repairMake.getButlerId());
        evaluate.setPropertyId(repairMake.getPropertyId());
        evaluate.setStaffId(repairMake.getStaffId());
        evaluate.setEvaluateTime(LocalDateTime.now());
        evaluate.setCreateBy(user.getNickName());
        evaluate.setCreateTime(LocalDateTime.now());
        repairEvaluateMapper.insert(evaluate);

        repairMake.setEvaluateFlag(1);
        repairMake.setUpdateBy(user.getNickName());
        repairMake.setUpdateTime(LocalDateTime.now());
        repairMakeMapper.updateById(repairMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(user.getUserId());
        log.setOperateName(user.getNickName());
        log.setSourceId(repairMake.getId());
        log.setType("make_repairs");
        log.setRefuse("用户评价成功");
        log.setDelStatus(0);
        log.setCreateBy(user.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        //管家通知
        messageService.cusToButler(
                user.getUserId(), repairMake.getButlerId(),
                user.getNickName() + "评价报修完成",
                "/pages/butler/home/<USER>/info/index?id=" + repairMake.getId() + "&type=2"
        );

        //物业通知
        messageService.cusToProperty(
                user.getUserId(), repairMake.getPropertyId(),
                user.getNickName() + "评价报修完成",
                "/pages/property/info/index?id=" + repairMake.getId() + "&type=2"
        );
    }


    public void remove(Long id, SysUser user) {
        repairEvaluateMapper.update(q -> q
                .set(FlatRepairEvaluate::getDelStatus, 1)
                .set(FlatRepairEvaluate::getUpdateTime, LocalDateTime.now())
                .set(FlatRepairEvaluate::getUpdateBy, user.getUsername())
                .eq(FlatRepairEvaluate::getId, id)
                .eq(FlatRepairEvaluate::getDelStatus, 0)
        );
    }

}
