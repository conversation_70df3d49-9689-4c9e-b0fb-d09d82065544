package com.flat.logic.service.sundry;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.sundry.SundryCategoryAddReq;
import com.flat.logic.dto.req.sundry.SundryCategoryEditReq;
import com.flat.logic.dto.req.sundry.SundryCategoryQueryReq;
import com.flat.logic.entity.sundry.SundryCategory;
import com.flat.logic.mapper.sundry.SundryCategoryMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

@Service
public class SundryCategoryService {

    @Resource
    private SundryCategoryMapper sundryCategoryMapper;

    @Transactional
    public void add(SundryCategoryAddReq req, SysUser sysUser) {
        SundryCategory category = new SundryCategory();
        category.setName(req.getName());
        category.setCreateBy(sysUser.getUsername());
        category.setCreateTime(LocalDateTime.now());
        category.setDelStatus(0);
        sundryCategoryMapper.insert(category);
    }

    @Transactional
    public void edit(SundryCategoryEditReq req, SysUser sysUser) {
        SundryCategory category = sundryCategoryMapper.selectById(req.getId());
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("物品库类别不存在");
        }

        category.setName(req.getName());
        category.setUpdateBy(sysUser.getUsername());
        category.setUpdateTime(LocalDateTime.now());
        sundryCategoryMapper.updateById(category);
    }

    @Transactional
    public void delete(Long id, SysUser sysUser) {
        SundryCategory category = sundryCategoryMapper.selectById(id);
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("物品库类别不存在");
        }

        category.setDelStatus(1);
        category.setUpdateBy(sysUser.getUsername());
        category.setUpdateTime(LocalDateTime.now());
        sundryCategoryMapper.updateById(category);
    }

    public TablePage<SundryCategory> listPage(SundryCategoryQueryReq queryReq) {
        return PageUtils.paginate(() -> list(queryReq));
    }

    public List<SundryCategory> list(SundryCategoryQueryReq queryReq) {
        return sundryCategoryMapper.selectList(q -> q
            .like(StringUtils.isNotBlank(queryReq.getName()), SundryCategory::getName, queryReq.getName())
            .le(queryReq.getBeginCreateTime() != null, SundryCategory::getCreateTime, queryReq.getBeginCreateTime())
            .ge(queryReq.getEndCreateTime() != null, SundryCategory::getCreateTime, queryReq.getEndCreateTime())
            .eq(SundryCategory::getDelStatus, 0)
            .orderByDesc(SundryCategory::getCreateTime)
        );
    }

    // 查询详情
    public SundryCategory detail(Long id) {
        SundryCategory category = sundryCategoryMapper.selectById(id);
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("物品库类别不存在");
        }
        return category;
    }
}
