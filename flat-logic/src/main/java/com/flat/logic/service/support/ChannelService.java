package com.flat.logic.service.support;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.http.HttpUtil;
import com.flat.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
public class ChannelService {

    @Value("${channel.secret}")
    private String channelSecret;

    @Value("${channel.resetCusLoginUrl}")
    private String resetCusLoginUrl;

    public String encrypt(String content) {
        try {
            AES aes = SecureUtil.aes(SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue(), channelSecret.getBytes(StandardCharsets.UTF_8)).getEncoded());
            return aes.encryptBase64(content);
        } catch (Exception e) {
            throw new ServiceException("加密失败", e);
        }
    }

    public String decrypt(String content) {
        try {
            AES aes = SecureUtil.aes(SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue(), channelSecret.getBytes(StandardCharsets.UTF_8)).getEncoded());
            return aes.decryptStr(content);
        } catch (Exception e) {
            throw new ServiceException("解密失败", e);
        }
    }


    public void resetCusLogin(Long cusId) {
        System.out.println(encrypt("" + cusId));
        HttpUtil.get(resetCusLoginUrl + "?code=" + encrypt("" + cusId));
    }
}
