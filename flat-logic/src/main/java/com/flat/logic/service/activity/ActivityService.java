package com.flat.logic.service.activity;

import java.time.LocalDateTime;
import java.util.List;

import com.flat.logic.dto.resp.FileUploadResp;
import com.flat.logic.service.cloud.OSSService;
import com.flat.logic.service.cloud.WeChatMiniProgramService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.activity.ActivityAddReq;
import com.flat.logic.dto.req.activity.ActivityQueryReq;
import com.flat.logic.dto.req.activity.ActivityReviewReq;
import com.flat.logic.dto.req.activity.ActivityUpdateReq;
import com.flat.logic.dto.resp.activity.ActivityResp;
import com.flat.logic.entity.activity.Activity;
import com.flat.logic.mapper.activity.ActivityMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 活动服务
 */
@Service
public class ActivityService {

    @Resource
    private OSSService ossService;

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private WeChatMiniProgramService weChatMiniProgramService;

    /**
     * 新增活动
     *
     * @param req 活动信息
     * @param sysUser 操作人
     * @return 活动ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long add(ActivityAddReq req, SysUser sysUser) {
        // 验证活动时间有效性
        if (!req.getEndTime().isAfter(req.getBeginTime())) {
            throw new ServiceException("活动结束时间必须大于开始时间");
        }
        
        // 验证报名时间有效性
        if (req.getRegisterBeginTime() != null && req.getRegisterEndTime() != null) {
            if (!req.getRegisterEndTime().isAfter(req.getRegisterBeginTime())) {
                throw new ServiceException("报名结束时间必须大于报名开始时间");
            }
        }

        Activity activity = new Activity();
        activity.setName(req.getName());
        activity.setBannerPath(req.getBannerPath());
        activity.setBeginTime(req.getBeginTime());
        activity.setEndTime(req.getEndTime());
        activity.setRegisterBeginTime(req.getRegisterBeginTime());
        activity.setRegisterEndTime(req.getRegisterEndTime());
        activity.setMaxAmount(req.getMaxAmount());
        activity.setDescription(req.getDescription());
        activity.setAddress(req.getAddress());
        activity.setWechatPromotionUrl(req.getWechatPromotionUrl());
        activity.setEntryFeeFlag(req.getEntryFeeFlag());
        activity.setEntryFee(req.getEntryFee());
        activity.setReviewType(req.getReviewType());
        activity.setReviewContent(req.getReviewContent());
        activity.setWechatReviewUrl(req.getWechatReviewUrl());
        activity.setCreateBy(sysUser.getUsername());
        activity.setCreateTime(LocalDateTime.now());
        activity.setDelStatus(0);

        activityMapper.insert(activity);

        FileUploadResp resp = weChatMiniProgramService.queryQrCodeImage("pages/home/<USER>/detail/index?id=" + activity.getId());
        activity.setQrCodePath(resp.getFileName());
        activityMapper.updateById(activity);

        return activity.getId();
    }

    /**
     * 更新活动
     *
     * @param req 活动信息
     * @param sysUser 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(ActivityUpdateReq req, SysUser sysUser) {
        Activity activity = activityMapper.selectById(req.getId());
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        // 验证活动时间有效性
        if (!req.getEndTime().isAfter(req.getBeginTime())) {
            throw new ServiceException("活动结束时间必须大于开始时间");
        }
        
        // 验证报名时间有效性
        if (req.getRegisterBeginTime() != null && req.getRegisterEndTime() != null) {
            if (!req.getRegisterEndTime().isAfter(req.getRegisterBeginTime())) {
                throw new ServiceException("报名结束时间必须大于报名开始时间");
            }
        }

        activity.setName(req.getName());
        activity.setBannerPath(req.getBannerPath());
        activity.setBeginTime(req.getBeginTime());
        activity.setEndTime(req.getEndTime());
        activity.setRegisterBeginTime(req.getRegisterBeginTime());
        activity.setRegisterEndTime(req.getRegisterEndTime());
        activity.setMaxAmount(req.getMaxAmount());
        activity.setDescription(req.getDescription());
        activity.setAddress(req.getAddress());
        activity.setWechatPromotionUrl(req.getWechatPromotionUrl());
        activity.setEntryFeeFlag(req.getEntryFeeFlag());
        activity.setEntryFee(req.getEntryFee());
        activity.setReviewType(req.getReviewType());
        activity.setReviewContent(req.getReviewContent());
        activity.setWechatReviewUrl(req.getWechatReviewUrl());
        activity.setUpdateBy(sysUser.getUsername());
        activity.setUpdateTime(LocalDateTime.now());

        activityMapper.updateById(activity);
    }

    /**
     * 删除活动
     *
     * @param id 活动ID
     * @param sysUser 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id, SysUser sysUser) {
        Activity activity = activityMapper.selectById(id);
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        activity.setDelStatus(1);
        activity.setUpdateBy(sysUser.getUsername());
        activity.setUpdateTime(LocalDateTime.now());

        activityMapper.updateById(activity);
    }

    /**
     * 设置活动回顾
     *
     * @param req 回顾请求
     * @param sysUser 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void review(ActivityReviewReq req, SysUser sysUser) {
        // 查询活动
        Activity activity = activityMapper.selectById(req.getId());
        if (activity == null || activity.getDelStatus() == 1) {
            throw new ServiceException("活动不存在");
        }

        // 验证回顾类型与内容匹配
        if (req.getReviewType() == 1 && (req.getReviewContent() == null || req.getReviewContent().isBlank())) {
            throw new ServiceException("图文回顾内容不能为空");
        }

        if (req.getReviewType() == 2 && (req.getWechatReviewUrl() == null || req.getWechatReviewUrl().isBlank())) {
            throw new ServiceException("公众号回顾链接不能为空");
        }

        // 设置回顾信息
        activity.setReviewType(req.getReviewType());
        activity.setReviewContent(req.getReviewContent());
        activity.setWechatReviewUrl(req.getWechatReviewUrl());
        activity.setUpdateBy(sysUser.getUsername());
        activity.setUpdateTime(LocalDateTime.now());

        // 更新到数据库
        activityMapper.updateById(activity);
    }

    /**
     * 查询活动详情
     *
     * @param id 活动ID
     * @return 活动详情
     */
    public ActivityResp queryDetail(Long id) {
        ActivityResp activity = activityMapper.selectActivityDetail(id);
        if (activity == null) {
            throw new ServiceException("活动不存在");
        }

        activity.setQrCodeUrl(ossService.joinUrl(activity.getQrCodePath()));

        return activity;
    }

    /**
     * 分页查询活动列表
     *
     * @param req 查询条件
     * @return 活动列表
     */
    public TablePage<ActivityResp> queryPage(ActivityQueryReq req) {
        return PageUtils.paginate(
            () -> activityMapper.selectActivityList(req),
            (e) -> {
                e.setQrCodeUrl(ossService.joinUrl(e.getQrCodePath()));
                return e;
            }
        );
    }

    /**
     * 查询活动列表
     *
     * @param req 查询条件
     * @return 活动列表
     */
    public List<ActivityResp> queryList(ActivityQueryReq req) {
        List<ActivityResp> resps = activityMapper.selectActivityList(req);
        for (ActivityResp resp : resps) {
            resp.setQrCodeUrl(ossService.joinUrl(resp.getQrCodePath()));
        }
        return resps;
    }
}