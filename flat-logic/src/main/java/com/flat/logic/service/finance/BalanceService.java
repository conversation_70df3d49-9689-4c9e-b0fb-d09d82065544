package com.flat.logic.service.finance;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;

import com.flat.common.core.dto.IdReq;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.commons.BillConstants;
import com.flat.logic.dto.req.finance.BalanceChangeReq;
import com.flat.logic.dto.req.finance.BalanceDetailReq;
import com.flat.logic.dto.req.finance.BalanceLogQueryReq;
import com.flat.logic.dto.req.finance.BalanceQueryReq;
import com.flat.logic.dto.req.finance.BalanceRechargeReq;
import com.flat.logic.dto.resp.finance.BalanceDetailResp;
import com.flat.logic.dto.resp.finance.BalanceListResp;
import com.flat.logic.dto.resp.finance.BalanceLogResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.finance.FlatBalanceLog;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.finance.FlatBalanceLogMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.system.entity.SysUser;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import jakarta.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class BalanceService {

    @Resource
    private FlatBalanceLogMapper balanceLogMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private BillService billService;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    @Transactional(rollbackFor = Exception.class)
    public WxPayMpOrderResult recharge(BalanceRechargeReq req, CusUser cusUser) {
        // 查询租赁记录
        FlatLease lease = leaseMapper.selectById(req.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        if(lease.getInvalidFlag() == 1) {
            throw new ServiceException("租赁记录已作废");
        }

        // 检查租赁状态
        if (lease.getStatus() != 2) {
            throw new ServiceException("租赁状态不正确");
        }

        if(!Objects.equals(lease.getCusId(), cusUser.getUserId())) {
            throw new ServiceException("只有承租方本人账号才可以发起充值");
        }

        // 查询企业
        FlatCompany company = companyMapper.selectById(lease.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("收款企业不存在");
        }

        // 创建充值账单
        String outTradeNo = "BALANCE" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);
        String title = BillConstants.WATER.equalsIgnoreCase(req.getPayTarget()) ? "水费充值账单" : "电费充值账单";

        FlatBill bill = new FlatBill();
        bill.setFlatId(lease.getFlatId());
        bill.setRoomId(lease.getRoomId());
        bill.setCusId(lease.getCusId());
        bill.setLeaseId(lease.getId());
        bill.setReceiveCompanyId(lease.getCompanyId());
        bill.setName(title);
        bill.setOrderNo(outTradeNo);
        bill.setMoney(req.getMoney());
        bill.setPayChannel(1);
        bill.setPayTarget(req.getPayTarget());
        bill.setUseCouponFlag(0);
        bill.setReadStatus(0);
        bill.setPayMode(0);
        bill.setPayStatus(0);
        bill.setInvoiceStatus(0);
        bill.setCreateBy(cusUser.getNickName());
        bill.setCreateTime(LocalDateTime.now());
        bill.setDelStatus(0);
        billMapper.insert(bill);

        return billService.pay(bill, company, cusUser);
    }

    public void handlePay(String outTradeNo, String xmlData) throws WxPayException {
        //根据outTradeNo查询账单
        FlatPayLog payLog = payLogMapper.selectFirst(q -> q
                .eq(FlatPayLog::getOutTradeNo, outTradeNo)
                .eq(FlatPayLog::getChannel, 0)
                .eq(FlatPayLog::getDelStatus, 0)
        );
        if(payLog == null) {
            throw new ServiceException("渠道支付记录未找到");
        }

        FlatBill bill = billMapper.selectById(payLog.getBillId());
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单未找到");
        }

        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        CusUser cusUser = cusUserMapper.selectById(bill.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        FlatLease lease = leaseMapper.selectById(bill.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录未找到");
        }

        FlatRoom room = roomMapper.selectById(lease.getRoomId());
        if(room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源未找到");
        }

        FlatCompany company = companyMapper.selectById(bill.getReceiveCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        WxPayOrderNotifyResult result = weChatPayService.parseOrderNotifyResult(company.getWxPayAppId(), company.getWxPayMchId(), xmlData);
        if(!"SUCCESS".equalsIgnoreCase(result.getResultCode())) {
            throw new ServiceException("支付失败");
        }

        payLog.setPayStatus(1);
        payLog.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payLog.setTransactionNo(result.getTransactionId());
        payLog.setUpdateTime(LocalDateTime.now());
        payLogMapper.updateById(payLog);

        bill.setPayStatus(1);
        bill.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        bill.setTransactionNo(result.getTransactionId());
        bill.setUpdateTime(LocalDateTime.now());
        billMapper.updateById(bill);


        // 添加余额变更记录
        FlatBalanceLog balanceLog = new FlatBalanceLog();
        balanceLog.setRoomId(bill.getRoomId());
        balanceLog.setCusId(bill.getCusId());
        balanceLog.setLeaseId(bill.getLeaseId());
        balanceLog.setAccountNo(BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? room.getWaterAccountNo() : room.getElectricAccountNo());
        balanceLog.setAccountType(BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? 1 : 2);
        balanceLog.setChangeType(6);
        balanceLog.setOldMoney(BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? lease.getWaterAccountBalance() : lease.getElectricAccountBalance());
        balanceLog.setNewMoney(BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? lease.getWaterAccountBalance().add(bill.getMoney()) : lease.getElectricAccountBalance().add(bill.getMoney()));
        balanceLog.setAmount(bill.getMoney());
        balanceLog.setBillId(bill.getId());
        balanceLog.setCreateBy(cusUser.getNickName());
        balanceLog.setCreateTime(LocalDateTime.now());
        balanceLog.setDelStatus(0);
        balanceLogMapper.insert(balanceLog);

        // 更新账户余额
        lease.setWaterAccountBalance(BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? lease.getWaterAccountBalance().add(bill.getMoney()) : lease.getWaterAccountBalance());
        lease.setElectricAccountBalance(BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? lease.getElectricAccountBalance() : lease.getElectricAccountBalance().add(bill.getMoney()));
        lease.setUpdateBy(cusUser.getNickName());
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
        if (maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
            weChatPublicMessageService.sendMessage(
                    maiUser,
                    "cAq85XSX6f_rdjvcoC1sZVB8ls7isKDkBRECICtxGN0",
                    null,
                    Map.of(
                            "thing10", Map.of("value", room.getName()),
                            "thing11", Map.of("value", cusUser.getRealName()),
                            "amount6", Map.of("value", bill.getMoney().toString()),
                            "thing14", Map.of("value", BillConstants.WATER.equalsIgnoreCase(bill.getPayTarget()) ? "水费缴费" : "电费缴费"),
                            "time5", Map.of("value", bill.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    )
            );
        }
    }

    public TablePage<BalanceListResp> queryRespPage(BalanceQueryReq req) {
        return PageUtils.paginate(() -> leaseMapper.selectBalanceList(req));
    }

    public BalanceDetailResp queryRespDetail(BalanceDetailReq req) {
        FlatLease lease = leaseMapper.selectById(req.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        FlatRoom room = roomMapper.selectById(lease.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        CusUser cusUser = cusUserMapper.selectById(lease.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在");
        }

        List<FlatBalanceLog> balanceLogs = balanceLogMapper.selectList(q -> q
                .eq(FlatBalanceLog::getLeaseId, req.getLeaseId())
                .eq(FlatBalanceLog::getAccountType, req.getType())
                .eq(FlatBalanceLog::getDelStatus, 0)
                .orderByDesc(FlatBalanceLog::getCreateTime)
        );

        BalanceDetailResp resp = new BalanceDetailResp();
        resp.setLeaseId(lease.getId());
        resp.setRoomId(lease.getRoomId());
        resp.setRoomName(room.getName());
        resp.setCusId(lease.getCusId());
        resp.setCusRealName(cusUser.getRealName());
        resp.setCusPhoneNumber(cusUser.getPhoneNumber());
        resp.setType(req.getType());
        resp.setAccountNo(req.getType() == 1 ? room.getWaterAccountNo() : room.getElectricAccountNo());
        resp.setBalance(req.getType() == 1 ? lease.getWaterAccountBalance() : lease.getElectricAccountBalance());
        resp.setLogs(balanceLogs);
        return resp;
    }


    public void change(BalanceChangeReq req, SysUser sysUser) {
        if(req.getMoney().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("金额必须大于0");
        }

        FlatLease lease = leaseMapper.selectFirst(q -> q
            .eq(FlatLease::getId, req.getLeaseId())
            .in(FlatLease::getStatus, List.of(2, 3, 5, 7))
            .eq(FlatLease::getDelStatus, 0)
        );
        if (lease == null) {
            throw new ServiceException("有效的租赁记录不存在");
        }

        FlatRoom room = roomMapper.selectById(lease.getRoomId());
        if(room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源未找到");
        }

        if(req.getOperateType() == 1) { // 添加
            if(req.getAccountType() == 1) { // 水费
                BigDecimal oldMoney = lease.getWaterAccountBalance();
                lease.setWaterAccountBalance(oldMoney.add(req.getMoney()));
                lease.setUpdateBy(sysUser.getUsername());
                lease.setUpdateTime(LocalDateTime.now());
                leaseMapper.updateById(lease);

                FlatBalanceLog log = new FlatBalanceLog();
                log.setRoomId(lease.getRoomId());
                log.setCusId(lease.getCusId());
                log.setLeaseId(lease.getId());
                log.setAccountNo(room.getWaterAccountNo());
                log.setAccountType(1);
                log.setChangeType(7);
                log.setOldMoney(oldMoney);
                log.setNewMoney(oldMoney.add(req.getMoney()));
                log.setAmount(req.getMoney());
                log.setCreateBy(sysUser.getUsername());
                log.setCreateTime(LocalDateTime.now());
                log.setDelStatus(0);
                log.setRemark(req.getRemark());
                balanceLogMapper.insert(log);
            } else if (req.getAccountType() == 2) { // 电费
                BigDecimal oldMoney = lease.getElectricAccountBalance();

                lease.setElectricAccountBalance(oldMoney.add(req.getMoney()));
                lease.setUpdateBy(sysUser.getUsername());
                lease.setUpdateTime(LocalDateTime.now());
                leaseMapper.updateById(lease);

                FlatBalanceLog log = new FlatBalanceLog();
                log.setRoomId(lease.getRoomId());
                log.setCusId(lease.getCusId());
                log.setLeaseId(lease.getId());
                log.setAccountNo(room.getElectricAccountNo());
                log.setAccountType(2);
                log.setChangeType(7);
                log.setOldMoney(oldMoney);
                log.setNewMoney(oldMoney.add(req.getMoney()));
                log.setAmount(req.getMoney());
                log.setCreateBy(sysUser.getUsername());
                log.setCreateTime(LocalDateTime.now());
                log.setDelStatus(0);
                log.setRemark(req.getRemark());
                balanceLogMapper.insert(log);
            }
        } else if(req.getOperateType() == 2) { // 减少
            if(req.getAccountType() == 1) { // 水费
                BigDecimal oldMoney = lease.getWaterAccountBalance();

                lease.setWaterAccountBalance(oldMoney.subtract(req.getMoney()));
                lease.setUpdateBy(sysUser.getUsername());
                lease.setUpdateTime(LocalDateTime.now());
                leaseMapper.updateById(lease);

                FlatBalanceLog log = new FlatBalanceLog();
                log.setRoomId(lease.getRoomId());
                log.setCusId(lease.getCusId());
                log.setLeaseId(lease.getId());
                log.setAccountNo(room.getWaterAccountNo());
                log.setAccountType(1);
                log.setChangeType(2);
                log.setOldMoney(oldMoney);
                log.setNewMoney(oldMoney.subtract(req.getMoney()));
                log.setAmount(req.getMoney());
                log.setCreateBy(sysUser.getUsername());
                log.setCreateTime(LocalDateTime.now());
                log.setDelStatus(0);
                log.setRemark(req.getRemark());
                balanceLogMapper.insert(log);

            } else if (req.getAccountType() == 2) { // 电费
                BigDecimal oldMoney = lease.getElectricAccountBalance();

                lease.setElectricAccountBalance(oldMoney.subtract(req.getMoney()));
                lease.setUpdateBy(sysUser.getUsername());
                lease.setUpdateTime(LocalDateTime.now());
                leaseMapper.updateById(lease);

                FlatBalanceLog log = new FlatBalanceLog();
                log.setRoomId(lease.getRoomId());
                log.setCusId(lease.getCusId());
                log.setLeaseId(lease.getId());
                log.setAccountNo(room.getElectricAccountNo());
                log.setAccountType(2);
                log.setChangeType(2);
                log.setOldMoney(oldMoney);
                log.setNewMoney(oldMoney.subtract(req.getMoney()));
                log.setAmount(req.getMoney());
                log.setCreateBy(sysUser.getUsername());
                log.setCreateTime(LocalDateTime.now());
                log.setDelStatus(0);
                log.setRemark(req.getRemark());
                balanceLogMapper.insert(log);
            }
        }
    }

    public TablePage<BalanceLogResp> queryLogRespPage(BalanceLogQueryReq req) {
        return PageUtils.paginate(() -> balanceLogMapper.selectRespList(req));
    }

    public BalanceLogResp queryLogRespDetail(Long id) {
        FlatBalanceLog balanceLog = balanceLogMapper.selectById(id);
        if (balanceLog == null || balanceLog.getDelStatus() == 1) {
            throw new ServiceException("余额变更记录不存在");
        }

        FlatRoom room = roomMapper.selectById(balanceLog.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源不存在");
        }

        CusUser cusUser = cusUserMapper.selectFirst(q -> q
                .eq(CusUser::getUserId, balanceLog.getCusId())
                .eq(CusUser::getDelStatus, 0)
        );
        if (cusUser == null) {
            throw new ServiceException("用户不存在");
        }

        BalanceLogResp resp = new BalanceLogResp();
        BeanUtils.copyProperties(balanceLog, resp);
        resp.setRoomName(room.getName());
        resp.setCusRealName(cusUser.getRealName());
        resp.setCusPhoneNumber(cusUser.getPhoneNumber());
        return resp;
    }

    /**
     * 删除余额变更记录（仅限管理员操作的记录）
     *
     * @param req 删除请求
     * @param sysUser 操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBalanceLog(IdReq req, SysUser sysUser) {
        // 查询余额变更记录
        FlatBalanceLog balanceLog = balanceLogMapper.selectById(req.getId());
        if (balanceLog == null || balanceLog.getDelStatus() == 1) {
            throw new ServiceException("余额变更记录不存在");
        }

        // 检查是否为管理员操作的记录
        if (balanceLog.getChangeType() != 2 && balanceLog.getChangeType() != 7) {
            throw new ServiceException("只能删除管理员操作的记录");
        }

        // 查询租赁记录
        FlatLease lease = leaseMapper.selectById(balanceLog.getLeaseId());
        if (lease == null || lease.getDelStatus() == 1) {
            throw new ServiceException("租赁记录不存在");
        }

        // 恢复余额数据
        if (balanceLog.getAccountType() == 1) { // 水费
            // 如果是管理员增加金额，则需要减去相应金额
            if (balanceLog.getChangeType() == 7) {
                lease.setWaterAccountBalance(lease.getWaterAccountBalance().subtract(balanceLog.getAmount()));
            }
            // 如果是管理员减少金额，则需要加上相应金额
            else if (balanceLog.getChangeType() == 2) {
                lease.setWaterAccountBalance(lease.getWaterAccountBalance().add(balanceLog.getAmount()));
            }
        } else if (balanceLog.getAccountType() == 2) { // 电费
            // 如果是管理员增加金额，则需要减去相应金额
            if (balanceLog.getChangeType() == 7) {
                lease.setElectricAccountBalance(lease.getElectricAccountBalance().subtract(balanceLog.getAmount()));
            }
            // 如果是管理员减少金额，则需要加上相应金额
            else if (balanceLog.getChangeType() == 2) {
                lease.setElectricAccountBalance(lease.getElectricAccountBalance().add(balanceLog.getAmount()));
            }
        }

        // 更新租赁记录
        lease.setUpdateBy(sysUser.getUsername());
        lease.setUpdateTime(LocalDateTime.now());
        leaseMapper.updateById(lease);

        // 逻辑删除余额变更记录
        balanceLog.setDelStatus(1);
        balanceLog.setUpdateBy(sysUser.getUsername());
        balanceLog.setUpdateTime(LocalDateTime.now());
        balanceLogMapper.updateById(balanceLog);
    }

}
