package com.flat.logic.service.account;

import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.flat.logic.dto.req.account.*;
import com.flat.logic.service.cloud.IdentityService;
import com.flat.logic.service.cloud.OSSService;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.service.cloud.WeChatPublicService;
import com.flat.logic.service.support.ChannelService;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 普通用户信息Service业务层处理
 */
@Service
public class CusUserService {

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatContractMapper contractMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private WeChatPublicService weChatPublicService;

    @Resource
    private ChannelService channelService;

    @Resource
    private IdentityService identityService;

    @Resource
    private OSSService ossService;

    /**
     * 查询普通用户信息列表
     *
     * @param req 普通用户信息
     * @return 普通用户信息
     */
    public TablePage<CusUser> queryPage(CusUserQueryReq req) {
        return PageUtils.paginate(() -> cusUserMapper.selectBaseList(req));
    }

    //查询详情
    public CusUser queryDetail(Long id) {
        CusUser cusUser = cusUserMapper.selectById(id);
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new RuntimeException("用户信息不存在");
        }
        return cusUser;
    }

    /**
     * 新增普通用户信息
     */
    public void add(
            String unionId, String openId,
            String nickName, String phoneNumber,
            String avatarUrl, Integer gender
    ) {
        CusUser cusUser = new CusUser();
        cusUser.setWxOpenId(openId);
        cusUser.setWxUnionId(unionId);
        cusUser.setNickName(nickName);
        cusUser.setPhoneNumber(phoneNumber);
        cusUser.setAvatar(avatarUrl);
        cusUser.setGender(gender);
        cusUser.setStatus(0);
        cusUser.setIsReal(0);
        cusUser.setCreateTime(LocalDateTime.now());
        cusUser.setUpdateTime(LocalDateTime.now());
        cusUser.setDelStatus(0);
        cusUserMapper.insert(cusUser);
    }

    /**
     * 修改普通用户信息
     *
     * @param cusUser 普通用户信息
     */
    public void modify(CusUser cusUser) {
        cusUserMapper.updateById(cusUser);
    }

    public CusUser selectCusUserByUnionId(String unionId) {
        return cusUserMapper.selectOne((query) -> query.eq(CusUser::getWxUnionId, unionId).eq(CusUser::getDelStatus, 0));
    }

    public CusUser selectCusUserByPhone(String phoneNumber) {
        return cusUserMapper.selectOne((query) -> query.eq(CusUser::getPhoneNumber, phoneNumber).eq(CusUser::getDelStatus, 0));
    }

    public JSONObject recognizeIdCard(IdCardRecognizeReq req) {
        return identityService.recognizeIdCard(ossService.joinUrl(req.getImagePath()));
    }

    public void authReal(RealNameAuthReq req, CusUser user) {
        // if(user.getIsReal() != null && user.getIsReal() == 1) {
        //     throw new RuntimeException("您已实名，无需再新增");
        // }

        if(cusUserMapper.selectCount(q -> q
                .eq(CusUser::getIdCard, req.getIdCard())
                .eq(CusUser::getDelStatus, 0)
                .eq(CusUser::getIsReal, 1)
                .ne(CusUser::getUserId, user.getUserId())
        ) > 0) {
            throw new ServiceException("该实名信息已存在");
        }

        // 判断是否存在有效的租赁记录，如果存在则不允许修改实名认证信息
        if (leaseMapper.selectCount(q -> q
                .eq(FlatLease::getCusId, user.getUserId())
                .in(FlatLease::getStatus, List.of(0, 2, 3, 4, 5, 6, 7, 8))
                .eq(FlatLease::getInvalidFlag, 0)
                .eq(FlatLease::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("存在有效的租赁记录，不允许修改实名认证信息");
        }

        JSONObject jsonObject = identityService.checkIDCard(req.getRealName(), req.getIdCard());
        if(!jsonObject.containsKey("status") || !jsonObject.getString("status").equalsIgnoreCase("01")) {
            throw new ServiceException("实名认证未通过");
        }

        user.setRealName(req.getRealName());
        user.setIdCard(req.getIdCard());
        user.setFrontUrl(req.getFrontUrl());
        user.setReverseUrl(req.getReverseUrl());
        user.setPhoneNumber(req.getPhoneNumber());
        user.setIsReal(1);
        cusUserMapper.updateById(user);
    }

    // 删除用户
    public void remove(Long id, SysUser sysUser) {
        // 检查用户是否存在且未被删除
        CusUser cusUser = cusUserMapper.selectById(id);
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new RuntimeException("用户不存在或已被删除");
        }

        // 检查是否有租赁记录
        List<FlatLease> leases = leaseMapper.selectList(q -> q
                .eq(FlatLease::getCusId, id)
                .eq(FlatLease::getDelStatus, 0)
        );
        if (!leases.isEmpty()) {
            throw new ServiceException("该用户已存在租赁记录，无法删除");
        }

        // 执行逻辑删除
        cusUserMapper.update(q -> q
                .set(CusUser::getDelStatus, 1)
                .set(CusUser::getUpdateTime, LocalDateTime.now())
                .set(CusUser::getUpdateBy, sysUser.getUsername())
                .eq(CusUser::getUserId, id)
                .eq(CusUser::getDelStatus, 0)
        );

        channelService.resetCusLogin(cusUser.getUserId());
    }

    public void clearPhoneNumber(Long cusUserId, SysUser sysUser) {
        cusUserMapper.update(q -> q
                .set(CusUser::getPhoneNumber, null)
                .set(CusUser::getUpdateTime, LocalDateTime.now())
                .set(CusUser::getUpdateBy, sysUser.getUsername())
                .eq(CusUser::getUserId, cusUserId)
                .eq(CusUser::getDelStatus, 0)
        );

        channelService.resetCusLogin(cusUserId);
    }

    public void clearReal(Long cusUserId, SysUser sysUser) {
        // 判断是否签约过合同，如果签约过则不允许清除实名认证信息
        if (contractMapper.selectCount(q -> q
                .eq(FlatContract::getCusId, cusUserId)
                .in(FlatContract::getStatus, List.of(4, 6))
                .eq(FlatContract::getDelStatus, 0)
        ) > 0) {
            throw new RuntimeException("该用户已签约过合同，不允许清除实名认证信息");
        }

        cusUserMapper.update(q -> q
                .set(CusUser::getIsReal, 0)
                .set(CusUser::getRealName, null)
                .set(CusUser::getIdCard, null)
                .set(CusUser::getUpdateTime, LocalDateTime.now())
                .set(CusUser::getUpdateBy, sysUser.getUsername())
                .eq(CusUser::getUserId, cusUserId)
                .eq(CusUser::getDelStatus, 0)
        );

        channelService.resetCusLogin(cusUserId);
    }

    public void authWxPublic(CusUser cusUser, String code) {
        JSONObject jsonObject = weChatPublicService.auth(code);
        String openId = jsonObject.getString("openid");
        String unionId = jsonObject.getString("unionid");

        cusUser.setWxPublicOpenId(openId);
        if(!cusUser.getWxUnionId().equalsIgnoreCase(unionId)) {
            throw new ServiceException("微信授权失败，非同一微信用户");
        }

        cusUserMapper.update(q -> q
                .set(CusUser::getWxPublicOpenId, openId)
                .set(CusUser::getUpdateTime, LocalDateTime.now())
                .set(CusUser::getUpdateBy, cusUser.getNickName())
                .eq(CusUser::getUserId, cusUser.getUserId())
                .eq(CusUser::getDelStatus, 0)
        );
    }
    
    /**
     * 根据请求对象修改用户信息
     *
     * @param req 用户信息修改请求
     * @param sysUser 操作人信息
     */
    public void updateUserInfo(CusUserModifyReq req, SysUser sysUser) {
        // 检查用户是否存在
        CusUser cusUser = cusUserMapper.selectById(req.getUserId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在或已被删除");
        }
        
        // 更新用户信息
        cusUserMapper.update(q -> q
                .set(CusUser::getNickName, req.getNickName())
                .set(CusUser::getPhoneNumber, req.getPhoneNumber())
                .set(CusUser::getAvatar, req.getAvatar())
                .set(CusUser::getGender, req.getGender())
                .set(CusUser::getStatus, req.getStatus())
                .set(CusUser::getUpdateTime, LocalDateTime.now())
                .set(CusUser::getUpdateBy, sysUser.getUsername())
                .eq(CusUser::getUserId, req.getUserId())
                .eq(CusUser::getDelStatus, 0)
        );

        channelService.resetCusLogin(cusUser.getUserId());
    }
    
    /**
     * 修改用户实名认证信息
     *
     * @param req 实名认证信息修改请求
     * @param sysUser 操作人信息
     */
    public void updateRealInfo(CusUserRealInfoModifyReq req, SysUser sysUser) {
        // 检查用户是否存在
        CusUser cusUser = cusUserMapper.selectById(req.getUserId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户不存在或已被删除");
        }
        
        // 如果要修改身份证信息且身份证号不为空，需要检查身份证是否已被其他用户使用
        if (req.getIdCard() != null && !req.getIdCard().isBlank()) {
            // 检查身份证号是否被其他用户使用
            if(cusUserMapper.selectCount(q -> q
                    .eq(CusUser::getIdCard, req.getIdCard())
                    .eq(CusUser::getDelStatus, 0)
                    .eq(CusUser::getIsReal, 1)
                    .ne(CusUser::getUserId, req.getUserId())
            ) > 0) {
                throw new ServiceException("该实名信息已被其他用户使用");
            }
        }
        
        // 判断是否存在有效的租赁记录，如果存在则不允许修改实名认证信息
        if (leaseMapper.selectCount(q -> q
                .eq(FlatLease::getCusId, req.getUserId())
                .in(FlatLease::getStatus, List.of(0, 2, 3, 4, 5, 6, 7, 8))
                .eq(FlatLease::getInvalidFlag, 0)
                .eq(FlatLease::getDelStatus, 0)
        ) > 0) {
            throw new ServiceException("存在有效的租赁记录，不允许修改实名认证信息");
        }
        
        // 更新实名认证信息
        cusUserMapper.update(q -> {
            if (req.getRealName() != null) {
                q.set(CusUser::getRealName, req.getRealName());
            }
            if (req.getIdCard() != null) {
                q.set(CusUser::getIdCard, req.getIdCard());
            }
            if (req.getFrontUrl() != null) {
                q.set(CusUser::getFrontUrl, req.getFrontUrl());
            }
            if (req.getReverseUrl() != null) {
                q.set(CusUser::getReverseUrl, req.getReverseUrl());
            }
            
            q.set(CusUser::getUpdateTime, LocalDateTime.now())
             .set(CusUser::getUpdateBy, sysUser.getUsername())
             .eq(CusUser::getUserId, req.getUserId())
             .eq(CusUser::getDelStatus, 0);
            
            return q;
        });

        channelService.resetCusLogin(cusUser.getUserId());
    }
}
