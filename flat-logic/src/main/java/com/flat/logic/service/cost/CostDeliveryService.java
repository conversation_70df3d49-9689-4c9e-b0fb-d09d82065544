package com.flat.logic.service.cost;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.cost.CostDeliveryAddReq;
import com.flat.logic.dto.req.cost.CostDeliveryEditReq;
import com.flat.logic.dto.req.cost.CostDeliveryQueryReq;
import com.flat.logic.entity.cost.FlatCostDelivery;
import com.flat.logic.mapper.cost.FlatCostDeliveryMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;

/**
 * 定点取货条目服务
 */
@Service
public class CostDeliveryService {

    @Resource
    private FlatCostDeliveryMapper costDeliveryMapper;

    /**
     * 添加定点取货条目
     */
    public void add(CostDeliveryAddReq req, SysUser sysUser) {
        FlatCostDelivery costDelivery = new FlatCostDelivery();
        BeanUtils.copyProperties(req, costDelivery);
        costDelivery.setCreateBy(sysUser.getUsername());
        costDelivery.setCreateTime(LocalDateTime.now());
        costDelivery.setDelStatus(0);
        costDeliveryMapper.insert(costDelivery);
    }

    /**
     * 修改定点取货条目
     */
    public void edit(CostDeliveryEditReq req, SysUser sysUser) {
        FlatCostDelivery costDelivery = costDeliveryMapper.selectById(req.getId());
        if (costDelivery == null || costDelivery.getDelStatus() == 1) {
            throw new ServiceException("费用定点取货信息不存在");
        }

        BeanUtils.copyProperties(req, costDelivery);
        costDelivery.setUpdateBy(sysUser.getUsername());
        costDelivery.setUpdateTime(LocalDateTime.now());
        costDeliveryMapper.updateById(costDelivery);
    }

    /**
     * 删除定点取货条目
     */
    public void remove(Long id, SysUser sysUser) {
        costDeliveryMapper.update(q -> q
                .set(FlatCostDelivery::getDelStatus, 1)
                .set(FlatCostDelivery::getUpdateTime, LocalDateTime.now())
                .set(FlatCostDelivery::getUpdateBy, sysUser.getUsername())
                .eq(FlatCostDelivery::getId, id)
                .eq(FlatCostDelivery::getDelStatus, 0)
        );
    }

    /**
     * 查询定点取货条目详情
     */
    public FlatCostDelivery queryDetail(Long id) {
        FlatCostDelivery costDelivery = costDeliveryMapper.selectById(id);
        if (costDelivery == null || costDelivery.getDelStatus() == 1) {
            throw new ServiceException("定点取货条目信息不存在");
        }
        return costDelivery;
    }

    /**
     * 查询定点取货条目列表
     *
     * @param req 查询条件
     * @param costTypeId 费用类型ID（可选）
     * @return 定点取货条目列表
     */
    public List<FlatCostDelivery> queryList(CostDeliveryQueryReq req, Long costTypeId) {
        return costDeliveryMapper.selectDeliveryList(req, costTypeId);
    }
    
    /**
     * 查询定点取货条目列表（不关联费用类型）
     */
    public List<FlatCostDelivery> queryList(CostDeliveryQueryReq req) {
        return queryList(req, null);
    }

    /**
     * 分页查询定点取货条目
     *
     * @param req 查询条件
     * @param costTypeId 费用类型ID（可选）
     * @return 分页结果
     */
    public TablePage<FlatCostDelivery> queryPage(CostDeliveryQueryReq req, Long costTypeId) {
        return PageUtils.paginate(() -> costDeliveryMapper.selectDeliveryList(req, costTypeId));
    }
    
    /**
     * 分页查询定点取货条目（不关联费用类型）
     */
    public TablePage<FlatCostDelivery> queryPage(CostDeliveryQueryReq req) {
        return queryPage(req, null);
    }
} 