package com.flat.logic.service.lease;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.commons.BillConstants;
import com.flat.logic.dto.req.lease.PayPlanOfflineCertEditReq;
import com.flat.logic.dto.req.lease.PayPlanOfflinePayReq;
import com.flat.logic.dto.req.lease.PayPlanQueryReq;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatPayPlan;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatPayPlanMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.finance.BillService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.Resource;

/**
 * 公寓合同付款计划Service业务层处理
 */
@Service
public class PayPlanService {

    @Resource
    private FlatPayPlanMapper payPlanMapper;

    @Resource
    private FlatContractMapper contractMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private BillService billService;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    public List<FlatPayPlan> calculateEnergyPlans(
            String companySymbol, LocalDate beginDate, Integer monthCount
    ) {
        if(!"hn".equalsIgnoreCase(companySymbol)) {
            return new ArrayList<>();
        }

        BigDecimal energyMonthMoney = new BigDecimal(sysConfigService.queryConfigByKey("ENERGY_MONTH_MONEY"));

        List<FlatPayPlan> plans = new ArrayList<>();
        for(int month = 0; month < monthCount; month++) {
            FlatPayPlan plan = new FlatPayPlan();
            plan.setBeginTime(LocalDateTimeUtil.beginOfDay(beginDate.plusMonths(month)));
            plan.setEndTime(LocalDateTimeUtil.endOfDay(beginDate.plusMonths(month + 1).minusDays(1), true));
            plan.setType(2);
            plan.setIndexNo(month + 1);
            plan.setName("第" + plan.getIndexNo() + "期");
            plan.setMoney(energyMonthMoney);
            plan.setUnpaidNotifyFlag(0);
            plans.add(plan);
        }

        return plans;
    }

    public List<FlatPayPlan> calculateRentPlans(
            String companySymbol, Integer durationType, BigDecimal monthMoney,
            LocalDate beginDate, Integer monthCount, Integer payPeriod
    ) {
        if("hn".equalsIgnoreCase(companySymbol) && durationType == 0) {
            return new ArrayList<>();
        }
        if (durationType == 0) { // 短租
            // 第一个付款周期是一个月，后面按照付租周期类型计算
            return calculateRentPlans(beginDate, monthMoney, monthCount, payPeriod, 1);
        } else { // 长租
            // 按照付租周期类型计算
            return calculateRentPlans(beginDate, monthMoney, monthCount, payPeriod, 0);
        }
    }

    public List<FlatPayPlan> calculateRentPlans(
            LocalDate beginDate, BigDecimal monthMoney, Integer monthCount, Integer payPeriod,
            Integer firstPeriodMonths
    ) {
        List<FlatPayPlan> periods = new ArrayList<>();

        LocalDateTime periodBeginTime = LocalDateTimeUtil.beginOfDay(beginDate);
        Integer remainMonths = monthCount;
        Integer indexNo = 0;
        int realMonths = 0;

        if(firstPeriodMonths > 0) {
            indexNo ++;
            FlatPayPlan firstPlan = new FlatPayPlan();
            firstPlan.setType(1);
            firstPlan.setIndexNo(indexNo);
            firstPlan.setName("第" + indexNo + "期");
            firstPlan.setBeginTime(periodBeginTime);
            realMonths = remainMonths < firstPeriodMonths ? remainMonths : firstPeriodMonths;
            firstPlan.setEndTime(LocalDateTimeUtil.endOfDay(periodBeginTime.plusMonths(realMonths).minusDays(1), true));
            firstPlan.setMoney(monthMoney.multiply(BigDecimal.valueOf(realMonths)));
            firstPlan.setUnpaidNotifyFlag(0);
            periods.add(firstPlan);
        }

        periodBeginTime = periodBeginTime.plusMonths(realMonths);
        remainMonths -= realMonths;
        if (remainMonths <= 0) {
            return periods;
        }

        int months = 1;
        switch (payPeriod) {
            case 0: // 按月付
                break;
            case 1: // 按季度付
                months = 3;
                break;
            case 2: // 按半年付
                months = 6;
                break;
            case 3: // 按年付
                months = 12;
                break;
            default:
                throw new ServiceException("付款周期类型不正确");
        }

        while (remainMonths > 0) {
            indexNo++;
            realMonths = remainMonths < months ? remainMonths : months;

            FlatPayPlan plan = new FlatPayPlan();
            plan.setType(1);
            plan.setIndexNo(indexNo);
            plan.setName("第" + plan.getIndexNo() + "期");
            plan.setBeginTime(periodBeginTime);
            plan.setEndTime(LocalDateTimeUtil.endOfDay(periodBeginTime.plusMonths(realMonths).minusDays(1), true));
            plan.setMoney(monthMoney.multiply(BigDecimal.valueOf(realMonths)));
            plan.setUnpaidNotifyFlag(0);
            periods.add(plan);

            periodBeginTime = periodBeginTime.plusMonths(realMonths);
            remainMonths -= realMonths;
        }

        return periods;
    }

    public List<FlatPayPlan> calculateShopPropertyPlans(
            LocalDate beginDate, Integer monthCount, BigDecimal monthPropertyMoney
    ) {

        List<FlatPayPlan> periods = new ArrayList<>();

        if(monthPropertyMoney == null || monthPropertyMoney.compareTo(BigDecimal.ZERO) <= 0) {
            return periods;
        }

        LocalDateTime periodBeginTime = LocalDateTimeUtil.beginOfDay(beginDate);
        Integer remainMonths = monthCount;
        Integer indexNo = 0;
        int realMonths = 0;

        periodBeginTime = periodBeginTime.plusMonths(realMonths);
        remainMonths -= realMonths;
        if (remainMonths <= 0) {
            return periods;
        }

        int months = 12;

        while (remainMonths > 0) {
            indexNo++;
            realMonths = remainMonths < months ? remainMonths : months;

            FlatPayPlan plan = new FlatPayPlan();
            plan.setType(3);
            plan.setIndexNo(indexNo);
            plan.setName("第" + plan.getIndexNo() + "期");
            plan.setBeginTime(periodBeginTime);
            plan.setEndTime(LocalDateTimeUtil.endOfDay(periodBeginTime.plusMonths(realMonths).minusDays(1), true));
            plan.setMoney(monthPropertyMoney.multiply(BigDecimal.valueOf(realMonths)));
            plan.setUnpaidNotifyFlag(0);
            periods.add(plan);

            periodBeginTime = periodBeginTime.plusMonths(realMonths);
            remainMonths -= realMonths;
        }

        return periods;
    }


    /**
     * 查询公寓付款计划
     * 
     * @param id 公寓付款计划主键
     * @return 公寓付款计划
     */
    public FlatPayPlan queryDetail(Long id) {
        FlatPayPlan rentPlan = payPlanMapper.selectById(id);
        if (rentPlan == null || rentPlan.getDelStatus() == 1) {
            throw new ServiceException("付款计划不存在");
        }

        return rentPlan;
    }

    /**
     * 查询公寓合同付款计划列表
     */
    public TablePage<FlatPayPlan> queryPage(PayPlanQueryReq queryReq) {
        return PageUtils.paginate(() -> payPlanMapper.selectBaseList(queryReq));
    }

    public List<FlatPayPlan> queryList(PayPlanQueryReq queryReq) {
        return payPlanMapper.selectBaseList(queryReq);
    }

    public List<FlatPayPlan> queryListByLeaseId(Long leaseId) {
        return payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getLeaseId, leaseId)
                .eq(FlatPayPlan::getDelStatus, 0)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayMpOrderResult pay(Long id, CusUser user) {
        FlatPayPlan plan = payPlanMapper.selectById(id);
        if (plan == null || plan.getDelStatus() == 1) {
            throw new ServiceException("合同付款计划不存在");
        }

        if(plan.getPayStatus() == 1) {
            throw new ServiceException("该付款计划已支付");
        }

        if(plan.getMoney().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("该付款计划无需支付");
        }

        if(plan.getInvalidFlag() == 1) {
            throw new ServiceException("该付款计划已作废，无法支付");
        }

        FlatContract contract = contractMapper.selectById(plan.getContractId());
        if (contract == null || contract.getDelStatus() == 1) {
            throw new ServiceException("合同不存在");
        }

        if(contract.getStatus() != 4) {
            throw new ServiceException("合同还未签署完成，无法支付");
        }

        FlatCompany company = companyMapper.selectById(contract.getCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业不存在");
        }

        // 查询账单是否存在，如果存在则不再生成
        FlatBill bill = billMapper.selectFirst(q -> q
                .eq(FlatBill::getPayPlanId, plan.getId())
                .eq(FlatBill::getPayTarget, BillConstants.PLAN)
                .eq(FlatBill::getCusId, contract.getCusId())
                .eq(FlatBill::getDelStatus, 0)
        );
        if(bill == null) {
            String outTradeNo = "PLAN" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);
            bill = new FlatBill();
            bill.setOrderNo(outTradeNo);
            bill.setMoney(plan.getMoney());
            bill.setPayChannel(1);
            bill.setCusId(contract.getCusId()); // 属于用户的账单
            bill.setPayTarget(BillConstants.PLAN);
            bill.setReadStatus(0);
            bill.setFlatId(contract.getFlatId());
            bill.setRoomId(contract.getRoomId());
            bill.setLeaseId(plan.getLeaseId());
            bill.setInvoiceStatus(0);
            bill.setUseCouponFlag(0);
            bill.setPayMode(0);
            bill.setPayStatus(0);
            bill.setPayPlanId(plan.getId());
            bill.setPayPlanType(plan.getType());
            bill.setCreateBy(user.getNickName());
            bill.setCreateTime(LocalDateTime.now());
            bill.setDelStatus(0);
            bill.setName(plan.getName() + (plan.getType() == 1 ? "租金" : "能耗费"));
            bill.setReceiveCompanyId(company.getId());
            billMapper.insert(bill);
        } else {
            if(bill.getPayStatus() == 1) {
                throw new ServiceException("账单已支付，请勿重复支付");
            }
        }

        return billService.pay(bill, company, user);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlePay(String outTradeNo, String xmlData) throws WxPayException {
        //根据outTradeNo查询账单
        FlatPayLog payLog = payLogMapper.selectFirst(q -> q
                .eq(FlatPayLog::getOutTradeNo, outTradeNo)
                .eq(FlatPayLog::getChannel, 0)
                .eq(FlatPayLog::getDelStatus, 0)
        );
        if(payLog == null) {
            throw new ServiceException("渠道支付记录未找到");
        }

        FlatBill bill = billMapper.selectById(payLog.getBillId());
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单未找到");
        }

        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        FlatPayPlan payPlan = payPlanMapper.selectById(bill.getPayPlanId());
        if (payPlan == null || payPlan.getDelStatus() == 1) {
            throw new ServiceException("付款计划未找到");
        }

        CusUser cusUser = cusUserMapper.selectById(payPlan.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        FlatCompany company = companyMapper.selectById(bill.getReceiveCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        WxPayOrderNotifyResult result = weChatPayService.parseOrderNotifyResult(company.getWxPayAppId(), company.getWxPayMchId(), xmlData);
        if(!"SUCCESS".equalsIgnoreCase(result.getResultCode())) {
            throw new ServiceException("支付失败");
        }

        payLog.setPayStatus(1);
        payLog.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payLog.setTransactionNo(result.getTransactionId());
        payLog.setUpdateTime(LocalDateTime.now());
        payLogMapper.updateById(payLog);

        bill.setPayStatus(1);
        bill.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        bill.setTransactionNo(result.getTransactionId());
        bill.setUpdateTime(LocalDateTime.now());
        billMapper.updateById(bill);

        payPlan.setPayMode(0);
        payPlan.setPayStatus(1);
        payPlan.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payPlan.setUpdateTime(LocalDateTime.now());
        payPlanMapper.updateById(payPlan);

        FlatRoom room = roomMapper.selectById(payPlan.getRoomId());
        if(room != null && room.getDelStatus() == 0) {
            MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
            if(maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
                String type = "其他支付计划付款";
                if(payPlan.getType() == 1) {
                    type = payPlan.getName() + "租金支付";
                } else if(payPlan.getType() == 2) {
                    type = payPlan.getName() + "能耗费支付";
                } else if(payPlan.getType() == 3) {
                    type = payPlan.getName() + "商铺物业费支付";
                }

                weChatPublicMessageService.sendMessage(
                        maiUser,
                        "cAq85XSX6f_rdjvcoC1sZVB8ls7isKDkBRECICtxGN0",
                        null,
                        Map.of(
                                "thing10", Map.of("value", room.getName()),
                                "thing11", Map.of("value", cusUser.getRealName()),
                                "amount6", Map.of("value", bill.getMoney().toString()),
                                "thing14", Map.of("value", type),
                                "time5", Map.of("value", bill.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        )
                );
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void offlinePay(PayPlanOfflinePayReq req, SysUser sysUser) {
        FlatPayPlan payPlan = payPlanMapper.selectById(req.getId());
        if (payPlan == null || payPlan.getDelStatus() == 1) {
            throw new ServiceException("付款计划不存在");
        }

        if(payPlan.getPayStatus() == 1) {
            throw new ServiceException("该付款计划已支付");
        }

        if(payPlan.getMoney().compareTo(BigDecimal.ZERO) <= 0) {    
            throw new ServiceException("该付款计划无需支付");
        }

        if(payPlan.getInvalidFlag() == 1) {
            throw new ServiceException("该付款计划已作废，无法支付");
        }

        payPlan.setCertNo(req.getCertNo());
        payPlan.setCertFileUrl(req.getCertFileUrl());
        payPlan.setPayMode(1);
        payPlan.setPayStatus(1);
        payPlan.setPayTime(req.getPayTime());
        payPlan.setUpdateTime(LocalDateTime.now());
        payPlan.setUpdateBy(sysUser.getNickName());
        payPlanMapper.updateById(payPlan);
    }

    // 更新线下付款凭证数据
    public void editOfflineCert(PayPlanOfflineCertEditReq req, SysUser sysUser) {
        FlatPayPlan payPlan = payPlanMapper.selectById(req.getId());
        if (payPlan == null || payPlan.getDelStatus() == 1) {
            throw new ServiceException("付款计划不存在");
        }

        if(payPlan.getInvalidFlag() == 1) {
            throw new ServiceException("该付款计划已作废，无法修改");
        }

        if(payPlan.getPayStatus() != 1) {
            throw new ServiceException("该付款计划未支付");
        }

        if(payPlan.getPayMode() != 1) {
            throw new ServiceException("该付款计划不是线下支付");
        }

        payPlan.setCertNo(req.getCertNo());
        payPlan.setCertFileUrl(req.getCertFileUrl());
        payPlan.setPayTime(req.getPayTime());
        payPlan.setUpdateTime(LocalDateTime.now());
        payPlan.setUpdateBy(sysUser.getNickName());
        payPlanMapper.updateById(payPlan);
    }
}
