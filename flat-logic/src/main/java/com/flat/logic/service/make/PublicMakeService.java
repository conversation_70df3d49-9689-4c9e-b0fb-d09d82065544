package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.commons.BillConstants;
import com.flat.logic.dto.req.VerifyReq;
import com.flat.logic.dto.req.make.PublicMakeAddReq;
import com.flat.logic.dto.req.make.PublicMakeQueryReq;
import com.flat.logic.dto.resp.make.PublicMakeDetailResp;
import com.flat.logic.dto.resp.make.PublicMakeListResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.entity.make.FlatPublic;
import com.flat.logic.entity.make.FlatPublicMake;
import com.flat.logic.entity.make.FlatPublicPeriod;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.mapper.make.FlatPublicMakeMapper;
import com.flat.logic.mapper.make.FlatPublicMapper;
import com.flat.logic.mapper.make.FlatPublicPeriodMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.logic.service.finance.BillService;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 公寓公用设施预约Service业务层处理
 */
@Service
public class PublicMakeService {

    @Resource
    private FlatPublicMakeMapper publicMakeMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private FlatPublicMapper publicMapper;

    @Resource
    private FlatPublicPeriodMapper publicPeriodMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private BillService billService;

     @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;


    public TablePage<PublicMakeListResp> queryRespPage(PublicMakeQueryReq req) {
        String icon = sysConfigService.queryConfigByKey("PUBLIC_ICON");
        return PageUtils.paginate(
                () -> publicMakeMapper.selectRespList(req),
                (e) -> {
                    e.setIcon(icon);
                    e.setTitle("预约公共设施");
                    return e;
                }
        );
    }

    public TablePage<PublicMakeListResp> queryRespPageForButter(PublicMakeQueryReq req, MaiUser butter) {
        if(!butter.getPermission().contains("public-make-verify")) {
            throw new ServiceException("无权限查看记录列表");
        }

        req.setButlerId(null);

        return queryRespPage(req);
    }

    public PublicMakeDetailResp queryRespDetail(Long id) {
        PublicMakeListResp listResp = publicMakeMapper.selectRespById(id);
        if (listResp == null) {
            throw new ServiceException("公共设施预约信息不存在");
        }

        String icon = sysConfigService.queryConfigByKey("PUBLIC_ICON");

        PublicMakeDetailResp detailResp = new PublicMakeDetailResp();
        BeanUtils.copyProperties(listResp, detailResp);

        List<FlatMakeLog> logs = makeLogMapper.selectList(q -> q
                .eq(FlatMakeLog::getSourceId, listResp.getId())
                .eq(FlatMakeLog::getType, "make_public")
                .eq(FlatMakeLog::getDelStatus, 0)
                .orderByAsc(FlatMakeLog::getCreateTime)
        );
        if (logs != null && !logs.isEmpty()) {
            detailResp.setLogs(logs);
        }

        detailResp.setIcon(icon);
        detailResp.setTitle("预约公共设施");

        return detailResp;
    }

    @Transactional(rollbackFor = Exception.class)
    public FlatPublicMake add(PublicMakeAddReq addReq, CusUser cusUser) {
        FlatPublic pub = publicMapper.selectById(addReq.getPublicId());
        if (pub == null || pub.getDelStatus() == 1) {
            throw new ServiceException("公共设施信息不存在");
        }

        FlatPublicPeriod period = publicPeriodMapper.selectById(addReq.getPublicPeriodId());
        if (period == null || period.getDelStatus() == 1) {
            throw new ServiceException("公共设施时段信息不存在");
        }

        FlatRoom room = roomMapper.selectById(addReq.getRoomId());
        if (room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房源未找到");
        }

        //超过时间段截止时间不可预约
        LocalDateTime makeDateTime = LocalDateTime.of(addReq.getMakeDate(), period.getEndTime());
        if (makeDateTime.isBefore(LocalDateTime.now())) {
            throw new ServiceException("超过时间段截止时间不可预约");
        }

        // 判断该时间段内的人数是否已经约满了
        int hasMakePeopleCount = publicMakeMapper.selectPeopleCount(addReq.getPublicId(), addReq.getPublicPeriodId(), addReq.getMakeDate());
        if (hasMakePeopleCount >= period.getPeopleCount()) {
            throw new ServiceException("该时段已约满");
        }
        if (addReq.getPeopleCount() > (period.getPeopleCount() - hasMakePeopleCount)) {
            throw new ServiceException("预约人数大于剩余人数");
        }

        List<FlatLease> leases = leaseMapper.selectList(q -> q
                .eq(FlatLease::getCusId, cusUser.getUserId())
                .eq(FlatLease::getRoomId, addReq.getRoomId())
                .eq(FlatLease::getStatus, 2)
                .eq(FlatLease::getDelStatus, 0)
                .eq(FlatLease::getInvalidFlag, 0)
                .orderByDesc(FlatLease::getCreateTime)
        );

        List<FlatLive> lives = liveMapper.selectList(q -> q
                .eq(FlatLive::getLiveCusId, cusUser.getUserId())
                .eq(FlatLive::getRoomId, addReq.getRoomId())
                .eq(FlatLive::getStatus, 2)
                .eq(FlatLive::getDelStatus, 0)
                .orderByDesc(FlatLive::getCreateTime)
        );

        if(leases.isEmpty() && lives.isEmpty()) {
            throw new ServiceException("该公寓中不存在您生效的租住或同住信息");
        }

        FlatPublicMake publicMake = new FlatPublicMake();
        publicMake.setFlatId(pub.getFlatId());
        publicMake.setRoomId(leases.isEmpty() ? lives.get(0).getRoomId() : leases.get(0).getRoomId());
        publicMake.setApplyCusType(leases.isEmpty() ? 2 : 1);
        publicMake.setCusId(cusUser.getUserId());
        publicMake.setLeaseId(leases.isEmpty() ? lives.get(0).getLeaseId() : leases.get(0).getId());
        publicMake.setPublicId(pub.getId());
        publicMake.setMakeUserName(addReq.getMakeUserName());
        publicMake.setMakeUserPhoneNumber(addReq.getMakeUserPhoneNumber());
        publicMake.setMakeDate(addReq.getMakeDate());
        publicMake.setMakeBeginTime(period.getBeginTime());
        publicMake.setMakeEndTime(period.getEndTime());
        publicMake.setPeopleCount(addReq.getPeopleCount());
        publicMake.setPublicPeriodId(addReq.getPublicPeriodId());
        publicMake.setNeedPayFlag(pub.getNeedPayFlag());
        if(pub.getNeedPayFlag() == 1) {
            publicMake.setMoney(pub.getPrice());
        }
        publicMake.setButlerId(leases.isEmpty() ? lives.get(0).getButlerId() : leases.get(0).getButlerId());
        publicMake.setPropertyId(leases.isEmpty() ? lives.get(0).getPropertyId() : leases.get(0).getPropertyId());
        publicMake.setStatus(0);
        publicMake.setPayStatus(0);
        publicMake.setDescription(addReq.getDescription());
        publicMake.setCreateBy(cusUser.getNickName());
        publicMake.setCreateTime(LocalDateTime.now());
        publicMake.setDelStatus(0);
        publicMakeMapper.insert(publicMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(publicMake.getId());
        log.setType("make_public");
        log.setRefuse("公共设施预约提交成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        //消息通知
        messageService.cusToButler(
                cusUser.getUserId(), publicMake.getButlerId(),
                cusUser.getNickName() + "预约公共设施",
                "/pages/butler/home/<USER>/info/index?id=" + publicMake.getId() + "&type=3"
        );

        MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
        if (maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
            weChatPublicMessageService.sendMessage(
                    maiUser,
                    "5xHwZhTGPXH4ZhYHmCjF66RLqY2lmqXK0F1OnsdCaOo",
                    "/pages/butler/home/<USER>/info/index?id=" + publicMake.getId() + "&type=3",
                    Map.of(
                            "thing11", Map.of("value", cusUser.getRealName()),
                            "time12", Map.of("value", publicMake.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                            "const13", Map.of("value", pub.getName()),
                            "phone_number4", Map.of("value", cusUser.getPhoneNumber())
                    )
            );
        }

        return publicMake;
    }

    // 撤销申请
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long publicMakeId, CusUser cusUser) {
        FlatPublicMake publicMake = publicMakeMapper.selectById(publicMakeId);
        if (publicMake == null || publicMake.getDelStatus() == 1) {
            throw new ServiceException("预约公共设施信息不存在");
        }

        if (publicMake.getStatus() == 1) {
            throw new ServiceException("该申请已撤销");
        }

        if(publicMake.getStatus() != 0) {
            throw new ServiceException("该申请已处理，无法撤销");
        }

        if (publicMake.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，无法撤销");
        }

        publicMake.setStatus(1);
        publicMake.setCancelTime(LocalDateTime.now());
        publicMake.setUpdateBy(cusUser.getNickName());
        publicMake.setUpdateTime(LocalDateTime.now());
        publicMakeMapper.updateById(publicMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(publicMakeId);
        log.setType("make_public");
        log.setRefuse("公共设施预约撤销成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        //消息通知
        messageService.cusToButler(
                MessageService.TYPE_CHANGE,
                cusUser.getUserId(), publicMake.getButlerId(),
                cusUser.getRealName() + "撤销公共设施预约",
                "/pages/butler/home/<USER>/info/index?id=" + publicMakeId + "&type=3"
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void verify(VerifyReq req, MaiUser maiUser) {
        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatPublicMake publicMake = publicMakeMapper.selectById(req.getId());
        if(publicMake == null) {
            throw new ServiceException("公共设施预约信息不存在");
        }

        if(publicMake.getStatus() == 1) {
            throw new ServiceException("公共设施预约已撤销");
        }

        if(publicMake.getNeedPayFlag() == 1 && publicMake.getPayStatus() == 0) {
            throw new ServiceException("账单未支付，无法审核");
        }

        if("BUTLER".equalsIgnoreCase(maiUser.getUserType())) { // 是管家角色
            if(publicMake.getStatus() != 0) {
                throw new ServiceException("公共设施预约管家已审核");
            }

            if(req.getStatus() == 1) {
                publicMake.setStatus(3);
            } else {
                publicMake.setStatus(2);
                publicMake.setRefuse(req.getRefuse());
            }
            publicMake.setVerifyButterId(maiUser.getUserId());
            publicMake.setButlerVerifyTime(LocalDateTime.now());
            publicMake.setUpdateBy(maiUser.getNickName());
            publicMake.setUpdateTime(LocalDateTime.now());
            publicMakeMapper.updateById(publicMake);

            //记录操作流程
            FlatMakeLog log = new FlatMakeLog();
            log.setOperateId(maiUser.getUserId());
            log.setOperateName(maiUser.getNickName());
            log.setSourceId(publicMake.getId());
            log.setType("make_public");
            log.setRefuse(req.getStatus() == 1 ? "公共设施预约管家审核通过" : "公共设施预约管家审核未通过");
            log.setDelStatus(0);
            log.setCreateBy(maiUser.getNickName());
            log.setCreateTime(LocalDateTime.now());
            makeLogMapper.insert(log);

            if(req.getStatus() == 1) { // 审核通过，通知用户和物业
                messageService.butlerToCus(
                        maiUser.getUserId(),
                        publicMake.getCusId(),
                        "公共设施预约管家审核通过",
                        "/pages/user/viewRecord/info/index?id=" + publicMake.getId() + "&type=3"
                );

                messageService.butlerToProperty(
                        maiUser.getUserId(),
                        publicMake.getPropertyId(),
                        "公共设施预约管家审核通过",
                        "/pages/property/info/index?id=" + publicMake.getId() + "&type=1"
                );
            } else { // 审核未通过，通知用户
                messageService.butlerToCus(
                        maiUser.getUserId(),
                        publicMake.getCusId(),
                        "公共设施预约管家审核未通过",
                        "/pages/user/viewRecord/info/index?id=" + publicMake.getId() + "&type=3"
                );
            }
        } else if("PROPERTY".equalsIgnoreCase(maiUser.getUserType())) { // 是物业角色
            if(publicMake.getStatus() > 3) {
                throw new ServiceException("公共设施预约物业已审核");
            }

            if(publicMake.getStatus() != 3) {
                throw new ServiceException("公共设施预约管家未审核通过，物业无法审核");
            }

            if(req.getStatus() == 1) {
                publicMake.setStatus(5);
            } else {
                publicMake.setStatus(4);
                publicMake.setRefuse(req.getRefuse());
            }
            publicMake.setPropertyId(maiUser.getUserId());
            publicMake.setPropertyVerifyTime(LocalDateTime.now());
            publicMake.setUpdateBy(maiUser.getNickName());
            publicMake.setUpdateTime(LocalDateTime.now());
            publicMakeMapper.updateById(publicMake);

            //记录操作流程
            FlatMakeLog log = new FlatMakeLog();
            log.setOperateId(maiUser.getUserId());
            log.setOperateName(maiUser.getNickName());
            log.setSourceId(publicMake.getId());
            log.setType("make_public");
            log.setRefuse(req.getStatus() == 1 ? "公共设施预约物业审核通过" : "公共设施预约物业审核未通过");
            log.setDelStatus(0);
            log.setCreateBy(maiUser.getNickName());
            log.setCreateTime(LocalDateTime.now());
            makeLogMapper.insert(log);

            if(req.getStatus() == 1) { // 审核通过，通知管家和用户
                messageService.propertyToCus(
                        maiUser.getUserId(),
                        publicMake.getCusId(),
                        "公共设施预约物业审核通过",
                        "/pages/user/viewRecord/info/index?id=" + publicMake.getId() + "&type=3"
                );

                messageService.propertyToButler(
                        maiUser.getUserId(),
                        publicMake.getButlerId(),
                        "公共设施预约物业审核通过",
                        "/pages/butler/home/<USER>/info/index?id=" + publicMake.getId() + "&type=3"
                );
            } else { // 审核未通过，通知用户和管家
                messageService.propertyToCus(
                        maiUser.getUserId(),
                        publicMake.getCusId(),
                        "公共设施预约物业审核未通过",
                        "/pages/user/viewRecord/info/index?id=" + publicMake.getId() + "&type=3"
                );

                messageService.propertyToButler(
                        maiUser.getUserId(),
                        publicMake.getButlerId(),
                        "公共设施预约物业审核未通过",
                        "/pages/butler/home/<USER>/info/index?id=" + publicMake.getId() + "&type=3"
                );
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayMpOrderResult pay(Long publicId, CusUser cusUser) {
        FlatPublicMake publicMake = publicMakeMapper.selectById(publicId);
        if(publicMake == null) {
            throw new ServiceException("预约公共设施信息不存在");
        }

        if(publicMake.getStatus() == 1) {
            throw new ServiceException("公共设施预约已撤销");
        }

        if(publicMake.getNeedPayFlag() == 0) {
            throw new ServiceException("该预约无需支付");
        }

        if (publicMake.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，无需重复支付");
        }

        FlatCompany company = companyMapper.selectBySymbol("by");
        if (company == null) {
            throw new ServiceException("收款企业不存在");
        }

        // 查找账单
        FlatBill bill = billMapper.selectFirst(q -> q
                .eq(FlatBill::getPublicMakeId, publicMake.getId())
                .eq(FlatBill::getPayTarget, BillConstants.PUBLIC)
                .eq(FlatBill::getCusId, cusUser.getUserId())
                .eq(FlatBill::getDelStatus, 0)
        );
        if(bill == null) {
            String outTradeNo = "PUB" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);
            bill = new FlatBill();
            bill.setOrderNo(outTradeNo);
            bill.setMoney(publicMake.getMoney());
            bill.setPayChannel(1);
            bill.setCusId(publicMake.getCusId()); // 属于用户的账单
            bill.setPayTarget(BillConstants.PUBLIC);
            bill.setReadStatus(0);
            bill.setFlatId(publicMake.getFlatId());
            bill.setRoomId(publicMake.getRoomId());
            bill.setLeaseId(publicMake.getLeaseId());
            bill.setPayMode(0);
            bill.setPayStatus(0);
            bill.setInvoiceStatus(0);
            bill.setUseCouponFlag(0);
            bill.setPublicMakeId(publicMake.getId());
            bill.setCreateBy(cusUser.getNickName());
            bill.setCreateTime(LocalDateTime.now());
            bill.setDelStatus(0);
            bill.setName("公共设施预约付款");
            bill.setReceiveCompanyId(company.getId());
            billMapper.insert(bill);
        } else {
            if(bill.getPayStatus() == 1) {
                throw new ServiceException("账单已支付，请勿重复支付");
            }
        }

        return billService.pay(bill, company, cusUser);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlePay(String outTradeNo, String xmlData) throws WxPayException {
        //根据outTradeNo查询账单
        FlatPayLog payLog = payLogMapper.selectFirst(q -> q
                .eq(FlatPayLog::getOutTradeNo, outTradeNo)
                .eq(FlatPayLog::getChannel, 0)
                .eq(FlatPayLog::getDelStatus, 0)
        );
        if(payLog == null) {
            throw new ServiceException("渠道支付记录未找到");
        }

        FlatBill bill = billMapper.selectById(payLog.getBillId());
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单未找到");
        }

        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        FlatPublicMake publicMake = publicMakeMapper.selectById(bill.getPublicMakeId());
        if (publicMake == null || publicMake.getDelStatus() == 1) {
            throw new ServiceException("公共设施预约记录未找到");
        }



        FlatCompany company = companyMapper.selectById(bill.getReceiveCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        CusUser cusUser = cusUserMapper.selectById(publicMake.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        WxPayOrderNotifyResult result = weChatPayService.parseOrderNotifyResult(company.getWxPayAppId(), company.getWxPayMchId(), xmlData);
        if(!"SUCCESS".equalsIgnoreCase(result.getResultCode())) {
            throw new ServiceException("支付失败");
        }

        payLog.setPayStatus(1);
        payLog.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payLog.setTransactionNo(result.getTransactionId());
        payLog.setUpdateTime(LocalDateTime.now());
        payLogMapper.updateById(payLog);

        bill.setPayStatus(1);
        bill.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        bill.setTransactionNo(result.getTransactionId());
        bill.setUpdateTime(LocalDateTime.now());
        billMapper.updateById(bill);

        publicMake.setPayStatus(1);
        publicMake.setPayMode(0);
        publicMake.setUpdateTime(LocalDateTime.now());
        publicMakeMapper.updateById(publicMake);

        // 记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(bill.getCusId());
        log.setOperateName(bill.getCreateBy());
        log.setSourceId(bill.getPublicMakeId());
        log.setType("make_public");
        log.setRefuse("预约公共设施缴费成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        FlatRoom room = roomMapper.selectById(publicMake.getRoomId());
        if(room != null && room.getDelStatus() == 0) {
            MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
            if (maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
                weChatPublicMessageService.sendMessage(
                        maiUser,
                        "cAq85XSX6f_rdjvcoC1sZVB8ls7isKDkBRECICtxGN0",
                        null,
                        Map.of(
                                "thing10", Map.of("value", room.getName()),
                                "thing11", Map.of("value", cusUser.getRealName()),
                                "amount6", Map.of("value", bill.getMoney().toString()),
                                "thing14", Map.of("value", "公共设施预约缴费"),
                                "time5", Map.of("value", bill.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        )
                );
            }
        }
    }

    // 删除公共设施预约记录
    public void remove(Long id, SysUser user) {
        publicMakeMapper.update(q -> q
                .set(FlatPublicMake::getDelStatus, 1)
                .set(FlatPublicMake::getUpdateTime, LocalDateTime.now())
                .set(FlatPublicMake::getUpdateBy, user.getUsername())
                .eq(FlatPublicMake::getId, id)
                .eq(FlatPublicMake::getDelStatus, 0)
        );
    }
}
