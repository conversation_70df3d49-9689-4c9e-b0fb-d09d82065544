package com.flat.logic.service.make;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.common.utils.bean.BeanUtils;
import com.flat.logic.dto.req.VerifyReq;
import com.flat.logic.dto.req.make.RepairMakeAddReq;
import com.flat.logic.dto.req.make.RepairMakePropertyVerifyReq;
import com.flat.logic.dto.req.make.RepairMakeQueryReq;
import com.flat.logic.dto.resp.make.RepairMakeDetailResp;
import com.flat.logic.dto.resp.make.RepairMakeListResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatLease;
import com.flat.logic.entity.lease.FlatLive;
import com.flat.logic.entity.make.FlatRepairEvaluate;
import com.flat.logic.entity.make.FlatRepairMake;
import com.flat.logic.entity.make.FlatRepairType;
import com.flat.logic.entity.trash.FlatMakeLog;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatLeaseMapper;
import com.flat.logic.mapper.lease.FlatLiveMapper;
import com.flat.logic.mapper.make.FlatRepairEvaluateMapper;
import com.flat.logic.mapper.make.FlatRepairMakeMapper;
import com.flat.logic.mapper.make.FlatRepairTypeMapper;
import com.flat.logic.mapper.trash.FlatMakeLogMapper;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.logic.service.support.MessageService;
import com.flat.system.entity.SysUser;
import com.flat.system.service.SysConfigService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 公寓预约报修Service业务层处理
 */
@Service
public class RepairMakeService {

    @Resource
    private FlatRepairMakeMapper repairMakeMapper;

    @Resource
    private FlatRepairTypeMapper repairTypeMapper;

    @Resource
    private FlatRepairEvaluateMapper repairEvaluateMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private FlatMakeLogMapper makeLogMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private FlatLeaseMapper leaseMapper;

    @Resource
    private FlatLiveMapper liveMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    @Transactional(rollbackFor = Exception.class)
    public void add(RepairMakeAddReq req, CusUser cusUser) {
        FlatRoom room = roomMapper.selectById(req.getRoomId());
        if(room == null || room.getDelStatus() == 1) {
            throw new ServiceException("房间信息不存在");
        }

        MaiUser maiUser = maiUserMapper.selectById(room.getButlerId());
        if(maiUser == null || maiUser.getDelStatus() == 1) {
            throw new ServiceException("管家信息不存在");
        }

        FlatRepairType repairType = repairTypeMapper.selectById(req.getRepairTypeId());
        if(repairType == null || repairType.getDelStatus() == 1) {
            throw new ServiceException("保修类型不存在");
        }

        FlatLease lease = leaseMapper.selectFirst(q -> q
                .eq(FlatLease::getRoomId, req.getRoomId())
                .eq(FlatLease::getCusId, cusUser.getUserId())
                .eq(FlatLease::getDelStatus, 0)
                .eq(FlatLease::getStatus, 2)
        );

        if(lease != null && lease.getInvalidFlag() == 1) {
            throw new ServiceException("该租赁已作废");
        }

        FlatLive live = liveMapper.selectFirst(q -> q
                .eq(FlatLive::getRoomId, req.getRoomId())
                .eq(FlatLive::getLiveCusId, cusUser.getUserId())
                .eq(FlatLive::getDelStatus, 0)
                .eq(FlatLive::getStatus, 2)
        );

        if(lease == null && live == null) {
            throw new ServiceException("未找到租赁或同住记录");
        }

        FlatRepairMake repairMake = new FlatRepairMake();
        BeanUtils.copyProperties(req, repairMake);
        repairMake.setFlatId(room.getFlatId());
        if(lease != null) {
            repairMake.setApplyCusType(1);
        } else {
            repairMake.setApplyCusType(2);
        }
        repairMake.setCusId(cusUser.getUserId());
        repairMake.setLeaseId(lease != null ? lease.getId() : live.getLeaseId());
        repairMake.setButlerId(room.getButlerId());
        repairMake.setPropertyId(room.getPropertyId());
        repairMake.setMakeTime(LocalDateTime.now());
        repairMake.setStatus(0);
        repairMake.setEvaluateFlag(0);
        repairMake.setCreateBy(cusUser.getNickName());
        repairMake.setCreateTime(LocalDateTime.now());
        repairMake.setDelStatus(0);
        repairMakeMapper.insert(repairMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(repairMake.getId());
        log.setType("make_repairs");
        log.setRefuse("预约报修成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        //消息通知
        messageService.cusToButler(
                cusUser.getUserId(), room.getButlerId(),
                cusUser.getNickName() + "预约报修",
                "/pages/butler/home/<USER>/info/index?id=" + repairMake.getId() + "&type=2"
        );

        if (StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
            weChatPublicMessageService.sendMessage(
                    maiUser,
                    "WKAfDyJDHOmCoyab9Hwx5WSaHpKoDAgKMaKYBPPzHdU",
                    "/pages/butler/home/<USER>/info/index?id=" + repairMake.getId() + "&type=2",
                    Map.of(
                            "thing15", Map.of("value", cusUser.getRealName()),
                            "thing8", Map.of("value", repairType.getName()),
                            "time12", Map.of("value", repairMake.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                            "thing10", Map.of("value", repairMake.getDescription()),
                            "thing4", Map.of("value", room.getName())
                    )
            );
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long repairMakeId, CusUser cusUser) {
        // 只能撤销待审核未支付的订单
        FlatRepairMake repairMake = repairMakeMapper.selectById(repairMakeId);
        if(repairMake == null) {
            throw new ServiceException("保修预约信息不存在");
        }

        if(repairMake.getStatus() == 1) {
            throw new ServiceException("保修预约已撤销");
        }

        if(repairMake.getStatus() != 0) {
            throw new ServiceException("保修预约已处理，无法撤销");
        }

        repairMake.setUpdateBy(cusUser.getNickName());
        repairMake.setStatus(1);
        repairMake.setCancelTime(LocalDateTime.now());
        repairMakeMapper.updateById(repairMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(cusUser.getUserId());
        log.setOperateName(cusUser.getNickName());
        log.setSourceId(repairMake.getId());
        log.setType("make_repairs");
        log.setRefuse("预约报修用户撤销成功");
        log.setDelStatus(0);
        log.setCreateBy(cusUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);
    }

    @Transactional(rollbackFor = Exception.class)
    public void verifyForButler(VerifyReq req, MaiUser maiUser) {
        if(!"BUTLER".equalsIgnoreCase(maiUser.getUserType())) {
            throw new ServiceException("只有管家用户才能审核");
        }

        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatRepairMake repairMake = repairMakeMapper.selectById(req.getId());
        if(repairMake == null) {
            throw new ServiceException("报修预约信息不存在");
        }

        if(repairMake.getStatus() == 1) {
            throw new ServiceException("报修预约已撤销");
        }

        if(repairMake.getStatus() != 0) {
            throw new ServiceException("报修预约管家已审核");
        }

        if(req.getStatus() == 1) {
            repairMake.setStatus(3);
        } else {
            repairMake.setStatus(2);
            repairMake.setRefuse(req.getRefuse());
        }
        repairMake.setButlerId(maiUser.getUserId());
        repairMake.setButlerVerifyTime(LocalDateTime.now());
        repairMake.setUpdateBy(maiUser.getNickName());
        repairMake.setUpdateTime(LocalDateTime.now());
        repairMakeMapper.updateById(repairMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getNickName());
        log.setSourceId(repairMake.getId());
        log.setType("make_repairs");
        log.setRefuse(req.getStatus() == 1 ? "报修预约管家审核通过" : "报修预约管家审核未通过");
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        if(req.getStatus() == 1) { // 审核通过，通知用户和物业
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    repairMake.getCusId(),
                    "报修预约管家审核通过",
                    "/pages/user/viewRecord/info/index?id=" + repairMake.getId() + "&type=2"
            );

            messageService.butlerToProperty(
                    maiUser.getUserId(),
                    repairMake.getPropertyId(),
                    "报修预约管家审核通过",
                    "/pages/property/info/index?id=" + repairMake.getId() + "&type=2"
            );
        } else { // 审核未通过，通知用户
            messageService.butlerToCus(
                    maiUser.getUserId(),
                    repairMake.getCusId(),
                    "报修预约管家审核未通过",
                    "/pages/user/viewRecord/info/index?id=" + repairMake.getId() + "&type=2"
            );
        }
    }

    public void verifyForProperty(RepairMakePropertyVerifyReq req, MaiUser maiUser) {
        if(!"PROPERTY".equalsIgnoreCase(maiUser.getUserType())) {
            throw new ServiceException("只有物业用户才能审核");
        }

        if(req.getStatus() == 2 && req.getRefuse() == null) {
            throw new ServiceException("拒绝原因不能为空");
        }

        FlatRepairMake repairMake = repairMakeMapper.selectById(req.getId());
        if(repairMake == null) {
            throw new ServiceException("报修预约信息不存在");
        }

        if(repairMake.getStatus() == 1) {
            throw new ServiceException("报修预约已撤销");
        }

        if(repairMake.getStatus() > 3) {
            throw new ServiceException("报修预约物业已审核");
        }

        if(repairMake.getStatus() != 3) {
            throw new ServiceException("报修预约管家未审核通过，物业无法审核");
        }

        if(req.getStatus() == 1) {
            repairMake.setStatus(5);
        } else {
            repairMake.setStatus(4);
            repairMake.setRefuse(req.getRefuse());
        }
        repairMake.setMaintainTime(req.getMaintainTime());
        repairMake.setMaintainUrl(req.getMaintainUrl());
        repairMake.setStaffId(req.getStaffId());
        repairMake.setPropertyId(maiUser.getUserId());
        repairMake.setPropertyVerifyTime(LocalDateTime.now());
        repairMake.setUpdateBy(maiUser.getNickName());
        repairMake.setUpdateTime(LocalDateTime.now());
        repairMakeMapper.updateById(repairMake);

        //记录操作流程
        FlatMakeLog log = new FlatMakeLog();
        log.setOperateId(maiUser.getUserId());
        log.setOperateName(maiUser.getNickName());
        log.setSourceId(repairMake.getId());
        log.setType("make_repairs");
        log.setRefuse(req.getStatus() == 1 ? "报修预约物业审核通过" : "报修预约物业审核未通过");
        log.setDelStatus(0);
        log.setCreateBy(maiUser.getNickName());
        log.setCreateTime(LocalDateTime.now());
        makeLogMapper.insert(log);

        if(req.getStatus() == 1) { // 审核通过，通知管家和用户
            messageService.propertyToCus(
                    maiUser.getUserId(),
                    repairMake.getCusId(),
                    "报修预约物业审核通过",
                    "/pages/user/viewRecord/info/index?id=" + repairMake.getId() + "&type=2"
            );

            messageService.propertyToButler(
                    maiUser.getUserId(),
                    repairMake.getButlerId(),
                    "报修预约物业审核通过",
                    "/pages/butler/home/<USER>/info/index?id=" + repairMake.getId() + "&type=2"
            );
        } else { // 审核未通过，通知用户和管家
            messageService.propertyToCus(
                    maiUser.getUserId(),
                    repairMake.getCusId(),
                    "报修预约物业审核未通过",
                    "/pages/user/viewRecord/info/index?id=" + repairMake.getId() + "&type=2"
            );

            messageService.propertyToButler(
                    maiUser.getUserId(),
                    repairMake.getButlerId(),
                    "报修预约物业审核未通过",
                    "/pages/butler/home/<USER>/info/index?id=" + repairMake.getId() + "&type=2"
            );
        }

    }

    public TablePage<RepairMakeListResp> queryRespPage(RepairMakeQueryReq req) {
        String icon = sysConfigService.queryConfigByKey("REPAIRS_ICON");
        
        return PageUtils.paginate(
                () -> repairMakeMapper.selectRespList(req),
                (e) -> {
                    e.setIcon(icon);
                    e.setTitle("预约报修");
                    return e;
                }
        );
    }

    
    public RepairMakeDetailResp queryRespDetail(Long id) {
        RepairMakeListResp listResp = repairMakeMapper.selectRespById(id);
        if(listResp == null) {
            throw new RuntimeException("报修记录不存在");
        }

        RepairMakeDetailResp detailResp = new RepairMakeDetailResp();
        BeanUtils.copyProperties(listResp, detailResp);

        String icon = sysConfigService.queryConfigByKey("REPAIRS_ICON");
        listResp.setIcon(icon);
        listResp.setTitle("预约报修");

        if(detailResp.getEvaluateFlag() == 1) {
            FlatRepairEvaluate evaluate = repairEvaluateMapper.selectFirst(q -> q
                    .eq(FlatRepairEvaluate::getRepairMakeId, id)
                    .eq(FlatRepairEvaluate::getDelStatus, 0)
            );
            detailResp.setEvaluate(evaluate);
        }

        List<FlatMakeLog> logs = makeLogMapper.selectList(q -> q
                .eq(FlatMakeLog::getSourceId, detailResp.getId())
                .eq(FlatMakeLog::getType, "make_repairs")
                .eq(FlatMakeLog::getDelStatus, 0)
                .orderByAsc(FlatMakeLog::getCreateTime)
        );
        detailResp.setLogs(logs);

        return detailResp;
    }

    
//    public List<FlatRepairMake> selectFlatRepairsToday(StatisticsReq request) {
//        return flatRepairsMapper.selectFlatRepairsToday(request);
//    }

    public void remove(Long id, SysUser user) {
        repairMakeMapper.update(q -> q
                .set(FlatRepairMake::getDelStatus, 1)
                .set(FlatRepairMake::getUpdateTime, LocalDateTime.now())
                .set(FlatRepairMake::getUpdateBy, user.getUsername())
                .eq(FlatRepairMake::getId, id)
                .eq(FlatRepairMake::getDelStatus, 0)
        );
    }

    
}
