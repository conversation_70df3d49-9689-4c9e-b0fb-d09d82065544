package com.flat.logic.service.flat;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.flat.RoomLabelAddReq;
import com.flat.logic.dto.req.flat.RoomLabelEditReq;
import com.flat.logic.dto.req.flat.RoomLabelQueryReq;
import com.flat.logic.entity.flat.FlatRoomLabel;
import com.flat.logic.mapper.flat.FlatRoomLabelMapper;
import com.flat.system.entity.SysUser;

import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 房间标签服务层处理
 */
@Service
public class RoomLabelService {

    @Resource
    private FlatRoomLabelMapper roomLabelMapper;

    /**
     * 添加房间标签
     *
     * @param req 添加请求
     * @return 添加的标签ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(RoomLabelAddReq req, SysUser user) {
        FlatRoomLabel roomLabel = new FlatRoomLabel();
        BeanUtils.copyProperties(req, roomLabel);
        roomLabel.setCreateBy(user.getUsername());
        roomLabel.setCreateTime(LocalDateTime.now());
        roomLabelMapper.insert(roomLabel);
    }

    /**
     * 编辑房间标签
     *
     * @param req 编辑请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void edit(RoomLabelEditReq req, SysUser user) {
        FlatRoomLabel roomLabel = Optional.ofNullable(roomLabelMapper.selectById(req.getId()))
                .orElseThrow(() -> new ServiceException("房间标签不存在"));

        BeanUtils.copyProperties(req, roomLabel);
        roomLabel.setUpdateBy(user.getUsername());
        roomLabel.setUpdateTime(LocalDateTime.now());
        roomLabelMapper.updateById(roomLabel);
    }

    /**
     * 分页查询房间标签
     *
     * @param req 查询请求
     * @return 分页结果
     */
    public TablePage<FlatRoomLabel> queryPage(RoomLabelQueryReq req) {
        LambdaQueryWrapper<FlatRoomLabel> queryWrapper = new LambdaQueryWrapper<>();
        
        Optional.ofNullable(req.getType()).ifPresent(type -> queryWrapper.eq(FlatRoomLabel::getType, type));
        Optional.ofNullable(req.getName()).ifPresent(name -> queryWrapper.like(FlatRoomLabel::getName, name));

        return PageUtils.paginate(() -> roomLabelMapper.selectList(queryWrapper));
    }

    public List<FlatRoomLabel> queryList(RoomLabelQueryReq req) {
        LambdaQueryWrapper<FlatRoomLabel> queryWrapper = new LambdaQueryWrapper<>();
        
        Optional.ofNullable(req.getType()).ifPresent(type -> queryWrapper.eq(FlatRoomLabel::getType, type));
        Optional.ofNullable(req.getName()).ifPresent(name -> queryWrapper.like(FlatRoomLabel::getName, name));

        return roomLabelMapper.selectList(queryWrapper);
    }

    /**
     * 查询房间标签详情
     *
     * @param id 标签ID
     * @return 标签详情
     */
    public FlatRoomLabel queryDetail(Long id) {
        return Optional.ofNullable(roomLabelMapper.selectById(id))
                .orElseThrow(() -> new ServiceException("房间标签不存在"));
    }

    public void remove(Long id, SysUser user) {
        FlatRoomLabel roomLabel = roomLabelMapper.selectById(id);
        if (roomLabel == null || roomLabel.getDelStatus() == 1) {
            throw new ServiceException("房间标签不存在");
        }
        roomLabel.setUpdateBy(user.getUsername());
        roomLabel.setUpdateTime(LocalDateTime.now());
        roomLabel.setDelStatus(1);
        roomLabelMapper.updateById(roomLabel);
    }
}
