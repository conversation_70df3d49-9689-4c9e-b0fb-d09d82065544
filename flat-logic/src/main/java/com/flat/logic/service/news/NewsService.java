package com.flat.logic.service.news;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.dto.req.news.NewsAddReq;
import com.flat.logic.dto.req.news.NewsEditReq;
import com.flat.logic.dto.req.news.NewsQueryReq;
import com.flat.logic.dto.resp.news.NewsResp;
import com.flat.logic.entity.activity.Activity;
import com.flat.logic.entity.news.News;
import com.flat.logic.entity.news.NewsCategory;
import com.flat.logic.mapper.activity.ActivityMapper;
import com.flat.logic.mapper.news.NewsCategoryMapper;
import com.flat.logic.mapper.news.NewsMapper;
import com.flat.system.entity.SysUser;
import com.github.pagehelper.PageInfo;

import jakarta.annotation.Resource;

/**
 * 新闻条目服务
 */
@Service
public class NewsService {

    @Resource
    private NewsMapper newsMapper;

    @Resource
    private NewsCategoryMapper newsCategoryMapper;

    @Resource
    private ActivityMapper activityMapper;

    /**
     * 添加新闻条目
     */
    public void add(NewsAddReq req, SysUser sysUser) {
        // 检查分类是否存在
        NewsCategory category = newsCategoryMapper.selectById(req.getCategoryId());
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("新闻分类不存在");
        }

        // 根据内容类型校验对应字段
        validateContentFields(req.getContentType(), req.getContent(), req.getActivityId(), req.getOutUrl());

        // 校验视频号相关字段
        validateVideoAccountFields(req.getVideoAccountFlag(), req.getVideoAccountId());

        // 创建新闻条目对象
        News news = new News();
        BeanUtils.copyProperties(req, news);

        // 设置创建者信息
        news.setCreateBy(sysUser.getUsername());
        news.setCreateTime(LocalDateTime.now());
        news.setDelStatus(0);

        // 保存到数据库
        newsMapper.insert(news);
    }

    /**
     * 编辑新闻条目
     */
    public void edit(NewsEditReq req, SysUser sysUser) {
        // 检查新闻是否存在
        News news = newsMapper.selectById(req.getId());
        if (news == null || news.getDelStatus() == 1) {
            throw new ServiceException("新闻条目不存在");
        }

        // 检查分类是否存在
        NewsCategory category = newsCategoryMapper.selectById(req.getCategoryId());
        if (category == null || category.getDelStatus() == 1) {
            throw new ServiceException("新闻分类不存在");
        }

        // 根据内容类型校验对应字段
        validateContentFields(req.getContentType(), req.getContent(), req.getActivityId(), req.getOutUrl());

        // 校验视频号相关字段
        validateVideoAccountFields(req.getVideoAccountFlag(), req.getVideoAccountId());

        // 更新新闻信息
        BeanUtils.copyProperties(req, news);
        news.setUpdateBy(sysUser.getUsername());
        news.setUpdateTime(LocalDateTime.now());
        // 保存到数据库
        newsMapper.updateById(news);
    }

    /**
     * 删除新闻条目
     */
    public void remove(Long id, SysUser sysUser) {
        // 检查新闻是否存在
        News news = newsMapper.selectById(id);
        if (news == null || news.getDelStatus() == 1) {
            throw new ServiceException("新闻条目不存在");
        }

        // 逻辑删除
        news.setDelStatus(1);
        news.setUpdateBy(sysUser.getUsername());

        // 保存到数据库
        newsMapper.updateById(news);
    }

    /**
     * 查询新闻条目详情
     */
    public NewsResp queryDetail(Long id) {
        // 查询新闻
        News news = newsMapper.selectById(id);
        if (news == null || news.getDelStatus() == 1) {
            throw new ServiceException("新闻条目不存在");
        }

        // 转换为响应对象
        NewsResp resp = new NewsResp();
        BeanUtils.copyProperties(news, resp);

        // 设置分类名称
        NewsCategory category = newsCategoryMapper.selectById(news.getCategoryId());
        if (category != null) {
            resp.setCategoryName(category.getName());
        }

        // 如果内容类型为活动，查询活动名称并设置
        if (news.getContentType() == 1) {
            Activity activity = activityMapper.selectById(news.getActivityId());
            if (activity != null) {
                resp.setActivityName(activity.getName());
            }
        }

        return resp;
    }

    /**
     * 查询新闻条目列表
     */
    public List<NewsResp> queryList(NewsQueryReq req) {
        // 查询新闻列表
        List<News> list = newsMapper.selectList(q -> q
                .like(req.getTitle() != null, News::getTitle, req.getTitle())
                .eq(req.getCategoryId() != null, News::getCategoryId, req.getCategoryId())
                .eq(req.getContentType() != null, News::getContentType, req.getContentType())
                .eq(req.getShowFlag() != null, News::getShowFlag, req.getShowFlag())
                .eq(req.getVideoAccountFlag() != null, News::getVideoAccountFlag, req.getVideoAccountFlag())
                .eq(News::getDelStatus, 0)
                .orderByDesc(News::getCreateTime)
        );

        // 转换为响应对象
        return list.stream().map(this::convertToResp).toList();
    }

    /**
     * 分页查询新闻条目
     */
    public TablePage<NewsResp> queryPage(NewsQueryReq req) {
        // 分页查询
        List<News> list = PageUtils.execute(() -> newsMapper.selectList(q -> q
                .like(req.getTitle() != null, News::getTitle, req.getTitle())
                .eq(req.getCategoryId() != null, News::getCategoryId, req.getCategoryId())
                .eq(req.getContentType() != null, News::getContentType, req.getContentType())
                .eq(req.getShowFlag() != null, News::getShowFlag, req.getShowFlag())
                .eq(req.getVideoAccountFlag() != null, News::getVideoAccountFlag, req.getVideoAccountFlag())
                .eq(News::getDelStatus, 0)
                .orderByDesc(News::getCreateTime)
        ));

        // 转换为响应对象
        List<NewsResp> respList = list.stream().map(this::convertToResp).toList();

        // 创建分页结果
        return TablePage.from(new PageInfo<>(list), respList);
    }

    /**
     * 转换为响应对象
     */
    private NewsResp convertToResp(News news) {
        NewsResp resp = new NewsResp();
        BeanUtils.copyProperties(news, resp);

        // 设置分类名称
        NewsCategory category = newsCategoryMapper.selectById(news.getCategoryId());
        if (category != null) {
            resp.setCategoryName(category.getName());
        }

        // 如果内容类型为活动，查询活动名称并设置
        if (news.getContentType() == 1) {
            Activity activity = activityMapper.selectById(news.getActivityId());
            if (activity != null) {
                resp.setActivityName(activity.getName());
            }
        }

        return resp;
    }

    /**
     * 根据内容类型验证对应字段
     */
    private void validateContentFields(Integer contentType, String content, Long activityId, String outUrl) {
        if (contentType == 0) {
            // 图文内容
            if (content == null || content.isBlank()) {
                throw new ServiceException("图文内容不能为空");
            }
        } else if (contentType == 1) {
            // 指定活动
            if (activityId == null) {
                throw new ServiceException("活动ID不能为空");
            }
            // 可以在此处添加活动存在性校验
            Activity activity = activityMapper.selectById(activityId);
            if (activity == null || activity.getDelStatus() == 1) {
                throw new ServiceException("活动不存在");
            }
        } else if (contentType == 2) {
            // 外部链接
            if (outUrl == null || outUrl.isBlank()) {
                throw new ServiceException("外部链接不能为空");
            }
        }
    }

    /**
     * 验证视频号相关字段
     */
    private void validateVideoAccountFields(Integer videoAccountFlag, String videoAccountId) {
        if (videoAccountFlag == 1) {
            // 当选择插入视频号时，验证视频号ID
            if (videoAccountId == null || videoAccountId.isBlank()) {
                throw new ServiceException("视频号ID不能为空");
            }
        }
    }
}
