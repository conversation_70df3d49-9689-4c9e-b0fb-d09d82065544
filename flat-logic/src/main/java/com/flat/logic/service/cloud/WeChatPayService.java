package com.flat.logic.service.cloud;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.exception.ServiceException;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderCloseResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WeChatPayService {

    @Value("${wx.pay.appId}")
    private String payAppId;

    @Value("${wx.pay.mchId}")
    private String payMchId;

    @Value("${wx.pay.mchKey}")
    private String payMchKey;

    @Value("${wx.pay.keyPath}")
    private String payKeyPath;

    @Value("${wx.pay.notifyUrl}")
    private String payNotifyUrl;

    @Resource
    private WeChatMiniProgramService weChatMiniProgramService;

    public String queryPhone(String code) {
        try {
            String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + weChatMiniProgramService.queryAccessToken();
            String result = HttpUtil.post(url, JSON.toJSONString(MapUtil.of("code", code)));

            log.warn("手机号查询返回结果，code: {}, result: {}", code, result);

            JSONObject jsonObject = JSON.parseObject(result);
            JSONObject info = jsonObject.getJSONObject("phone_info");
            return info.getString("phoneNumber");
        } catch (Exception e) {
            throw new ServiceException("获取手机号失败", e);
        }
    }

    private WxPayService createWxPayService(String subAppId, String subMchId) {
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(payAppId);
        payConfig.setMchId(payMchId);
        payConfig.setMchKey(payMchKey);
        payConfig.setSubAppId(subAppId);
        payConfig.setSubMchId(subMchId);
        payConfig.setKeyPath(StringUtils.trimToNull(payKeyPath));

        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }

    public WxPayOrderCloseResult closeOrder(String subAppId, String subMchId, String outTradeNo) throws WxPayException {
        WxPayService wxPayService = createWxPayService(subAppId, subMchId);
        return wxPayService.closeOrder(outTradeNo);
    }

    public WxPayMpOrderResult createOrder(String payTarget, String subAppId, String subMchId, String subOpenId, String body, String orderNo, String outTradeNo, Integer fee) throws WxPayException {
        WxPayService wxPayService = createWxPayService(subAppId, subMchId);

        WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
        request.setBody(body);
        request.setOutTradeNo(outTradeNo);
        request.setTotalFee(fee);
        request.setTradeType("JSAPI");
        request.setSubOpenid(subOpenId);
        request.setNotifyUrl(payNotifyUrl);
        request.setSpbillCreateIp("127.0.0.1");
        request.setAttach(payTarget + "/" + orderNo);
        return wxPayService.createOrder(request);
    }

    public WxPayOrderNotifyResult parseOrderNotifyResult(String subAppId, String subMchId, String xmlData) throws WxPayException {
        WxPayService wxPayService = createWxPayService(subAppId, subMchId);
        return wxPayService.parseOrderNotifyResult(xmlData);
    }

    public WxPayRefundResult refund(
            String subAppId, String subMchId,
            String outTradeNo, String outRefundNo,
            Integer totalFee, Integer refundFee
    ) throws WxPayException {
        WxPayService wxPayService = createWxPayService(subAppId, subMchId);

        WxPayRefundRequest refundRequest = new WxPayRefundRequest();
        refundRequest.setOutRefundNo(outRefundNo);
        refundRequest.setOutTradeNo(outTradeNo);
        refundRequest.setTotalFee(totalFee);
        refundRequest.setRefundFee(refundFee);
        return wxPayService.refund(refundRequest);
    }

}
