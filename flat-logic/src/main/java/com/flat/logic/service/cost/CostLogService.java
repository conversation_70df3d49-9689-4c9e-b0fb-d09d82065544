package com.flat.logic.service.cost;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.flat.logic.entity.account.MaiUser;
import com.flat.logic.mapper.account.MaiUserMapper;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import com.flat.system.service.SysConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.flat.common.core.page.TablePage;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.PageUtils;
import com.flat.logic.commons.BillConstants;
import com.flat.logic.dto.req.cost.CostLogAddReq;
import com.flat.logic.dto.req.cost.CostLogPayReq;
import com.flat.logic.dto.req.cost.CostLogQueryReq;
import com.flat.logic.dto.resp.cost.CostLogPayResp;
import com.flat.logic.dto.resp.cost.CostLogResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.cost.FlatCostDelivery;
import com.flat.logic.entity.cost.FlatCostLog;
import com.flat.logic.entity.cost.FlatCostType;
import com.flat.logic.entity.cost.FlatCostTypeDelivery;
import com.flat.logic.entity.coupon.Coupon;
import com.flat.logic.entity.finance.FlatBill;
import com.flat.logic.entity.finance.FlatPayLog;
import com.flat.logic.entity.flat.FlatCompany;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.cost.FlatCostDeliveryMapper;
import com.flat.logic.mapper.cost.FlatCostLogMapper;
import com.flat.logic.mapper.cost.FlatCostTypeDeliveryMapper;
import com.flat.logic.mapper.cost.FlatCostTypeMapper;
import com.flat.logic.mapper.coupon.CouponMapper;
import com.flat.logic.mapper.finance.FlatBillMapper;
import com.flat.logic.mapper.finance.FlatPayLogMapper;
import com.flat.logic.mapper.flat.FlatCompanyMapper;
import com.flat.logic.service.cloud.WeChatPayService;
import com.flat.logic.service.finance.BillService;
import com.flat.system.entity.SysUser;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.Resource;

@Service
public class CostLogService {
    
    @Resource
    private FlatCostLogMapper costLogMapper;

    @Resource
    private FlatCostTypeMapper costTypeMapper;

    @Resource
    private FlatBillMapper billMapper;

    @Resource
    private FlatCompanyMapper companyMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private WeChatPayService weChatPayService;

    @Resource
    private BillService billService;

    @Resource
    private FlatPayLogMapper payLogMapper;

    @Resource
    private FlatCostDeliveryMapper costDeliveryMapper;

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private FlatCostTypeDeliveryMapper costTypeDeliveryMapper;

    @Resource
    private MaiUserMapper maiUserMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    @Resource
    private SysConfigService sysConfigService;

    public FlatCostLog add(CostLogAddReq req, CusUser cusUser) {
        FlatCostType costType = costTypeMapper.selectById(req.getCostTypeId());
        if (costType == null || costType.getDelStatus() == 1) {
            throw new ServiceException("费用类型不存在");
        }

        // 判断当前时间是否在购买时间范围内
        if(costType.getBuyBeginTime() != null && LocalTime.now().isBefore(costType.getBuyBeginTime())) {
            throw new ServiceException("当前时间不在购买时间范围内");
        }
        if(costType.getBuyEndTime() != null && LocalTime.now().isAfter(costType.getBuyEndTime())) {
            throw new ServiceException("当前时间不在购买时间范围内");
        }

        // 如果costType中use_delivery_flag为1，则需要判断定点取货条目ID是否存在
        if(costType.getUseDeliveryFlag() == 1) {
            if(req.getDeliveryId() == null) {
                throw new ServiceException("定点取货条目ID不能为空");
            }
            FlatCostDelivery delivery = costDeliveryMapper.selectById(req.getDeliveryId());
            if(delivery == null || delivery.getDelStatus() == 1) {
                throw new ServiceException("定点取货条目不存在");
            }

            // 判断定点取货条目ID是否在费用类型关联的定点取货条目中
            FlatCostTypeDelivery costTypeDelivery = costTypeDeliveryMapper.selectFirst(q -> q
                    .eq(FlatCostTypeDelivery::getCostTypeId, costType.getId())
                    .eq(FlatCostTypeDelivery::getDeliveryId, req.getDeliveryId())
                    .eq(FlatCostTypeDelivery::getDelStatus, 0)
            );
            if(costTypeDelivery == null) {
                throw new ServiceException("定点取货条目与费用类型不匹配");
            }
        }

        // 添加费用记录
        FlatCostLog costLog = new FlatCostLog();
        costLog.setCusId(cusUser.getUserId());
        costLog.setCostTypeId(req.getCostTypeId());
        costLog.setPrice(costType.getPrice());
        costLog.setCount(req.getCount());
        costLog.setMoney(costType.getPrice().multiply(BigDecimal.valueOf(req.getCount())));
        costLog.setPayStatus(0);
        costLog.setDeliveryId(req.getDeliveryId());
        costLog.setCreateBy(cusUser.getRealName());
        costLog.setCreateTime(LocalDateTime.now());
        costLog.setDelStatus(0);
        costLogMapper.insert(costLog);

        return costLog;
    }

    public CostLogResp queryDetail(Long id) {
        CostLogResp costLog = costLogMapper.selectRespById(id);
        if (costLog == null || costLog.getDelStatus() == 1) {
            throw new ServiceException("费用记录不存在");
        }
        return costLog;
    }

    public List<CostLogResp> queryList(CostLogQueryReq req) {
        return costLogMapper.selectRespList(req);
    }

    // 分页查询记录
    public TablePage<CostLogResp> queryPage(CostLogQueryReq req) {
        return PageUtils.paginate(() -> costLogMapper.selectRespList(req));
    }

    // 删除记录
    public void remove(Long costLogId, SysUser sysUser) {
        FlatCostLog costLog = costLogMapper.selectById(costLogId);
        if (costLog == null || costLog.getDelStatus() == 1) {
            throw new ServiceException("费用记录不存在");
        }

        if(costLog.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿删除");
        }

        costLogMapper.update(q -> q
                .set(FlatCostLog::getDelStatus, 1)
                .set(FlatCostLog::getUpdateTime, LocalDateTime.now())
                .set(FlatCostLog::getUpdateBy, sysUser.getUsername())
                .eq(FlatCostLog::getId, costLogId)
                .eq(FlatCostLog::getDelStatus, 0)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public CostLogPayResp pay(CostLogPayReq req, CusUser cusUser) {
        FlatCostLog costLog = costLogMapper.selectById(req.getCostLogId());
        if(costLog == null) {
            throw new ServiceException("费用记录不存在");
        }

        if(costLog.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        FlatCostType costType = costTypeMapper.selectById(costLog.getCostTypeId());
        if (costType == null || costType.getDelStatus() == 1) {
            throw new ServiceException("费用类型不存在");
        }

        FlatCompany company = companyMapper.selectBySymbol("by");
        if (company == null) {
            throw new ServiceException("收款企业不存在");
        }

        // 优惠券处理
        BigDecimal discountAmount = BigDecimal.ZERO;
        Coupon coupon = null;
        if (req.getCouponId() != null) {
            coupon = couponMapper.selectById(req.getCouponId());
            if (coupon == null || coupon.getDelStatus() == 1) {
                throw new ServiceException("优惠券不存在");
            }
            
            if (!Objects.equals(coupon.getCusId(), cusUser.getUserId())) {
                throw new ServiceException("不能使用他人的优惠券");
            }

            // 判断是否审核
            if (coupon.getVerifyStatus() != 3) {
                throw new ServiceException("优惠券未审核通过");
            }
            
            if (coupon.getTarget() != 2) {
                throw new ServiceException("只能使用自定义费用券");
            }

            if (coupon.getCostTypeId() != null && !Objects.equals(coupon.getCostTypeId(), costType.getId())) {
                throw new ServiceException("优惠券不适用于该费用类型");
            }
            
            if (coupon.getUseStatus() == 1) {
                throw new ServiceException("优惠券已使用");
            }
            
            if (coupon.getValidBeginTime().isAfter(LocalDateTime.now())) {
                throw new ServiceException("优惠券未开始");
            }
            
            if (coupon.getValidEndTime().isBefore(LocalDateTime.now())) {
                throw new ServiceException("优惠券已过期");
            }
            
            BigDecimal costLogMoney = costLog.getMoney();
            BigDecimal couponAmount = new BigDecimal(coupon.getAmount());
            
            if (couponAmount.compareTo(costLogMoney) > 0) {
                throw new ServiceException("优惠券额度不能大于费用金额");
            }
            
            discountAmount = couponAmount;
        }

        // 查找账单
        FlatBill bill = billMapper.selectFirst(q -> q
                .eq(FlatBill::getCostLogId, costLog.getId())
                .eq(FlatBill::getPayTarget, BillConstants.COST)
                .eq(FlatBill::getCusId, cusUser.getUserId())
                .eq(FlatBill::getDelStatus, 0)
        );
        
        BigDecimal costLogMoney = costLog.getMoney();
        BigDecimal actualMoney = costLogMoney.subtract(discountAmount);
        
        if(bill == null) {
            String outTradeNo = "COST" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);
            bill = new FlatBill();
            bill.setOrderNo(outTradeNo);
            bill.setMoney(actualMoney);
            bill.setPayChannel(1);
            bill.setCusId(costLog.getCusId());
            bill.setPayTarget(BillConstants.COST);
            bill.setReadStatus(0);
            bill.setPayMode(0);
            bill.setPayStatus(0);
            bill.setInvoiceStatus(0);
            bill.setCostLogId(costLog.getId());
            bill.setCreateBy(cusUser.getNickName());
            bill.setCreateTime(LocalDateTime.now());
            bill.setUseCouponFlag(0);
            bill.setDelStatus(0);
            bill.setName(costType.getName());
            bill.setReceiveCompanyId(company.getId());
            billMapper.insert(bill);

            // 更新费用账单
            costLog.setBillId(bill.getId());
            costLog.setUpdateTime(LocalDateTime.now());
            costLog.setUpdateBy(cusUser.getNickName());
            costLogMapper.updateById(costLog);
        } else {
            if(bill.getPayStatus() == 1) {
                throw new ServiceException("账单已支付，请勿重复支付");
            }
            // 更新账单金额
            bill.setMoney(actualMoney);
            billMapper.updateById(bill);
        }
        
        // 如果使用了优惠券且优惠券金额能够完全抵扣费用
        if (coupon != null && discountAmount.compareTo(costLogMoney) >= 0) {
            // 更新优惠券状态
            coupon.setUseStatus(1);
            coupon.setUseTime(LocalDateTime.now());
            coupon.setUseBillId(bill.getId());
            coupon.setUpdateBy(cusUser.getNickName());
            coupon.setUpdateTime(LocalDateTime.now());
            couponMapper.updateById(coupon);
            
            // 更新账单状态为已支付
            bill.setPayStatus(1);
            bill.setUseCouponFlag(1);
            bill.setCouponId(coupon.getId());
            bill.setPayTime(LocalDateTime.now());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);
            
            // 更新费用记录状态为已支付
            costLog.setPayStatus(1);
            costLog.setPayMode(2); // 2表示优惠券支付
            costLog.setUpdateTime(LocalDateTime.now());
            costLogMapper.updateById(costLog);
            
            // 返回不需要支付的响应
            return CostLogPayResp.noPay();
        } else if (coupon != null) {
            bill.setUseCouponFlag(1);
            bill.setCouponId(coupon.getId());
            bill.setUpdateTime(LocalDateTime.now());
            billMapper.updateById(bill);

            // 如果使用了优惠券但不能完全抵扣，先更新优惠券状态
            coupon.setUseStatus(1);
            coupon.setUseTime(LocalDateTime.now());
            coupon.setUseBillId(bill.getId());
            coupon.setUpdateBy(cusUser.getNickName());
            coupon.setUpdateTime(LocalDateTime.now());
            couponMapper.updateById(coupon);
        }
        
        // 需要调用微信支付
        WxPayMpOrderResult wxPayResult = billService.pay(bill, company, cusUser);
        return CostLogPayResp.needPay(wxPayResult);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlePay(String outTradeNo, String xmlData) throws WxPayException {
        //根据outTradeNo查询账单
        FlatPayLog payLog = payLogMapper.selectFirst(q -> q
                .eq(FlatPayLog::getOutTradeNo, outTradeNo)
                .eq(FlatPayLog::getChannel, 0)
                .eq(FlatPayLog::getDelStatus, 0)
        );
        if(payLog == null) {
            throw new ServiceException("渠道支付记录未找到");
        }

        FlatBill bill = billMapper.selectById(payLog.getBillId());
        if (bill == null || bill.getDelStatus() == 1) {
            throw new ServiceException("账单未找到");
        }

        if(bill.getPayStatus() == 1) {
            throw new ServiceException("账单已支付，请勿重复支付");
        }

        FlatCostLog costLog = costLogMapper.selectById(bill.getCostLogId());
        if (costLog == null || costLog.getDelStatus() == 1) {
            throw new ServiceException("费用记录未找到");
        }

        FlatCostType costType = costTypeMapper.selectById(costLog.getCostTypeId());
        if (costType == null || costType.getDelStatus() == 1) {
            throw new ServiceException("费用类型未找到");
        }

        FlatCompany company = companyMapper.selectById(bill.getReceiveCompanyId());
        if (company == null || company.getDelStatus() == 1) {
            throw new ServiceException("企业未找到");
        }

        CusUser cusUser = cusUserMapper.selectById(costLog.getCusId());
        if (cusUser == null || cusUser.getDelStatus() == 1) {
            throw new ServiceException("用户未找到");
        }

        WxPayOrderNotifyResult result = weChatPayService.parseOrderNotifyResult(company.getWxPayAppId(), company.getWxPayMchId(), xmlData);
        if(!"SUCCESS".equalsIgnoreCase(result.getResultCode())) {
            throw new ServiceException("支付失败");
        }

        payLog.setPayStatus(1);
        payLog.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        payLog.setTransactionNo(result.getTransactionId());
        payLog.setUpdateTime(LocalDateTime.now());
        payLogMapper.updateById(payLog);

        bill.setPayStatus(1);
        bill.setPayTime(LocalDateTimeUtil.parse(result.getTimeEnd(), "yyyyMMddHHmmss"));
        bill.setTransactionNo(result.getTransactionId());
        bill.setUpdateTime(LocalDateTime.now());
        billMapper.updateById(bill);

        costLog.setPayStatus(1);
        costLog.setPayMode(0);
        costLog.setUpdateTime(LocalDateTime.now());
        costLogMapper.updateById(costLog);

        String butlerIdStr = sysConfigService.queryConfigByKey("message_notify_butler");
        if (StringUtils.isNotBlank(butlerIdStr)) {
            Long butlerId = Long.parseLong(butlerIdStr);

            MaiUser maiUser = maiUserMapper.selectById(butlerId);
            if(maiUser != null && maiUser.getDelStatus() == 0 && StringUtils.isNotBlank(maiUser.getWxPublicOpenId())) {
                weChatPublicMessageService.sendMessage(
                        maiUser,
                        "cAq85XSX6f_rdjvcoC1sZVB8ls7isKDkBRECICtxGN0",
                        null,
                        Map.of(
                                "thing10", Map.of("value", "--"),
                                "thing11", Map.of("value", cusUser.getRealName()),
                                "amount6", Map.of("value", bill.getMoney().toString()),
                                "thing14", Map.of("value", costType.getName() + "费用支付"),
                                "time5", Map.of("value", bill.getPayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        )
                );
            }
        }
    }
}
