package com.flat.logic.service.cloud;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.exception.ServiceException;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WeChatPublicService {

    @Value("${wx.public.appId}")
    private String appId;

    @Value("${wx.public.appSecret}")
    private String appSecret;

    public JSONObject auth(String code) {
        Map<String, Object> param = new HashMap<>(10);
        param.put("appid", appId);
        param.put("secret", appSecret);
        param.put("code", code);
        param.put("grant_type", "authorization_code");

        JSONObject jsonObject;
        try {
            String wxResult = HttpUtil.get("https://api.weixin.qq.com/sns/oauth2/access_token", param);
            jsonObject = JSON.parseObject(wxResult);

            log.info("获取openId结果: {}, 参数: code={}", wxResult, code);
        } catch (Exception e) {
            throw new ServiceException("获取openId失败", e);
        }

        if (jsonObject == null) {
            throw new ServiceException("获取openId失败");
        }

        return jsonObject;
    }


}
