package com.flat.logic.utils;

public class TransactionUtils {

    public int getIntTransaction(String type) {
        switch (type) {
            case "待付款":
                return 0;
            case "交易中":
                return 1;
            case "交易失败":
                return 2;
            case "交易成功":
                return 3;
            default:
                return 0;
        }
    }

    public String getStringTransaction(int type) {
        switch (type) {
            case 0:
                return "待付款";
            case 1:
                return "交易中";
            case 2:
                return "交易失败";
            case 3:
                return "交易成功";
            default:
                return "待付款";
        }
    }
}
