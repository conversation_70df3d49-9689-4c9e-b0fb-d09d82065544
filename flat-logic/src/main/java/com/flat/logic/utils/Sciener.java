package com.flat.logic.utils;

import com.alibaba.fastjson2.JSONObject;
import com.flat.common.exception.ServiceException;
import com.flat.common.utils.DateUtils;
import com.flat.common.utils.uuid.IdUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class Sciener {

    public static final Map<String, Object> objectMap = new HashMap();
    public static final String client_id = "9db2467153d949b39edf3cebb9e9b8e0";
    public static final String client_secret = "5a00dcfec8c4dc09745c52cb872de9d6";
   /* public static final String username = "bhdih_c042f4db68f23406c6cecf84a7ebb0fe";
    public static final String password = "556820e005745e86ac902ddda5d6c2b8";*/

    {
        getDoorToken();
    }

    public void getDoorToken() {
        //进入先加载token
        Map<String, Object> map = new HashMap<>();
        map.put("client_id", "9db2467153d949b39edf3cebb9e9b8e0");
        map.put("client_secret", "5a00dcfec8c4dc09745c52cb872de9d6");
       // map.put("username", "bhdih_18304306252");
        map.put("username", "18304306252");
        map.put("password", "2bf31dcfbaa6db10063f4be108ca2261");
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        String url = "https://api.sciener.com/oauth2/token";
        try {
            String result = HttpUtil.httpPostRequest(url, headers, map);
            JSONObject json = (JSONObject) JSONObject.parse(result);
            objectMap.put("sciener_access_token", json.get("access_token"));
            objectMap.put("sciener_refresh_token", json.get("refresh_token"));
            objectMap.put("sciener_uid", json.get("uid"));
            objectMap.put("sciener_expires_in", json.get("expires_in"));
            objectMap.put("sciener_openid", json.get("openid"));
            objectMap.put("sciener_scope", json.get("scope"));
            objectMap.put("sciener_token_type", json.get("token_type"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加密码
     * <p>
     * 2 参数说明
     * 名称	类型	是否必需	描述
     * clientId	String	Y	创建应用分配的client_id
     * accessToken	String	Y	访问令牌，通过获取访问令牌接口获取
     * lockId	Int	Y	锁ID，由锁初始化接口生成
     * keyboardPwd	Int	Y	密码
     * keyboardPwdName	String	N	密码名称
     * startDate	Long	Y	有效期开始时间（时间戳，单位毫秒）
     * endDate	Long	Y	有效期开始时间（时间戳，单位毫秒）
     * addType	Int	N	添加方式:
     * 1-通过APP蓝牙添加，不传则默认1，必需先通过APP蓝牙添加后再调用该接口
     * 2-通过网关远程添加，如果锁有连接网关，则可以传2，直接调用该接口添加到锁里
     * 3-NB-IoT，如果是NB-IoT锁，则可以传3，通过NB网络写入，需要等NB唤醒才会下发添加指令。
     * date	Long	Y	当前时间（时间戳，单位毫秒）
     */
    public static JSONObject add(Integer lockId, String keyboardPwd, Date startDate, Date endDate) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientId", client_id);

        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            //次方法获取键值对的名称
            String key = entry.getKey();
            //次方法获取键值对的值
            Object value = entry.getValue();
            if ("sciener_access_token".equals(key)) {
                map.put("accessToken", value);
            }
        }

        map.put("lockId", lockId);
        map.put("addType", 3);
        map.put("keyboardPwd", keyboardPwd);
        map.put("startDate", DateUtils.getTimeInMillis(startDate));
        map.put("endDate", DateUtils.getTimeInMillis(endDate));
        map.put("date", System.currentTimeMillis());

        String url = "https://api.sciener.com/v3/keyboardPwd/add";
        JSONObject json = null;
        try {
            String result = HttpUtil.httpPostRequest(url, map);
            json = (JSONObject) JSONObject.parse(result);
        } catch (UnsupportedEncodingException e) {
            e.getMessage();
        }
        /**
         * 3 返回参数及示例
         * 参数	类型	描述
         * keyboardPwdId	Int	密码ID
         * {
         *     "keyboardPwdId": 24242
         * }
         */
        return json;
    }

    /**
     * 修改密码
     * <p>
     * 2 参数说明
     * 名称	类型	是否必需	描述
     * clientId	String	Y	创建应用分配的client_id
     * accessToken	String	Y	访问令牌，通过获取访问令牌接口获取
     * lockId	Int	Y	锁ID，由锁初始化接口生成
     * keyboardPwdId	Int	Y	键盘密码ID
     * keyboardPwdName	String	N	新的密码名称
     * newKeyboardPwd	String	N	新的密码
     * startDate	Long	N	修改后密码有效期开始时间（时间戳，单位毫秒）
     * endDate	Long	N	修改后密码有效期结束时间（时间戳，单位毫秒）
     * changeType	Int	N	修改方式:
     * 1-通过APP走蓝牙修改，不传默认1，必需先通过APP SDK蓝牙修改后调用该接口
     * 2-通过网关修改，如果锁有连接网关，则可以传2，直接调用该接口修改生效
     * 3-NB-IoT，如果是NB-IoT锁，可以传3，通过NB网络下发指令，要等NB唤醒才会下发生效。
     * date	Long	Y	当前时间（时间戳，单位毫秒）
     */
    public static JSONObject update(Integer lockId, Integer keyboardPwdId, String newKeyboardPwd, Date startDate, Date endDate) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientId", client_id);

        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            //次方法获取键值对的名称
            String key = entry.getKey();
            //次方法获取键值对的值
            Object value = entry.getValue();
            if ("sciener_access_token".equals(key)) {
                map.put("accessToken", value);
            }
        }

        map.put("lockId", lockId);
        map.put("keyboardPwdId", keyboardPwdId);
        map.put("newKeyboardPwd", newKeyboardPwd);
        map.put("changeType", 3);
        map.put("startDate", DateUtils.getTimeInMillis(startDate));
        map.put("endDate", DateUtils.getTimeInMillis(endDate));
        map.put("date", System.currentTimeMillis());
        String url = "https://api.sciener.com/v3/keyboardPwd/change";
        JSONObject json = null;
        try {
            System.out.println(objectMap);
            String result = HttpUtil.httpPostRequest(url, map);
            json = (JSONObject) JSONObject.parse(result);
        } catch (UnsupportedEncodingException e) {
            e.getMessage();
        }
        /**
         * 3 返回参数及示例
         * 参数	类型	描述
         * errcode	Int	错误码
         * errmsg	String	错误信息
         * description	String	错误描述
         * {
         *     "errcode": 0,
         *     "errmsg": "none error message",
         *     "description":"表示成功或是"
         * }
         */
        return json;
    }

    /**
     * 查询密码
     * 名称	类型	是否必需	描述
     * clientId	String	Y	创建应用分配的client_id
     * accessToken	String	Y	访问令牌，通过获取访问令牌接口获取
     * lockId	Int	Y	锁ID，由锁初始化接口生成
     * keyboardPwdType	Int	Y	密码类型，请参考2.1节的说明 3
     * keyboardPwdName	String	N	密码名称
     * startDate	Long	Y	有效期开始时间（时间戳，单位毫秒）
     * endDate	Long	N	有效期结束时间（时间戳，单位毫秒）
     * date	Long	Y	当前时间（时间戳，单位毫秒）
     */
    public static JSONObject select(Integer lockId, Integer keyboardPwdType, Date startDate, Date endDate) {
        //进入先加载token
        Map<String, Object> map = new HashMap<>();
        map.put("clientId", client_id);
        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            //次方法获取键值对的名称
            String key = entry.getKey();
            //次方法获取键值对的值
            Object value = entry.getValue();
            if ("sciener_access_token".equals(key)) {
                map.put("accessToken", value);
            }
        }
        map.put("lockId", lockId);
        map.put("keyboardPwdType", keyboardPwdType);
        map.put("startDate", DateUtils.getTimeInMillis(startDate));
        if (DateUtils.getTimeInMillis(startDate) != DateUtils.getTimeInMillis(endDate)) {
            map.put("endDate", DateUtils.getTimeInMillis(endDate));
        }
        map.put("date", System.currentTimeMillis());
        String url = "https://api.sciener.com/v3/keyboardPwd/get";
        JSONObject json = null;
        try {
            String result = HttpUtil.httpPostRequest(url, map);
            json = (JSONObject) JSONObject.parse(result);
        } catch (UnsupportedEncodingException e) {
            e.getMessage();
        }
        /**

         3 返回参数及示例
         参数	类型	描述
         keyboardPwd	String	键盘密码
         keyboardPwdId	Int	键盘密码ID
         */
        return json;
    }


    /**
     * 删除密码
     * <p>
     * 名称	类型	是否必需	描述
     * clientId	String	Y	创建应用分配的client_id
     * accessToken	String	Y	访问令牌，通过获取访问令牌接口获取
     * lockId	Int	Y	锁ID，由锁初始化接口生成
     * keyboardPwdId	Int	Y	密码ID
     * <p>
     * deleteType	Int	Y	删除方式:
     * 1-通过APP走蓝牙删除，不传默认1，必需先通过APP蓝牙删除后再调用该接口
     * 2-通过网关删除，如果锁有连接网关，则可以传2，直接调用该接口从锁里删除密码
     * 3-NB-IoT，如果是NB-IoT锁，可以传3，通过NB网络删除，待NB唤醒后下发删除指令。
     * <p>
     * date	Long	Y	当前时间（时间戳，单位毫秒）
     */
    public static JSONObject delete(Integer lockId, Integer keyboardPwdId) {
        Map<String, Object> map = new HashMap<>();
        map.put("clientId", client_id);

        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            //次方法获取键值对的名称
            String key = entry.getKey();
            //次方法获取键值对的值
            Object value = entry.getValue();
            if ("sciener_access_token".equals(key)) {
                map.put("accessToken", value);
            }
        }

        map.put("lockId", lockId);
        map.put("keyboardPwdId", keyboardPwdId);
        map.put("deleteType", 3);
        map.put("date", System.currentTimeMillis());
        Map<String, Object> headers = new HashMap<>();
        String url = "https://api.sciener.com/v3/keyboardPwd/delete";
        JSONObject json = null;
        try {
            String result = HttpUtil.httpPostRequest(url, map);
            json = (JSONObject) JSONObject.parse(result);
        } catch (UnsupportedEncodingException e) {
            e.getMessage();
        }
        /**
         * 3 返回参数及示例
         * 参数	类型	描述
         * errcode	Int	错误码
         * errmsg	String	错误信息
         * description	String	错误描述
         * {
         *     "errcode": 0,
         *     "errmsg": "none error message",
         *     "description":"表示成功或是"
         * }
         */
        return json;
    }

    /**
     * 获取门锁注册使用token
     *
     * @return
     */
    public static String getToken() {
        String token = IdUtils.fastUUID();
        return token;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }

    /**
     * 获取门锁注册使用32位 MD5加密密码串
     */
    public static String getMD5PWD(String pwd) {
        byte[] digest = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(pwd.getBytes());
            digest = md5.digest();
        }catch (NoSuchAlgorithmException e){
            throw new ServiceException("不支持此算法", e);
        } finally {
            return byteArrayToHexString(digest);
        }
    }

    /**
     * 新用户注册
     *
     * @param username 账户
     * @param password 密码
     * @return 1. 参数说明
     * 名称	类型	是否必需	描述
     * clientId	String	Y	开放平台注册时分配的app_id
     * clientSecret	String	Y	开放平台注册时分配的app_secret
     * username	String	Y	用户名
     * password	String	Y	密码（32位，小写，md5 加密）
     * date	Long	Y	当前时间（时间戳，毫秒数）
     */
    public static JSONObject register(String username, String password) {
        //进入先加载token
        Map<String, Object> map = new HashMap<>();
        map.put("clientId", client_id);
        map.put("clientSecret", client_secret);
        map.put("username", username);
        try {
            map.put("password", getMD5PWD(password));
        } catch (ServiceException e) {
            e.printStackTrace();
        }
        map.put("date", System.currentTimeMillis());
        String url = "https://api.sciener.com/v3/user/register";
        JSONObject json = null;
        try {
            String result = HttpUtil.httpPostRequest(url,map);
            json = JSONObject.parse(result);
            System.out.println(json);
        } catch (UnsupportedEncodingException e) {
            e.getMessage();
        }
        /**
         2.返回参数
         参数	类型	描述
         username	String	注册成功返回（使用此username进行授权，密码则是传过来的password参数）
         */
        return json;
    }
}
