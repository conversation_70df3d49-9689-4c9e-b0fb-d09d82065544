package com.flat.logic.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/12/14 18:00
 * @Title: BillUtils
 * @Description:
 */
public class BillUtils {

    public static int getIntTypeName(String type) {
        switch (type) {
            case "押金":
                return 0;
            case "房租费":
                return 1;
            case "水费":
                return 2;
            case "电费":
                return 3;
            case "保洁费":
                return 4;
            case "维修费":
                return 5;
            case "退租费":
                return 6;
            case "其他":
                return 7;
            default:
                return 7;
        }
    }

    /**
     * 缴费类型（water=水费，electric=电费，rent=房租费，clean=保洁费，deposit=押金，repairs=维修费，other=其他）
     *
     * @param type
     * @return
     */
    public static String getStringTypeName(String type) {
        switch (type) {
            case "deposit":
                return "押金";
            case "rent":
                return "房租费";
            case "water":
                return "水费";
            case "electric":
                return "电费";
            case "clean":
                return "保洁费";
            case "repairs":
                return "维修费";
            case "surrender":
                return "退租费";
            case "other":
                return "其他";
            default:
                return "其他";
        }
    }

    /**
     * 支付类型（0=支付宝，1=微信，2=其他）
     *
     * @param type
     * @return
     */
    public static String getPayWayName(int type) {
        switch (type) {
            case 0:
                return "支付宝";
            case 1:
                return "微信";
            default:
                return "其他";
        }
    }

    /**
     * 是否已支付（0=否，1=是）
     *
     * @param type
     * @return
     */
    public static String getStateName(int type) {
        switch (type) {
            case 0:
                return "未支付";
            case 1:
                return "已支付";
            default:
                return "未支付";
        }
    }

    /**
     * 微信总金额计算：单位为（分）
     *
     * @param amount 金额
     * @return 8.88元=888分
     */
    public static Integer getAmount(BigDecimal amount) {
        BigDecimal mul = new BigDecimal(100);
        return amount.multiply(mul).intValue();
    }
}
