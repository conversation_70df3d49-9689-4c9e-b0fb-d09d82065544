package com.flat.logic.mapper.statistics;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.logic.dto.resp.statistics.RoomInvoiceStatisticsResp;

public interface StatisticsMapper {

    /**
     * 查询房间账单发票统计信息
     *
     * @param roomId 房间ID，可为空
     * @return 房间账单发票统计信息列表
     */
    List<RoomInvoiceStatisticsResp> selectRoomInvoiceStatistics(@Param("roomId") Long roomId);
}
