package com.flat.logic.mapper.meter;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.meter.MeterLogQueryReq;
import com.flat.logic.entity.meter.FlatMeterLog;

import java.util.List;

/**
 * <p>
 * 公寓房源抄表log表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatMeterLogMapper extends CoreMapper<FlatMeterLog> {

    List<FlatMeterLog> selectLastWaterListGroupByRoom();

    List<FlatMeterLog> selectLastElectricListGroupByRoom();

    List<FlatMeterLog> selectBaseList(MeterLogQueryReq req);

}
