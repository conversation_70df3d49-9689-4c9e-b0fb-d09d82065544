package com.flat.logic.mapper.flat;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.flat.RoomWifiQueryReq;
import com.flat.logic.dto.resp.flat.RoomWifiResp;
import com.flat.logic.entity.flat.FlatRoomWifi;

/**
 * <p>
 * wifi表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatRoomWifiMapper extends CoreMapper<FlatRoomWifi> {

    List<RoomWifiResp> selectResp(RoomWifiQueryReq req);

}
