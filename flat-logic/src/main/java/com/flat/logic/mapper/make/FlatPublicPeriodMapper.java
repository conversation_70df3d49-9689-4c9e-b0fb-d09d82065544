package com.flat.logic.mapper.make;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flat.logic.dto.req.make.PublicPeriodQueryReq;
import com.flat.logic.dto.resp.make.PublicPeriodResp;
import com.flat.logic.entity.make.FlatPublicPeriod;
import org.apache.ibatis.annotations.Param;

import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 公共设施时间段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatPublicPeriodMapper extends BaseMapper<FlatPublicPeriod> {

    /**
     * 查询时间段列表
     */
    List<PublicPeriodResp> selectRespList(PublicPeriodQueryReq req);

    /**
     * 查询时间段详情
     */
    PublicPeriodResp selectRespById(Long id);

    /**
     * 检查时间段是否重叠
     *
     * @param publicId 公共设施ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param excludeId 排除的时间段ID（修改时使用）
     * @return 重叠的时间段数量
     */
    int selectOverlapCount(
            @Param("publicId") Long publicId,
            @Param("beginTime") LocalTime beginTime,
            @Param("endTime") LocalTime endTime,
            @Param("excludeId") Long excludeId
    );
}
