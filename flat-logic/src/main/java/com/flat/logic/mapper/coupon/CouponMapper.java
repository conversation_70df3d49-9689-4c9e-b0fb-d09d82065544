package com.flat.logic.mapper.coupon;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.coupon.CouponQueryReq;
import com.flat.logic.dto.resp.coupon.CouponResp;
import com.flat.logic.entity.coupon.Coupon;

/**
 * <p>
 * 优惠券表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface CouponMapper extends CoreMapper<Coupon> {
    
    /**
     * 查询优惠券列表，包含用户信息
     * 
     * @param req 查询条件
     * @return 优惠券列表
     */
    List<CouponResp> selectCouponList(CouponQueryReq req);
    
    /**
     * 查询优惠券详情，包含用户信息
     * 
     * @param id 优惠券ID
     * @return 优惠券详情
     */
    CouponResp selectCouponDetail(@Param("id") Long id);
}
