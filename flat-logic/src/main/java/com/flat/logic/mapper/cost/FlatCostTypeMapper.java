package com.flat.logic.mapper.cost;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.cost.CostTypeQueryReq;
import com.flat.logic.entity.cost.FlatCostType;

/**
 * <p>
 * 用户费用类型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatCostTypeMapper extends CoreMapper<FlatCostType> {
    
    /**
     * 查询费用类型列表
     * 
     * @param req 查询条件
     * @return 费用类型列表
     */
    List<FlatCostType> selectCostTypeList(@Param("req") CostTypeQueryReq req);
}
