package com.flat.logic.mapper.lease;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.lease.SurrenderQueryReq;
import com.flat.logic.dto.resp.lease.SurrenderListResp;
import com.flat.logic.entity.lease.FlatSurrender;

import java.util.List;

/**
 * <p>
 * 公寓退租表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatSurrenderMapper extends CoreMapper<FlatSurrender> {


    List<SurrenderListResp> selectRespList(SurrenderQueryReq req);

}
