package com.flat.logic.mapper.lease;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.lease.ContractLogQueryReq;
import com.flat.logic.entity.lease.FlatContractLog;

/**
 * <p>
 * 公寓合同流程记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatContractLogMapper extends CoreMapper<FlatContractLog> {

    default List<FlatContractLog> selectList(ContractLogQueryReq req) {
        return this.selectList(q -> q
                .eq(req.getFlatId() != null, FlatContractLog::getFlatId, req.getFlatId())
                .eq(req.getContractId() != null, FlatContractLog::getContractId, req.getContractId())
                .eq(req.getLeaseId() != null, FlatContractLog::getLeaseId, req.getLeaseId())
                .eq(req.getType() != null, FlatContractLog::getType, req.getType())
                .like(StringUtils.isNotBlank(req.getTypeName()), FlatContractLog::getTypeName, req.getTypeName())
                .eq(req.getOperateId() != null, FlatContractLog::getOperateId, req.getOperateId())
                .eq(FlatContractLog::getDelStatus, 0)
        );
    }

}
