package com.flat.logic.mapper.make;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.make.PublicQueryReq;
import com.flat.logic.dto.resp.make.PublicResp;
import com.flat.logic.entity.make.FlatPublic;

import java.util.List;

/**
 * <p>
 * 公寓公用设施表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatPublicMapper extends CoreMapper<FlatPublic> {

    List<PublicResp> selectRespList(PublicQueryReq req);

    PublicResp selectRespById(Long id);

}
