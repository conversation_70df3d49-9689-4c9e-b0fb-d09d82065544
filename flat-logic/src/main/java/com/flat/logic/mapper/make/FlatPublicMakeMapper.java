package com.flat.logic.mapper.make;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.make.PublicMakeQueryReq;
import com.flat.logic.dto.resp.make.PublicMakeListResp;
import com.flat.logic.entity.make.FlatPublicMake;

import java.time.LocalDate;
import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 公寓公用设施预约表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatPublicMakeMapper extends CoreMapper<FlatPublicMake> {

    List<PublicMakeListResp> selectRespList(PublicMakeQueryReq req);

    PublicMakeListResp selectRespById(Long id);

    /**
     * 统计某公共设施某时间段的有效预约总人数
     *
     * @param publicId 公共设施ID
     * @param publicPeriodId 时间段ID
     * @param makeDate 预约日期
     * @return 总人数
     */
    int selectPeopleCount(
            @Param("publicId") Long publicId,
            @Param("publicPeriodId") Long publicPeriodId,
            @Param("makeDate") LocalDate makeDate
    );

}
