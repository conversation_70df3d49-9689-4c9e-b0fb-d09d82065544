package com.flat.logic.mapper.make;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.make.RepairMakeQueryReq;
import com.flat.logic.dto.resp.make.RepairMakeListResp;
import com.flat.logic.entity.make.FlatRepairMake;

import java.util.List;

/**
 * <p>
 * 公寓预约报修表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatRepairMakeMapper extends CoreMapper<FlatRepairMake> {

    List<RepairMakeListResp> selectRespList(RepairMakeQueryReq req);

    RepairMakeListResp selectRespById(Long id);

//    List<FlatRepairs> selectFlatRepairsToday(StatisticsReq request);

}
