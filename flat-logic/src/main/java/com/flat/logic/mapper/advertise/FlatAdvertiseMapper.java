package com.flat.logic.mapper.advertise;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.advertise.AdvertiseQueryReq;
import com.flat.logic.dto.resp.advertise.AdvertiseResp;
import com.flat.logic.entity.advertise.FlatAdvertise;

import java.util.List;

/**
 * <p>
 * 公寓广告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatAdvertiseMapper extends CoreMapper<FlatAdvertise> {

    List<AdvertiseResp> selectRespList(AdvertiseQueryReq req);

}
