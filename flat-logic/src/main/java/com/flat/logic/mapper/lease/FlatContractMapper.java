package com.flat.logic.mapper.lease;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.lease.ContractQueryReq;
import com.flat.logic.dto.resp.lease.ContractListResp;
import com.flat.logic.entity.lease.FlatContract;

/**
 * <p>
 * 公寓合同表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatContractMapper extends CoreMapper<FlatContract> {

    List<ContractListResp> selectRespList(ContractQueryReq req);

}
