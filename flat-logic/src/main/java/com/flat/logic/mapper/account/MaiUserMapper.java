package com.flat.logic.mapper.account;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.resp.account.ButlerContactResp;
import com.flat.logic.entity.account.MaiUser;

/**
 * <p>
 * 管家用户信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface MaiUserMapper extends CoreMapper<MaiUser> {

    default MaiUser selectUserByUsername(String username) {
        return selectOne(q -> q
                .eq(MaiUser::getUsername, username)
                .eq(MaiUser::getDelStatus, 0)
        );
    }

    List<ButlerContactResp> selectButlerContactList(Long cusId);
}
