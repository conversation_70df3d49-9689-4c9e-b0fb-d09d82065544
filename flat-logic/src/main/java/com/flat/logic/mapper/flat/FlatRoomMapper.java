package com.flat.logic.mapper.flat;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.flat.RoomQueryReq;
import com.flat.logic.dto.req.flat.RoomStateQueryReq;
import com.flat.logic.dto.resp.flat.RoomDetailResp;
import com.flat.logic.dto.resp.flat.RoomFinanceResp;
import com.flat.logic.dto.resp.flat.RoomResp;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.model.RoomStateModel;

/**
 * <p>
 * 公寓房间表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatRoomMapper extends CoreMapper<FlatRoom> {

    /**
     * 房源查询
     */
    List<RoomResp> selectRespList(RoomQueryReq req);

    /**
     * 获取所有楼栋号
     */
    List<String> selectDistinctBlockNos();

    List<Integer> selectDistinctFloorsByBlock(String blockNo);

    RoomResp selectRespById(Long id);

    /**
     * 查询房源详情，包含欠费状态和欠费金额
     *
     * @param id 房源ID
     * @return 房源详情
     */
    RoomDetailResp selectDetailById(Long id);

    /**
     * 用户端:首页搜索查询房源信息
     */
    List<RoomResp> selectNotFreeList(String content);

    List<FlatRoom> selectLeasedList(Long cusId);

    List<RoomStateModel> selectStates(RoomStateQueryReq req);
}
