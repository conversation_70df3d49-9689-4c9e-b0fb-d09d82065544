package com.flat.logic.mapper.activity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.activity.ActivityQueryReq;
import com.flat.logic.dto.resp.activity.ActivityResp;
import com.flat.logic.entity.activity.Activity;

/**
 * 活动表 Mapper 接口
 */
public interface ActivityMapper extends CoreMapper<Activity> {
    
    /**
     * 查询活动列表
     * 
     * @param req 查询条件
     * @return 活动列表
     */
    List<ActivityResp> selectActivityList(@Param("req") ActivityQueryReq req);
    
    /**
     * 查询活动详情
     * 
     * @param id 活动ID
     * @return 活动详情
     */
    ActivityResp selectActivityDetail(@Param("id") Long id);
}
