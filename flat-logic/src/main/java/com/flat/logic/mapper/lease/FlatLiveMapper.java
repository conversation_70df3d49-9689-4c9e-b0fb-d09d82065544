package com.flat.logic.mapper.lease;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.lease.LiveQueryReq;
import com.flat.logic.dto.resp.lease.LiveResp;
import com.flat.logic.entity.lease.FlatLive;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatLiveMapper extends CoreMapper<FlatLive> {

    List<LiveResp> selectRespList(LiveQueryReq req);

}
