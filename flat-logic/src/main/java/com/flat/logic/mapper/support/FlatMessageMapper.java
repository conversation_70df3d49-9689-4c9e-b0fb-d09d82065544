package com.flat.logic.mapper.support;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.support.MessageQueryReq;
import com.flat.logic.dto.resp.support.FlatMessageResp;
import com.flat.logic.entity.support.FlatMessage;

import java.util.List;

/**
 * <p>
 * 公寓消息通知表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatMessageMapper extends CoreMapper<FlatMessage> {

    /**
     * 查询公寓消息通知列表
     */
    List<FlatMessage> selectBaseList(MessageQueryReq req);

    /**
     * 查询公寓消息通知列表
     */
    List<FlatMessageResp> selectRespList(MessageQueryReq req);

}
