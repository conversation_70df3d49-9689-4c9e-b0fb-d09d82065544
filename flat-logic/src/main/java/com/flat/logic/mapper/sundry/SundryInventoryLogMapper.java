package com.flat.logic.mapper.sundry;

import com.flat.logic.dto.req.sundry.SundryInventoryLogQueryReq;
import com.flat.logic.dto.resp.sundry.SundryInventoryLogListResp;
import com.flat.logic.entity.sundry.SundryInventoryLog;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;

/**
 * <p>
 * 物品库产品进库与出库记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface SundryInventoryLogMapper extends CoreMapper<SundryInventoryLog> {

    List<SundryInventoryLogListResp> selectRespList(@Param("req") SundryInventoryLogQueryReq req);

}
