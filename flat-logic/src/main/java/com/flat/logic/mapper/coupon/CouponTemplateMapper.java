package com.flat.logic.mapper.coupon;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.coupon.CouponTemplateQueryReq;
import com.flat.logic.dto.resp.coupon.CouponTemplateResp;
import com.flat.logic.entity.coupon.CouponTemplate;

/**
 * <p>
 * 优惠券模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface CouponTemplateMapper extends CoreMapper<CouponTemplate> {

    /**
     * 查询优惠券模板列表，包含统计信息
     * @param req 查询条件
     * @return 优惠券模板列表（含统计信息）
     */
    List<CouponTemplateResp> selectTemplateRespList(CouponTemplateQueryReq req);
}
