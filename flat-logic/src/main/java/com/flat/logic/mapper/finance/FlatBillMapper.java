package com.flat.logic.mapper.finance;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.finance.BillQueryReq;
import com.flat.logic.dto.resp.finance.BillResp;
import com.flat.logic.entity.finance.FlatBill;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 公寓账单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatBillMapper extends CoreMapper<FlatBill> {


    List<BillResp> selectRespList(BillQueryReq req);

    /**
     * 获取指定日期的保洁费用总和
     *
     * @param date 指定日期
     * @return 保洁费用总和
     */
    BigDecimal selectCleanFeeByDate(LocalDate date);

    BigDecimal statTotalMoney(Long leaseId);

    BigDecimal statInvoicedMoney(Long leaseId);
}
