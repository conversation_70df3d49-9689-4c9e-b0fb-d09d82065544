package com.flat.logic.mapper.cost;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.cost.CostDeliveryQueryReq;
import com.flat.logic.entity.cost.FlatCostDelivery;

/**
 * <p>
 * 自定义费用定点取货条目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatCostDeliveryMapper extends CoreMapper<FlatCostDelivery> {
    
    /**
     * 根据条件查询定点取货条目列表
     * 
     * @param req 查询条件
     * @param costTypeId 费用类型ID
     * @return 定点取货条目列表
     */
    List<FlatCostDelivery> selectDeliveryList(@Param("req") CostDeliveryQueryReq req, @Param("costTypeId") Long costTypeId);
    
}
