package com.flat.logic.mapper.lease;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.finance.BalanceQueryReq;
import com.flat.logic.dto.req.lease.LeaseQueryReq;
import com.flat.logic.dto.resp.finance.BalanceListResp;
import com.flat.logic.dto.resp.lease.LeaseExportResp;
import com.flat.logic.dto.resp.lease.LeaseListResp;
import com.flat.logic.entity.lease.FlatLease;

import java.util.List;

/**
 * <p>
 * 房屋租赁记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatLeaseMapper extends CoreMapper<FlatLease> {

    List<LeaseListResp> selectRespList(LeaseQueryReq req);

    List<BalanceListResp> selectBalanceList(BalanceQueryReq req);

    /**
     * 查询租赁记录导出列表
     *
     * @param req 查询条件
     * @return 租赁记录导出列表
     */
    List<LeaseExportResp> selectExportList(LeaseQueryReq req);

}
