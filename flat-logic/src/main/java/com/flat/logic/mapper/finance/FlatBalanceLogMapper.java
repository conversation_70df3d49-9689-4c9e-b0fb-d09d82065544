package com.flat.logic.mapper.finance;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.finance.BalanceLogQueryReq;
import com.flat.logic.dto.resp.finance.BalanceLogResp;
import com.flat.logic.entity.finance.FlatBalanceLog;

/**
 * <p>
 * 公寓账户余额记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatBalanceLogMapper extends CoreMapper<FlatBalanceLog> {

    List<BalanceLogResp> selectRespList(BalanceLogQueryReq req);

    /**
     * 获取指定日期的水费总和
     *
     * @param date 指定日期
     * @return 水费总和
     */
    BigDecimal selectWaterFeeByDate(LocalDate date);

    /**
     * 获取指定日期的电费总和
     *
     * @param date 指定日期
     * @return 电费总和
     */
    BigDecimal selectElectricityFeeByDate(LocalDate date);
}
