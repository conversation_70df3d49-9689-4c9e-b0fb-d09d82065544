package com.flat.logic.mapper.make;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.make.VisitMakeQueryReq;
import com.flat.logic.dto.resp.make.VisitMakeListResp;
import com.flat.logic.entity.make.FlatVisitMake;

import java.util.List;

/**
 * <p>
 * 公寓访客预约表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatVisitMakeMapper extends CoreMapper<FlatVisitMake> {

    List<VisitMakeListResp> selectRespList(VisitMakeQueryReq req);

}
