package com.flat.logic.mapper.make;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.make.CleanMakeQueryReq;
import com.flat.logic.dto.resp.make.CleanMakeListResp;
import com.flat.logic.entity.make.FlatCleanMake;

import java.util.List;

/**
 * <p>
 * 公寓预约保洁表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatCleanMakeMapper extends CoreMapper<FlatCleanMake> {


    CleanMakeListResp selectRespById(Long id);

    List<CleanMakeListResp> selectRespList(CleanMakeQueryReq req);

}
