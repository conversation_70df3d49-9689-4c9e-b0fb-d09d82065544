package com.flat.logic.mapper.make;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.make.RoomMakeQueryReq;
import com.flat.logic.dto.resp.make.RoomMakeListResp;
import com.flat.logic.entity.make.FlatRoomMake;

import java.util.List;

/**
 * <p>
 * 公寓看房预约表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatRoomMakeMapper extends CoreMapper<FlatRoomMake> {

    List<RoomMakeListResp> selectRespList(RoomMakeQueryReq req);

    RoomMakeListResp selectRespById(Long id);

}
