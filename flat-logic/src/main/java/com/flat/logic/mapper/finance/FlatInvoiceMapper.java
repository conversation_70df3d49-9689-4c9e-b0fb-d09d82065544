package com.flat.logic.mapper.finance;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.finance.InvoiceQueryReq;
import com.flat.logic.dto.resp.finance.InvoiceListResp;
import com.flat.logic.entity.finance.FlatInvoice;

/**
 * <p>
 * 公寓发票表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatInvoiceMapper extends CoreMapper<FlatInvoice> {

    /**
     * 分页查询发票列表
     *
     * @param req 查询参数
     * @return 发票列表
     */
    List<InvoiceListResp> selectRespList(InvoiceQueryReq req);

    InvoiceListResp selectRespById(Long id);
}
