package com.flat.logic.mapper.cost;

import com.flat.logic.dto.req.cost.CostLogQueryReq;
import com.flat.logic.dto.resp.cost.CostLogResp;
import com.flat.logic.entity.cost.FlatCostLog;

import java.util.List;

import com.flat.common.database.mapper.CoreMapper;

/**
 * <p>
 * 用户费用记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface FlatCostLogMapper extends CoreMapper<FlatCostLog> {

    List<CostLogResp> selectRespList(CostLogQueryReq req);

    CostLogResp selectRespById(Long id);

}
