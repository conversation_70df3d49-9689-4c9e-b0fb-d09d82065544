package com.flat.logic.mapper.activity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.activity.ActivityUserQueryReq;
import com.flat.logic.dto.resp.activity.ActivityUserResp;
import com.flat.logic.entity.activity.ActivityUser;

/**
 * <p>
 * 活动报名表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface ActivityUserMapper extends CoreMapper<ActivityUser> {

    
    /**
     * 根据活动ID获取我参加的记录详情
     * 
     * @param userActivityId 活动报名记录ID
     * @return 活动报名记录详情
     */
    ActivityUserResp selectUserActivityDetail(@Param("userActivityId") Long userActivityId);

    ActivityUserResp selectUserJoinActivity(@Param("cusId") Long cusId, @Param("activityId") Long activityId);
    
    
    /**
     * 分页查询活动报名人员列表
     * 
     * @param req 查询条件
     * @return 报名人员列表
     */
    List<ActivityUserResp> selectActivityUserList(@Param("req") ActivityUserQueryReq req);
}
