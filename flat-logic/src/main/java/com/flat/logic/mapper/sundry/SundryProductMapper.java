package com.flat.logic.mapper.sundry;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.flat.common.database.mapper.CoreMapper;
import com.flat.logic.dto.req.sundry.SundryProductQueryReq;
import com.flat.logic.dto.resp.sundry.SundryProductListResp;
import com.flat.logic.entity.sundry.SundryProduct;

/**
 * <p>
 * 物品库产品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
public interface SundryProductMapper extends CoreMapper<SundryProduct> {

    List<SundryProductListResp> selectRespList(@Param("req") SundryProductQueryReq req);

}
