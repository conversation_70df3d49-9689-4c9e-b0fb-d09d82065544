package com.flat.logic.entity.finance;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓发票表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_invoice")
@Schema(description = "公寓发票表")
public class FlatInvoice implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房源ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "租赁记录ID")
    private Long leaseId;

    @Schema(description = "企业ID")
    private Long companyId;

    @Schema(description = "发票类型[0=电子发票;1=增值发票]")
    private Integer invoiceType;

    @Schema(description = "抬头类型[0=企业单位;1=个人/非企业单位]")
    private Integer riseType;

    @Schema(description = "抬头名称")
    private String riseName;

    @Schema(description = "单位税号")
    private String dutyNo;

    @Schema(description = "注册地址")
    private String registerAddress;

    @Schema(description = "注册电话")
    private String registerPhoneNumber;

    @Schema(description = "开户银行")
    private String openBank;

    @Schema(description = "银行账户")
    private String bankAccount;

    @Schema(description = "联系电话")
    private String contactPhoneNumber;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "邮寄地址")
    private String address;

    @Schema(description = "开票状态[0=否;1=已开票]")
    private Integer status;

    @Schema(description = "总金额")
    private BigDecimal amount;

    @Schema(description = "开票时间,status=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime openTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
