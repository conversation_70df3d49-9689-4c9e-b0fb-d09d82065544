package com.flat.logic.entity.coupon;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 优惠券表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "优惠券表")
public class Coupon implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "优惠券模板id")
    private Long templateId;

    @Schema(description = "优惠券领取人")
    private Long cusId;

    @Schema(description = "优惠券类型[1=保洁预约;2=自定义费用]")
    private Integer target;

    @Schema(description = "自定义费用类型，target=2时有效")
    private Long costTypeId;

    @Schema(description = "优惠券名称")
    private String name;

    @Schema(description = "有效期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validBeginTime;

    @Schema(description = "有效期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validEndTime;

    @Schema(description = "优惠券额度")
    private Integer amount;

    @Schema(description = "审核状态[0=待审核;1=撤销;2=业主拒绝;3=业主确认]")
    private Integer verifyStatus;

    @Schema(description = "业主审核人ID")
    private Long ownerVerifyId;

    @Schema(description = "业主审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ownerVerifyTime;

    @Schema(description = "审核拒绝原因")
    private String verifyRefuse;

    @Schema(description = "使用状态[0=未使用;1=已使用]")
    private Integer useStatus;

    @Schema(description = "使用时间，use_status=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime useTime;

    @Schema(description = "使用优惠券的账单ID，use_status=1并且type=3时有效")
    private Long useBillId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
