package com.flat.logic.entity.activity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 活动报名表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("activity_user")
@Schema(description = "活动报名表")
public class ActivityUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "活动id")
    private Long activityId;

    @Schema(description = "报名的用户id")
    private Long cusId;

    @Schema(description = "报名时填写的姓名")
    private String realName;

    @Schema(description = "报名时填写的手机号")
    private String phoneNumber;

    @Schema(description = "是否需要缴报名费[0=免费;1=需要报名费]")
    private Integer entryFeeFlag;

    @Schema(description = "报名费费用,entry_fee_flag=1时有效")
    private BigDecimal entryFee;

    @Schema(description = "是否已付费[0=未付费;1=已付费],entry_fee_flag=1时有效")
    private Integer payStatus;

    @Schema(description = "收付款方式[0=线上支付;1=线下支付],entry_fee_flag=1时有效")
    private Integer payMode;

    @Schema(description = "支付时间,entry_fee_flag=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
