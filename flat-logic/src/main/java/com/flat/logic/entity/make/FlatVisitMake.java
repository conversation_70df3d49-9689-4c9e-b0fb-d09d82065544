package com.flat.logic.entity.make;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓访客预约表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_visit_make")
@Schema(description = "公寓访客预约表")
public class FlatVisitMake implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户/发起者ID")
    private Long cusId;

    @Schema(description = "发起人类型[1=承租人;2=同住人]")
    private Integer applyCusType;

    @Schema(description = "房屋租赁记录ID")
    private Long leaseId;

    @Schema(description = "访客名称")
    private String visitorName;

    @Schema(description = "访问预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime visitTime;

    @Schema(description = "访客身份证号")
    private String visitorIdCard;

    @Schema(description = "访客联系方式")
    private String visitorPhoneNumber;

    @Schema(description = "用户申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cusApplyTime;

    @Schema(description = "审核状态[0=待审核;1=撤销;2=管家拒绝;3=管家确认;4=物业拒绝;5=物业确认已到达;6=物业确认未到达;7=过时已失效]")
    private Integer status;

    @Schema(description = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "管家审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime butlerVerifyTime;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "物业审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime propertyVerifyTime;

    @Schema(description = "拒绝原因")
    private String refuse;

    @Schema(description = "需求描述")
    private String description;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
