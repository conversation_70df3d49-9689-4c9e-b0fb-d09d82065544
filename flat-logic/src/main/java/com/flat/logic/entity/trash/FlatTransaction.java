package com.flat.logic.entity.trash;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓交易流水表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_transaction")
@Schema(description = "公寓交易流水表")
public class FlatTransaction implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "付款用户ID")
    private Long cusUserId;

    @Schema(description = "账单ID")
    private Long billId;

    @Schema(description = "账单标题")
    private String billTitle;

    @Schema(description = "资金流向[0=支出;1=收入]")
    private Integer moneyFlow;

    @Schema(description = "账单流水号")
    private String billOrderNo;

    @Schema(description = "交易单号（支付平台返回）")
    private String billTransactionNo;

    @Schema(description = "交易金额")
    private BigDecimal money;

    @Schema(description = "交易状态[0=进行中;1=交易成功;2=交易失败]")
    private Integer status;

    @Schema(description = "支付渠道[0=支付宝;1=微信;2=其他]")
    private Integer payChannel;

    @Schema(description = "支付目标[water=水费;electric=电费;rent=房租费;clean=保洁费]")
    private String payTarget;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @Schema(description = "水/电账户ID，pay_service=water/electric时有效")
    private Long accountId;

    @Schema(description = "合同支付计划ID，pay_service=rent时有效")
    private Long contractPayId;

    @Schema(description = "预约保洁记录ID，pay_service=clean时有效")
    private Long cleanMakeId;

    @Schema(description = "收款企业ID")
    private Long receiveCompanyId;

    @Schema(description = "是否已开票[0=否;1=是]")
    private Integer invoiceStatus;

    @Schema(description = "发票ID")
    private Long invoiceId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
