package com.flat.logic.entity.lease;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓退租表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_surrender")
@Schema(description = "公寓退租表")
public class FlatSurrender implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "用户名称")
    private String cusRealName;

    @Schema(description = "退款者联系方式")
    private String cusPhoneNumber;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "租赁记录ID")
    private Long leaseId;

    @Schema(description = "退租原因")
    private String reason;

    @Schema(description = "银行账户")
    private String bankAccount;

    @Schema(description = "开户银行名称")
    private String openBankName;

    @Schema(description = "开户行地址")
    private String openBankAddress;

    @Schema(description = "支付宝账户")
    private String alipayAccount;

    @Schema(description = "退租日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate surrenderDate;

    @Schema(description = "退租状态[0=用户提交待处理;1=用户撤销;2=后台确认款项;3=后台确认金额;4=后台确认打款;5=后台确认已打款]")
    private Integer status;

    @Schema(description = "用户撤销时间,status=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @Schema(description = "退租完成时间,status=5时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;

    @Schema(description = "物业费,status=2/3/4/5时有效")
    private BigDecimal propertyMoney;

    @Schema(description = "物业费退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer propertyMoneyBackType;

    @Schema(description = "房租费,status=2/3/4/5时有效")
    private BigDecimal roomMoney;

    @Schema(description = "房租费退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer roomMoneyBackType;

    @Schema(description = "水费,status=2/3/4/5时有效")
    private BigDecimal waterMoney;

    @Schema(description = "水费退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer waterMoneyBackType;

    @Schema(description = "电费,status=2/3/4/5时有效")
    private BigDecimal electricMoney;

    @Schema(description = "电费退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer electricMoneyBackType;

    @Schema(description = "押金,status=2/3/4/5时有效")
    private BigDecimal depositMoney;

    @Schema(description = "押金退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer depositMoneyBackType;

    @Schema(description = "其他费用,status=2/3/4/5时有效")
    private BigDecimal otherMoney;

    @Schema(description = "其他费用退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer otherMoneyBackType;

    @Schema(description = "线上退款金额,status=2/3/4/5时有效,线上指的是线上原路退回")
    private BigDecimal onlineBackMoney;

    @Schema(description = "线下退款金额,status=2/3/4/5时有效,银行卡和支付宝账户都属于线下退款方式")
    private BigDecimal offlineBackMoney;

    @Schema(description = "能耗费,status=2/3/4/5时有效")
    private BigDecimal energyMoney;

    @Schema(description = "能耗费退回方式[1=线上;2=线下],status=2/3/4/5时有效")
    private Integer energyMoneyBackType;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间(申请退租的时间)")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
