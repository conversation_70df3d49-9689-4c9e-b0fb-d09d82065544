package com.flat.logic.entity.flat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_company")
@Schema(description = "")
public class FlatCompany implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "企业标识")
    private String symbol;

    @Schema(description = "企业名称")
    private String companyName;

    @Schema(description = "e签宝住房模板名称")
    private String eSignHouseTemplateName;

    @Schema(description = "e签宝住房模板id")
    private String eSignHouseDocTemplateId;

    @Schema(description = "e签宝商铺模板名称")
    private String eSignShopTemplateName;

    @Schema(description = "e签宝商铺模板id")
    private String eSignShopDocTemplateId;

    @Schema(description = "e签宝印章id（授权后的印章）")
    private String eSignSealId;

    @Schema(description = "微信支付app_id")
    private String wxPayAppId;

    @Schema(description = "微信支付mch_id")
    private String wxPayMchId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;


}
