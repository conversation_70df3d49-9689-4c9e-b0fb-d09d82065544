package com.flat.logic.entity.sundry;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 物品库产品表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("sundry_product")
@Schema(description = "物品库产品表")
public class SundryProduct implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "物品库类别ID，sundry_category表ID")
    private Long categoryId;

    @Schema(description = "产品编号")
    private String number;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品单位")
    private String unit;

    @Schema(description = "采购单价")
    private BigDecimal unitPrice;

    @Schema(description = "期初库存")
    private Integer openingInventory;

    @Schema(description = "日消耗量")
    private Integer dayConsumeAmount;

    @Schema(description = "总库存")
    private Integer totalInventory;

    @Schema(description = "告警阈值，低于此值时告警")
    private Integer alarmThreshold;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
