package com.flat.logic.entity.trash;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 门锁表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_room_door")
@Schema(description = "门锁表")
public class FlatRoomDoor implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "门锁密码")
    private String passwork;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "可否更换密码（0=否，1=是）")
    private Integer type;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "门锁ID")
    private Long lockId;

    @Schema(description = "密码ID")
    private Long pwdId;


}
