package com.flat.logic.entity.make;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓保洁类型表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_clean_type")
@Schema(description = "公寓保洁类型表")
public class FlatCleanType implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "保洁项目名称")
    private String name;

    @Schema(description = "保洁内容")
    private String content;

    @Schema(description = "保洁费用")
    private BigDecimal price;

    @Schema(description = "是否需要付费[0=否;1=是]")
    private Integer needPayFlag;

    @Schema(description = "服务项目")
    private String services;

    @Schema(description = "清洗方式")
    private String cleaning;

    @Schema(description = "标签")
    private String label;

    @Schema(description = "主题图片地址")
    private String themeUrl;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
