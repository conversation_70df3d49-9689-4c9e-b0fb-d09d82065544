package com.flat.logic.entity.support;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 反馈表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_feedback")
@Schema(description = "反馈表")
public class SysFeedback implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "反馈类型（1=投诉，2=建议，3=其他）")
    private Integer type;

    @Schema(description = "房间号")
    private String roomNo;

    @Schema(description = "反馈内容")
    private String content;

    @Schema(description = "反馈图片")
    private String imgUrl;

    @Schema(description = "反馈人ID")
    private Long userId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "状态（0=展示，1=不展示），可用于逻辑删除")
    private Integer delStatus;


}
