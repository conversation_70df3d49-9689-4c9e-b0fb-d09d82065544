package com.flat.logic.entity.cost;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 用户自定义费用类型
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_cost_type")
@Schema(description = "用户自定义费用类型")
public class FlatCostType implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "类型名称")
    private String name;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "主题图片")
    private String themeUrl;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "购买开始时间")
    private LocalTime buyBeginTime;

    @Schema(description = "购买结束时间")
    private LocalTime buyEndTime;

    @Schema(description = "使用定点取货标识[0=不使用;1=使用]")
    private Integer useDeliveryFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
