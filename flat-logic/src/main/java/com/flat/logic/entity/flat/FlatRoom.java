package com.flat.logic.entity.flat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓房间表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_room")
@Schema(description = "公寓房间表")
public class FlatRoom implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "房源种类[0=住房;1=商铺]")
    private Integer kind;

    @Schema(description = "房间名称")
    private String name;

    @Schema(description = "栋/幢/楼号")
    private String blockNo;

    @Schema(description = "总楼层数")
    private Integer totalFloorNo;

    @Schema(description = "楼层")
    private Integer floorNo;

    @Schema(description = "房间号")
    private String roomNo;

    @Schema(description = "房型")
    private String houseType;

    @Schema(description = "面积")
    private BigDecimal acreage;

    @Schema(description = "朝向[0=南,1=北]")
    private Integer orientation;

    @Schema(description = "公寓价格")
    private BigDecimal price;

    @Schema(description = "主题图片")
    private String themeUrl;

    @Schema(description = "房间视频地址")
    private String videoUrl;

    @Schema(description = "房间图片地址")
    private String imgUrl;

    @Schema(description = "房源亮点")
    private String lightspot;

    @Schema(description = "交通出行")
    private String traffic;

    @Schema(description = "房间地址")
    private String address;

    @Schema(description = "房间状态[0=空闲;1=已出租;2=维修中;3=脏房;4=占用]")
    private Integer status;

    @Schema(description = "房源标签")
    private String label;

    @Schema(description = "房源类型[0=新上架;1=猜你喜欢;2=旧房源;3=未知]")
    private Integer roomType;

    @Schema(description = "租户ID")
    private Long cusId;

    @Schema(description = "租户ID")
    private Long cusTmpId;

    @Schema(description = "经纬度")
    private String gnote;

    @Schema(description = "水费户号")
    private String waterAccountNo;

    @Schema(description = "水表值")
    private BigDecimal waterMeterValue;

    @Schema(description = "水表最后记录的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime waterMeterLastTime;

    @Schema(description = "电费户号")
    private String electricAccountNo;

    @Schema(description = "电表值")
    private BigDecimal electricMeterValue;

    @Schema(description = "电表最后记录的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime electricMeterLastTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
