package com.flat.logic.entity.trash;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓房间共住人表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_room_live")
@Schema(description = "公寓房间共住人表")
public class FlatRoomLive implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户/租户ID")
    private Long cusId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "付款计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @Schema(description = "付款计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "身份证图片地址")
    private String imgUrl;

    @Schema(description = "共住人名称")
    private String name;

    @Schema(description = "共住人身份证号")
    private String idCard;

    @Schema(description = "审核状态（0=待审核，1=撤销，2=管家拒绝，3=管家确认）")
    private Integer state;

    @Schema(description = "用户确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cusTime;

    @Schema(description = "管家确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime butlerTime;

    @Schema(description = "拒绝原因")
    private String refuse;

    @Schema(description = "身份证反面地址")
    private String reverseUrl;

    @Schema(description = "身份证正面地址")
    private String frontUrl;

    @Schema(description = "联系方式")
    private String phonenumber;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;


}
