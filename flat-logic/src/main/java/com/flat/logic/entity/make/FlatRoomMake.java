package com.flat.logic.entity.make;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓看房预约表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_room_make")
@Schema(description = "公寓看房预约表")
public class FlatRoomMake implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "预约用户ID")
    private Long cusId;

    @Schema(description = "预约者名称")
    private String cusName;

    @Schema(description = "联系电话")
    private String cusPhoneNumber;

    @Schema(description = "预约看房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime makeTime;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "状态[0=提交待看房;1=撤销;2=管家拒绝;3=管家确认]")
    private Integer status;

    @Schema(description = "撤销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @Schema(description = "拒绝原因")
    private String refuse;

    @Schema(description = "管家审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime butlerVerifyTime;

    @Schema(description = "看房需求")
    private String description;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
