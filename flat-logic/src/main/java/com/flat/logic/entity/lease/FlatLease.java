package com.flat.logic.entity.lease;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 房屋租赁记录
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_lease")
@Schema(description = "房屋租赁记录")
public class FlatLease implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "租赁用户ID")
    private Long cusId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "签约企业ID")
    private Long companyId;

    @Schema(description = "租赁合同ID")
    private Long contractId;

    @Schema(description = "合同版本[1=老版本;2=新版本]")
    private Integer contractVersion;

    @Schema(description = "合同创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractCreateTime;

    @Schema(description = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractBeginTime;

    @Schema(description = "合同到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractEndTime;

    @Schema(description = "合同月租金")
    private BigDecimal contractMonthMoney;

    @Schema(description = "合同租赁月度数量")
    private Integer contractMonthCount;

    @Schema(description = "合同总租金")
    private BigDecimal contractTotalMoney;

    @Schema(description = "押金")
    private BigDecimal contractDepositMoney;

    @Schema(description = "合同月物业费")
    private BigDecimal contractMonthPropertyMoney;

    @Schema(description = "付租周期[0=月;1=季度;2=半年;3=年]")
    private Integer payPeriod;

    @Schema(description = "租赁状态[0=合同待签署;1=已取消(合同未签署);2=履约中(合同已签署);3=续租已申请;4=已续租;5=转租已申请;6=已转租;7=退租已申请;8=已退租]")
    private Integer status;

    @Schema(description = "合同签约时间,status=2/3/4/5/6/7/8时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractSignTime;

    @Schema(description = "取消时间,status=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @Schema(description = "结算渠道[0=用户自主支付结算;1=渠道结算(政府方代收后结算)]")
    private Integer settleChannel;

    @Schema(description = "续租发起的时间,status=3/4时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime renewApplyTime;

    @Schema(description = "续租完成的时间,和新的合同签约时间一致,status=4时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime renewFinishTime;

    @Schema(description = "续租后新的租赁记录ID,status=4时有效")
    private Long renewToLeaseId;

    @Schema(description = "转租发起的时间,status=5/6时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime subletApplyTime;

    @Schema(description = "转租完成的时间,和新的承租方合同签约时间一致,status=6时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime subletFinishTime;

    @Schema(description = "转租时是否保留同住人[0=不保留;1=保留]")
    private Integer subletToKeepLive;

    @Schema(description = "转租后新承租方的租赁记录ID,status=6时有效")
    private Long subletToLeaseId;

    @Schema(description = "退租申请时间,status=7/8时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime surrenderApplyTime;

    @Schema(description = "退租完成时间,status=8时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime surrenderFinishTime;

    @Schema(description = "退租记录ID,status=7/8时有效")
    private Long surrenderId;

    @Schema(description = "该记录是否属于续租[0=否;1=是]")
    private Integer renewFlag;

    @Schema(description = "续租的来源租赁记录ID(上一个租赁记录ID),relet_flag=1时有效")
    private Long renewFromLeaseId;

    @Schema(description = "该记录是否属于转租[0=否;1=是]")
    private Integer subletFlag;

    @Schema(description = "转租的来源租赁记录ID(上一个租赁记录ID),sublet_flag=1时有效")
    private Long subletFromLeaseId;

    @Schema(description = "登记入住时的水表值")
    private BigDecimal checkInWaterValue;

    @Schema(description = "登记入住时的电表值")
    private BigDecimal checkInElectricValue;

    @Schema(description = "水费账户余额,status=2/3/4/5/6/7/8时有效")
    private BigDecimal waterAccountBalance;

    @Schema(description = "电费账户余额,status=2/3/4/5/6/7/8时有效")
    private BigDecimal electricAccountBalance;

    @Schema(description = "是否作废[0=未作废;1=已作废]")
    private Integer invalidFlag;

    @Schema(description = "作废时间,invalid_flag=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @Schema(description = "作废操作管理员id")
    private Long invalidSysUserId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
