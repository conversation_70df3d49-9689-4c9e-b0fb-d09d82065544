package com.flat.logic.entity.news;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 新闻条目表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "新闻条目表")
public class News implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "所属新闻分类ID")
    private Long categoryId;

    @Schema(description = "新闻banner图片路径")
    private String bannerPath;

    @Schema(description = "新闻标题")
    private String title;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "内容类型[0=图文内容;1=指定活动;2=外部URL]")
    private Integer contentType;

    @Schema(description = "图文内容，content_type=0时有效")
    private String content;

    @Schema(description = "活动ID，content_type=1时有效")
    private Long activityId;

    @Schema(description = "外部URL，content_type=2时有效")
    private String outUrl;

    @Schema(description = "是否显示[0=不显示;1=显示]")
    private Integer showFlag;

    @Schema(description = "是否插入视频号[0=不插入;1=插入]")
    private Integer videoAccountFlag;

    @Schema(description = "视频号ID，video_account_flag=1时有效")
    private String videoAccountId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
