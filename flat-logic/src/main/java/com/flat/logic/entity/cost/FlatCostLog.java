package com.flat.logic.entity.cost;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 自定义费用记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_cost_log")
@Schema(description = "自定义费用记录表")
public class FlatCostLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户/发起者ID")
    private Long cusId;

    @Schema(description = "类型ID")
    private Long costTypeId;

    @Schema(description = "总费用")
    private BigDecimal money;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "账单ID")
    private Long billId;

    @Schema(description = "是否已付费[0=未付费;1=已付费]")
    private Integer payStatus;

    @Schema(description = "收付款方式[0=线上支付;1=线下支付]")
    private Integer payMode;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @Schema(description = "递送ID")
    private Long deliveryId;

    @Schema(description = "说明")
    private String description;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
