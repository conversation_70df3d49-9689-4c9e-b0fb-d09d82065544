package com.flat.logic.entity.lease;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 付款计划表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_pay_plan")
@Schema(description = "付款计划表")
public class FlatPayPlan implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "租赁记录ID")
    private Long leaseId;

    @Schema(description = "顺序")
    private Integer indexNo;

    @Schema(description = "计划类型[1=租金计划;2=能耗费计划;3=物业费计划(商铺)]")
    private Integer type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "付款金额")
    private BigDecimal money;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "付款计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @Schema(description = "付款计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "支付方式[0=线上支付;1=线下支付;2=转租固定支付],转租时当前时间及之前的所有支付计划默认都标识为转租固定支付")
    private Integer payMode;

    @Schema(description = "支付状态[0=待支付;1=已支付]")
    private Integer payStatus;

    @Schema(description = "支付凭证号，线下汇款账单编号，pay_mode=1时有效")
    private String certNo;

    @Schema(description = "支付凭证文件，线下汇款账单文件，pay_mode=1时有效")
    private String certFileUrl;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @Schema(description = "是否转租标识[0=否;1=是]")
    private Integer subletFlag;

    @Schema(description = "原租赁记录ID,sublet_flag=1时有效")
    private Long subletFromLeaseId;

    @Schema(description = "原支付计划ID,sublet_flag=1时有效,转租来源的版本V1的合同不存在该值")
    private Long subletRentPlanId;

    @Schema(description = "作废标识[0=未作废;1=已作废],租赁取消、转租、退租后或管理员作废租赁记录时未支付的计划标记为作废")
    private Integer invalidFlag;

    @Schema(description = "作废时间,invalid_flag=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "到期未支付通知")
    private Integer unpaidNotifyFlag;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;


}
