package com.flat.logic.entity.finance;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓账单表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_bill")
@Schema(description = "公寓账单表")
public class FlatBill implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "付款用户ID")
    private Long cusId;

    @Schema(description = "房租租赁记录ID")
    private Long leaseId;

    @Schema(description = "收款企业ID")
    private Long receiveCompanyId;

    @Schema(description = "账单名称")
    private String name;

    @Schema(description = "账单业务单号")
    private String orderNo;

    @Schema(description = "交易单号（支付平台返回）")
    private String transactionNo;

    @Schema(description = "交易金额")
    private BigDecimal money;

    @Schema(description = "支付渠道[0=支付宝;1=微信;2=其他]")
    private Integer payChannel;

    @Schema(description = "支付目标[water=水费;electric=电费;plan=付款计划;clean=保洁费;public=公共设施使用费;cost=个性化交易;activity-entry=报名费]")
    private String payTarget;

    @Schema(description = "是否已读[0=否;1=是]")
    private Integer readStatus;

    @Schema(description = "支付方式[0=线上支付;1=线下支付]")
    private Integer payMode;

    @Schema(description = "是否已支付[0=否;1=是]")
    private Integer payStatus;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @Schema(description = "支付计划ID，pay_target=plan时有效")
    private Long payPlanId;

    @Schema(description = "支付计划类型[1=租金计划;2=能耗费计划;3=物业费计划]，pay_target=plan时有效")
    private Integer payPlanType;

    @Schema(description = "预约保洁记录ID，pay_target=clean时有效")
    private Long cleanMakeId;

    @Schema(description = "个性化交易记录ID，pay_target=cost时有效")
    private Long costLogId;

    @Schema(description = "公共设施预约记录ID，pay_target=public时有效")
    private Long publicMakeId;

    @Schema(description = "活动报名记录ID，pay_target=activity-entry时有效")
    private Long activityUserId;

    @Schema(description = "是否使用优惠券[0=未使用;1=已使用]")
    private Integer useCouponFlag;

    @Schema(description = "优惠券id，use_coupon_flag=1时有效")
    private Long couponId;

    @Schema(description = "退款状态[0=未退款;1=已退款]")
    private Integer refundStatus;

    @Schema(description = "退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime refundTime;

    @Schema(description = "账单退款业务单号")
    private String refundOrderNo;

    @Schema(description = "退款金额")
    private BigDecimal refundMoney;

    @Schema(description = "是否已开票[0=否;1=是],pay_status=1时才允许开票")
    private Integer invoiceStatus;

    @Schema(description = "发票ID")
    private Long invoiceId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
