package com.flat.logic.entity.finance;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓账户余额记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_balance_log")
@Schema(description = "公寓账户余额记录表")
public class FlatBalanceLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "房源ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "租赁记录ID")
    private Long leaseId;

    @Schema(description = "户号")
    private String accountNo;

    @Schema(description = "账户类型[1=水费;2=电费]")
    private Integer accountType;

    @Schema(description = "余额变更类型[1=抄表扣费;2=管理员扣除金额;6=充值;7=管理员增添金额]")
    private Integer changeType;

    @Schema(description = "原余额")
    private BigDecimal oldMoney;

    @Schema(description = "现余额")
    private BigDecimal newMoney;

    @Schema(description = "变动金额")
    private BigDecimal amount;

    @Schema(description = "账单ID,change_type=6时有效")
    private Long billId;

    @Schema(description = "抄表记录ID,change_type=1时有效")
    private Long meterLogId;

    @Schema(description = "水费/电费开始值,该值和meter_log表中的begin_value不一定一致,区别为第一次抄表时使用租赁记录中的值,change_type=1时有效")
    private BigDecimal meterBeginValue;

    @Schema(description = "水费/电费结束值,该值和meter_log表中的end_value值一致,change_type=1时有效")
    private BigDecimal meterEndValue;

    @Schema(description = "水费/电费变化数值,change_type=1时有效")
    private BigDecimal meterAmount;

    @Schema(description = "水费/电费单价,该值和meter_log表中的price值一致,change_type=1时有效")
    private BigDecimal meterPrice;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
