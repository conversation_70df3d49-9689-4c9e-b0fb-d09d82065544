package com.flat.logic.entity.activity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 活动
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "活动")
public class Activity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "banner图存储路径")
    private String bannerPath;

    @Schema(description = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @Schema(description = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "报名开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerBeginTime;

    @Schema(description = "报名结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerEndTime;

    @Schema(description = "最大参与人数[0=不限制]")
    private Integer maxAmount;

    @Schema(description = "小程序码图片路径")
    private String qrCodePath;

    @Schema(description = "活动介绍")
    private String description;

    @Schema(description = "活动地点")
    private String address;

    @Schema(description = "公众号推广链接")
    private String wechatPromotionUrl;

    @Schema(description = "是否需要缴报名费[0=免费;1=需要报名费]")
    private Integer entryFeeFlag;

    @Schema(description = "报名费,entry_fee_flag=1时有效")
    private BigDecimal entryFee;

    @Schema(description = "回顾类型[0=未设置回顾;1=图文说明;2=微信公众号url]")
    private Integer reviewType;

    @Schema(description = "回顾内容,review_type=1时有效")
    private String reviewContent;

    @Schema(description = "公众号回顾url,review_type=2时有效")
    private String wechatReviewUrl;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
