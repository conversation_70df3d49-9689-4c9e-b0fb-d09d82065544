package com.flat.logic.entity.lease;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_live")
@Schema(description = "")
public class FlatLive implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "企业ID")
    private Long companyId;

    @Schema(description = "租赁记录ID")
    private Long leaseId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "承租人用户ID")
    private Long hostCusId;

    @Schema(description = "同住人用户ID")
    private Long liveCusId;

    @Schema(description = "合同版本[1=老版本;2=新版本]")
    private Integer contractVersion;

    @Schema(description = "同住合同ID")
    private Long contractId;

    @Schema(description = "合同创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractCreateTime;

    @Schema(description = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractBeginTime;

    @Schema(description = "合同到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractEndTime;

    @Schema(description = "审核状态[0=合同待签署;1=已取消(合同未签署);2=履约中(合同已签署);3=已完成]")
    private Integer status;

    @Schema(description = "撤销时间,status=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @Schema(description = "合同签约时间,status=2/3时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime contractSignTime;

    @Schema(description = "履约结束时间,status=3时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;

    @Schema(description = "拒绝原因")
    private String refuse;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
