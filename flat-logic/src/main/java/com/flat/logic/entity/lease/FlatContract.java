package com.flat.logic.entity.lease;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓合同表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_contract")
@Schema(description = "公寓合同表")
public class FlatContract implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "房源名称")
    private String roomName;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "用户名称")
    private String cusRealName;

    @Schema(description = "用户电话")
    private String cusPhoneNumber;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "签约的企业id")
    private Long companyId;

    @Schema(description = "合同类型[1=公寓租赁合同;2=公寓同住人合同]")
    private Integer type;

    @Schema(description = "房租租赁记录ID,type=1/2时有效")
    private Long leaseId;

    @Schema(description = "同住人记录ID,type=2时有效")
    private Long liveId;

    @Schema(description = "合同编号")
    private String number;

    @Schema(description = "合同名称")
    private String name;

    @Schema(description = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @Schema(description = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "房屋交付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deliveryTime;

    @Schema(description = "付租周期[0=月;1=季度;2=半年;3=年]")
    private Integer payPeriod;

    @Schema(description = "月租金")
    private BigDecimal monthMoney;

    @Schema(description = "租赁时长（月）")
    private Integer monthCount;

    @Schema(description = "总金额")
    private BigDecimal totalMoney;

    @Schema(description = "押金")
    private BigDecimal depositMoney;

    @Schema(description = "月物业费")
    private BigDecimal monthPropertyMoney;

    @Schema(description = "合同签约方式[0=线上签约;1=线下签约]")
    private Integer signMode;

    @Schema(description = "支付方式[0=线上支付;1=线下支付]")
    private Integer payMode;

    @Schema(description = "租赁时长类型[0=短租;1=长租]")
    private Integer durationType;

    @Schema(description = "紧急联系人姓名")
    private String contactName;

    @Schema(description = "紧急联系人电话")
    private String contactPhoneNumber;

    @Schema(description = "送达地址")
    private String deliveryAddress;

    @Schema(description = "合同版本[1=老版本;2=新版本]")
    private Integer version;

    @Schema(description = "居住人数")
    private Integer liveCount;

    @Schema(description = "结算渠道[0=用户自主支付结算;1=渠道结算(政府方代收后结算)]")
    private Integer settleChannel;

    @Schema(description = "合同状态[0=管家已发起合同;1=管家撤回;2=审核拒绝;3=审核同意;4=用户已签约;5=用户拒绝签约;6=已完成]")
    private Integer status;

    @Schema(description = "审核用户客户端[0=管理端;1=管家端]")
    private Integer verifyUserClient;

    @Schema(description = "审核用户ID")
    private Long verifyUserId;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime verifyTime;

    @Schema(description = "审核拒绝原因,state=2时有效")
    private String refuse;

    @Schema(description = "流程ID或签署完成合同ID")
    private String flowId;

    @Schema(description = "签约完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signFinishTime;

    @Schema(description = "用户签署url")
    private String signUrl;

    @Schema(description = "合同文件地址")
    private String fileUrl;

    @Schema(description = "履约结束时间,status=6时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;

    @Schema(description = "是否作废[0=未作废;1=已作废]")
    private Integer invalidFlag;

    @Schema(description = "作废时间,invalid_flag=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @Schema(description = "作废操作管理员id")
    private Long invalidSysUserId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
