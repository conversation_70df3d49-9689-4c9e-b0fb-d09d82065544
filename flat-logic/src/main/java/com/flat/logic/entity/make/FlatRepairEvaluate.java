package com.flat.logic.entity.make;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓报修评价表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_repair_evaluate")
@Schema(description = "公寓报修评价表")
public class FlatRepairEvaluate implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "房屋租赁记录ID")
    private Long leaseId;

    @Schema(description = "报修预约ID")
    private Long repairMakeId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "维修员ID")
    private Long staffId;

    @Schema(description = "及时性")
    private Integer timeliness;

    @Schema(description = "整洁度")
    private Integer tidiness;

    @Schema(description = "评价图片地址")
    private String imgUrl;

    @Schema(description = "平均星数")
    private String average;

    @Schema(description = "用户评价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime evaluateTime;

    @Schema(description = "服务态度")
    private Integer manner;

    @Schema(description = "评价内容")
    private String description;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
