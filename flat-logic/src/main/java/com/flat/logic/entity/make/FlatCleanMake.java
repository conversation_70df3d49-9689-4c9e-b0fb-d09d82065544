package com.flat.logic.entity.make;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓预约保洁表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_clean_make")
@Schema(description = "公寓预约保洁表")
public class FlatCleanMake implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "发起人类型[1=承租人;2=同住人]")
    private Integer applyCusType;

    @Schema(description = "用户/发起者ID")
    private Long cusId;

    @Schema(description = "保洁项目ID")
    private Long cleanTypeId;

    @Schema(description = "房租租赁ID")
    private Long leaseId;

    @Schema(description = "管家ID")
    private Long butlerId;

    @Schema(description = "物业ID")
    private Long propertyId;

    @Schema(description = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime makeTime;

    @Schema(description = "保洁费用")
    private BigDecimal money;

    @Schema(description = "图片地址")
    private String imgUrl;

    @Schema(description = "视频地址")
    private String videoUrl;

    @Schema(description = "是否需要支付[0=否;1=是]")
    private Integer needPayFlag;

    @Schema(description = "收付款方式[0=线上支付;1=线下支付]")
    private Integer payMode;

    @Schema(description = "是否已付费[0=未付费;1=已付费]")
    private Integer payStatus;

    @Schema(description = "预约提交客户端[0=用户端提交;1=管家端提交]")
    private Integer makeClient;

    @Schema(description = "审核状态[0=待审核;1=撤销;2=管家拒绝;3=管家确认;4=物业拒绝;5=物业确认]")
    private Integer status;

    @Schema(description = "用户取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @Schema(description = "管家确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime butlerVerifyTime;

    @Schema(description = "物业确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime propertyVerifyTime;

    @Schema(description = "拒绝原因")
    private String refuse;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;

    @Schema(description = "处理人ID")
    private Long staffId;

    @Schema(description = "保洁完成后照片地址")
    private String finishUrl;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "是否已评价[0=否;1=是]")
    private Integer evaluateFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
