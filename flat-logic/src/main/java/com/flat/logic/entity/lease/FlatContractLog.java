package com.flat.logic.entity.lease;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓合同流程记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_contract_log")
@Schema(description = "公寓合同流程记录表")
public class FlatContractLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓ID")
    private Long flatId;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "租赁记录ID")
    private Long leaseId;

    @Schema(description = "操作类型[0=合同待签署;1=合同撤销;2=审核拒绝;3=审核同意;4=用户已签约;5=用户拒绝签约]")
    private Integer type;

    @Schema(description = "操作类型名称")
    private String typeName;

    @Schema(description = "操作者ID")
    private Long operateId;

    @Schema(description = "操作者名称")
    private String operateName;

    @Schema(description = "拒绝原因或描述")
    private String refuse;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;


}
