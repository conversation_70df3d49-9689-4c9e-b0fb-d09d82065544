package com.flat.logic.entity.flat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "公寓表")
public class Flat implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "公寓名称")
    private String name;

    @Schema(description = "联系方式")
    private String phoneNumber;

    @Schema(description = "公寓地址")
    private String address;

    @Schema(description = "公寓视频地址")
    private String videoUrl;

    @Schema(description = "公寓图片地址")
    private String imgUrl;

    @Schema(description = "公寓设施")
    private String facility;

    @Schema(description = "公用设施")
    private String publicService;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "经纬度")
    private String gnote;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;


}
