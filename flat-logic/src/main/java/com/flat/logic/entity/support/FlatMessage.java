package com.flat.logic.entity.support;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 公寓消息通知表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("flat_message")
@Schema(description = "公寓消息通知表")
public class FlatMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "消息类型[make_room=预约看房;contract=合同;make_success=预约成功;change=处理成功;make_error=预约失败]")
    private String type;

    @Schema(description = "接收者ID")
    private Long receiveId;

    @Schema(description = "消息源ID")
    private Long senderId;

    @Schema(description = "已读状态[0=未读;1=已读]")
    private Integer readStatus;

    @Schema(description = "接收者类型[customer=用户;butler=管家;property=物业]")
    private String receiveType;

    @Schema(description = "发送者类型[customer=用户;butler=管家;property=物业;system=管理员]")
    private String senderType;

    @Schema(description = "跳转地址")
    private String skipUrl;

    @Schema(description = "描述")
    private String content;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer delStatus;


}
