package com.flat.logic.entity.trash;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 用户授权信息表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("cus_oauth")
@Schema(description = "用户授权信息表")
public class CusOauth implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "用户uid")
    private Integer userId;

    @Schema(description = "类型（wxmapp=app、wxapp=微信小程序）")
    private String type;

    @Schema(description = "微信openid")
    private String wxOpenId;

    @Schema(description = "微信开放平台unionid")
    private String wxUnionId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "头像")
    private String head;

    @Schema(description = "性别 1=男 2=女")
    private Integer gender;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}
