package com.flat.logic.entity.account;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 普通用户信息表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("cus_user")
@Schema(description = "普通用户信息表")
public class CusUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    @Schema(description = "微信openid标识")
    private String wxOpenId;

    @Schema(description = "微信unionid标识")
    private String wxUnionId;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "手机号码")
    private String phoneNumber;

    @Schema(description = "微信公众号openid")
    private String wxPublicOpenId;

    @Schema(description = "用户性别（0男 1女 2未知）")
    private Integer gender;

    @Schema(description = "头像地址")
    private String avatar;

    @Schema(description = "帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loginDate;

    @Schema(description = "实名姓名")
    private String realName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "身份证正面图片")
    private String frontUrl;

    @Schema(description = "身份证反面图片")
    private String reverseUrl;

    @Schema(description = "是否实名（0=否，1=是）")
    private Integer isReal;

    @Schema(description = "e签宝psnId")
    private String eSignPsnId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除（0=否，1=是）")
    private Integer delStatus;


}
