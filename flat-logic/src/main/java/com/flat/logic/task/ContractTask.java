package com.flat.logic.task;

import cn.hutool.http.HttpUtil;
import com.flat.logic.dto.resp.FileUploadResp;
import com.flat.logic.entity.account.CusUser;
import com.flat.logic.entity.flat.FlatRoom;
import com.flat.logic.entity.lease.FlatContract;
import com.flat.logic.entity.lease.FlatPayPlan;
import com.flat.logic.mapper.account.CusUserMapper;
import com.flat.logic.mapper.flat.FlatRoomMapper;
import com.flat.logic.mapper.lease.FlatContractMapper;
import com.flat.logic.mapper.lease.FlatPayPlanMapper;
import com.flat.logic.service.cloud.ESignCloudService;
import com.flat.logic.service.cloud.OSSService;
import com.flat.logic.service.cloud.WeChatPublicMessageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ContractTask extends Thread {

    @Resource
    private FlatContractMapper flatContractMapper;

    @Resource
    private ESignCloudService eSignCloudService;

    @Resource
    private OSSService ossService;

    @Resource
    private FlatPayPlanMapper payPlanMapper;

    @Resource
    private CusUserMapper cusUserMapper;

    @Resource
    private FlatRoomMapper roomMapper;

    @Resource
    private WeChatPublicMessageService weChatPublicMessageService;

    @Override
    public void run() {
        log.info("--- ContractTask start ---");
        while (true) {
            try {
                execute();
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                log.warn("labor auto task error", e);
            }
        }
    }

    private void execute() {
        System.out.println("--- ContractTask execute ---");

        // 查询签署完成但文件还未下载的合同
        List<FlatContract> contracts = flatContractMapper.selectList((query) ->
                query.eq(FlatContract::getDelStatus, 0)
                        .eq(FlatContract::getStatus, 4)
                        .isNull(FlatContract::getFileUrl)
        );

        if(!contracts.isEmpty()) {
            for (FlatContract contract : contracts) {
                try {
                    String fileUrl = eSignCloudService.requestSignFileUrl(contract.getFlowId());
                    byte[] bytes = HttpUtil.downloadBytes(fileUrl);
                    FileUploadResp uploadResp = ossService.uploadFile(bytes, "application/pdf", "pdf");
                    contract.setFileUrl(uploadResp.getUrl());
                    contract.setUpdateTime(LocalDateTime.now());
                    flatContractMapper.updateById(contract);
                    log.info("download file and upload to oss success, contractId: {}, flowId: {}, fileUrl: {}", contract.getId(), contract.getFlowId(), contract.getFileUrl());
                } catch (Exception e) {
                    log.warn("download or upload contract file error, contractId: {}, flowId: {}", contract.getId(), contract.getFlowId(), e);
                }
            }
        }

        // 查询已过期并且还未支付的付款计划
        List<FlatPayPlan> plans = payPlanMapper.selectList(q -> q
                .eq(FlatPayPlan::getDelStatus, 0)
                .eq(FlatPayPlan::getPayStatus, 0)
                .eq(FlatPayPlan::getInvalidFlag, 0)
                .eq(FlatPayPlan::getUnpaidNotifyFlag, 0)
                .lt(FlatPayPlan::getEndTime, LocalDateTime.now())
        );

        if(!plans.isEmpty()) {
            for (FlatPayPlan plan : plans) {
                try {

                    CusUser cusUser = cusUserMapper.selectById(plan.getCusId());
                    if(cusUser == null || cusUser.getDelStatus() == 1) {
                        continue;
                    }

                    if (StringUtils.isNotBlank(cusUser.getWxPublicOpenId())) {

                        FlatRoom room = roomMapper.selectById(plan.getRoomId());

                        String type = "其他";
                        if(plan.getType() == 1) {
                            type = plan.getName() + "租金";
                        } else if(plan.getType() == 2) {
                            type = plan.getName() + "能耗费";
                        } else if(plan.getType() == 3) {
                            type = plan.getName() + "商铺物业费";
                        }

                        weChatPublicMessageService.sendMessage(
                                cusUser,
                                "wkIjU91_NFRjohPjr6SKtZR2VHwe7yCLeTkoRuhApfg",
                                null,
                                Map.of(
                                        "thing3", Map.of("value", type),
                                        "thing15", Map.of("value", cusUser.getRealName()),
                                        "thing11", Map.of("value", room.getName()),
                                        "amount26", Map.of("value", plan.getMoney().toString()),
                                        "time17", Map.of("value", plan.getBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ~ " + plan.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                                )
                        );

                        // todo 发送短信通知
                        plan.setUnpaidNotifyFlag(1);
                        plan.setUpdateTime(LocalDateTime.now());
                        payPlanMapper.updateById(plan);
                    }

                } catch (Exception e) {
                    log.warn("send unpaid notify error, planId: {}", plan.getId(), e);
                }
            }
        }

    }
}
