package com.flat.logic.esign.comm;

import com.flat.common.exception.ServiceException;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件转换类
 */
public class ESignFileTransformation {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(ESignFileTransformation.class);

    /**
     * 传入本地文件路径转二进制byte
     *
     * @param srcFilePath 本地文件路径
     */
    public static byte[] fileToBytes(String srcFilePath) throws ServiceException {
        return getBytes(srcFilePath);
    }

    /**
     * 图片转base64
     *
     * @param filePath 本地文件路径
     */
    public static String fileToBase64(String filePath) throws ServiceException {
        byte[] bytes;
        String base64;
        bytes = fileToBytes(filePath);
        base64 = Base64.encodeBase64String(bytes);
        base64 = base64.replaceAll("\r\n", "");
        return base64;
    }



    /***
     * 计算文件内容的Content-MD5
     * @param filePath 文件路径
     */
    public static String getFileContentMD5(String filePath) throws ServiceException {
        // 获取文件MD5的二进制数组（128位）
        byte[] bytes = getFileMD5Bytes128(filePath);
        // 对文件MD5的二进制数组进行base64编码
        return Base64.encodeBase64String(bytes);
    }

    /**
     * 下载文件
     *
     * @param httpUrl 网络文件地址url
     */
    public static boolean downLoadFileByUrl(String httpUrl, String dir) throws ServiceException {
        InputStream fis = null;
        FileOutputStream fileOutputStream = null;
        try {
            URL url = new URL(httpUrl);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.connect();
            fis = httpConn.getInputStream();
            fileOutputStream = new FileOutputStream(dir);

            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer, 0, 1024)) != -1) {
                fileOutputStream.write(buffer, 0, length);
            }
        } catch (IOException e) {
            throw new ServiceException("获取文件流异常", e);
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                logger.warn("关闭文件流异常", e);
            }
        }
        return true;
    }


    /**
     * 网络文件转二进制MD5数组并获取文件大小
     *
     * @param fileUrl 网络文件地址url
     */
    public static Map<String, Object> fileUrlToBytes(String fileUrl) throws ServiceException {
        HashMap<String, Object> map = new HashMap<>();
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.connect();
            InputStream fis = httpConn.getInputStream();
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            outStream.close();
            map.put("fileSize", fis.available());
            byte[] md5Bytes;
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer, 0, 1024)) != -1) {
                md5.update(buffer, 0, length);
                outStream.write(buffer, 0, length);
            }
            md5Bytes = md5.digest();
            byte[] fileData = outStream.toByteArray();
            map.put("fileData", fileData);
            outStream.close();
            fis.close();
            map.put("md5Bytes", md5Bytes);
        } catch (IOException e) {
            throw new ServiceException("获取文件流异常", e);
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException("文件计算异常", e);
        }
        return map;
    }

    /***
     * 获取文件MD5的二进制数组（128位）
     */
    public static byte[] getFileMD5Bytes128(String filePath) throws ServiceException {
        FileInputStream fis = null;
        byte[] md5Bytes = null;
        try {
            File file = new File(filePath);
            fis = new FileInputStream(file);
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[1024];
            int length = -1;
            while ((length = fis.read(buffer, 0, 1024)) != -1) {
                md5.update(buffer, 0, length);
            }
            md5Bytes = md5.digest();
            fis.close();
        } catch (FileNotFoundException e) {
            throw new ServiceException("文件找不到", e);
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException("不支持此算法", e);
        } catch (IOException e) {
            throw new ServiceException("输入流或输出流异常", e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    logger.warn("关闭文件输入流失败", e);
                }
            }
        }
        return md5Bytes;
    }

    /**
     * 根据文件路径，获取文件base64
     */
    public static String getBase64Str(String path) throws ServiceException {
        InputStream is = null;
        try {
            is = new FileInputStream(path);
            byte[] bytes = new byte[is.available()];
            is.read(bytes);
            return Base64.encodeBase64String(bytes);
        } catch (Exception e) {
            throw new ServiceException("获取文件输入流失败", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.warn("关闭文件输入流失败", e);
                }
            }
        }
    }

    /**
     * 获取文件名称
     */
    public static String getFileName(String path) {
        return new File(path).getName();
    }

    /**
     * 获取文件字节流
     */
    public static byte[] getBytes(String filePath) throws ServiceException {
        File file = new File(filePath);
        FileInputStream fis = null;
        byte[] buffer = null;
        try {
            fis = new FileInputStream(file);
            buffer = new byte[(int) file.length()];
            fis.read(buffer);
        } catch (Exception e) {
            throw new ServiceException("获取文件字节流失败", e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    logger.warn("关闭文件字节流失败", e);
                }
            }
        }
        return buffer;
    }

//    public static void main(String[] args) throws ServiceException {
//        System.out.println(getFileContentMD5("D:\\文档\\PLT2022-02124CT.pdf"));
//    }
}
