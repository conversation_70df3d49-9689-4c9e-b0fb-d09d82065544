package com.flat.logic.esign.enums;

public enum ESignHeaderConstant {

    ACCEPT("*/*"),
    DATE(""),
    HEADERS(""),
    CONTENT_TYPE_FORM_DATA("application/x-www-form-urlencoded"),
    CONTENT_TYPE_JSON("application/json; charset=UTF-8"),
    CONTENT_TYPE_PDF("application/pdf"),
    CONTENT_TYPE_STREAM("application/octet-stream"),
    AUTH_MODE("Signature");

    private final String value;

    ESignHeaderConstant(String value) {
        this.value = value;
    }

    public String VALUE() {
        return this.value;
    }
}
