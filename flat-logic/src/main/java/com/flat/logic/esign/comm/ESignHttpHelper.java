package com.flat.logic.esign.comm;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.flat.common.exception.ServiceException;
import com.flat.logic.esign.enums.ESignHeaderConstant;
import com.flat.logic.esign.enums.ESignRequestType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class ESignHttpHelper {
    private static final Logger logger = LoggerFactory.getLogger(ESignHttpHelper.class);

    /**
     * 不允许外部创建实例
     */
    private ESignHttpHelper() {

    }

    /**
     * 发送常规HTTP 请求
     *
     * @param reqType  请求方式
     * @param url      请求路径
     * @param paramStr 请求参数
     */
    public static JSONObject doCommHttp(
            String host,
            String url,
            ESignRequestType reqType,
            Object paramStr,
            Map<String, String> httpHeader
    ) throws ServiceException {
        ESignHttpResponse response = ESignHttpCfgHelper.sendHttp(reqType, host + url, httpHeader, paramStr);
        if(response.getStatus() != 200) {
            throw new ServiceException("签章服务请求失败");
        }

        JSONObject jsonObject = JSON.parseObject(response.getBody());
        if(jsonObject.getIntValue("code") != 0) {
            throw new ServiceException(String.format(
                    "签章服务请求失败(%d, %s)",
                    jsonObject.getIntValue("code"),
                    jsonObject.getString("message")
            ));
        }

        return jsonObject.getJSONObject("data");
    }

    /**
     * 发送文件流上传 HTTP 请求
     *
     * @param reqType        请求方式
     * @param uploadUrl      请求路径
     * @param param          请求参数
     * @param fileContentMd5 文件fileContentMd5
     * @param contentType    文件MIME类型
     */
    public static void doUploadHttp(
            String uploadUrl,
            ESignRequestType reqType,
            byte[] param,
            String fileContentMd5,
            String contentType
    ) throws ServiceException {
        Map<String, String> uploadHeader = buildUploadHeader(fileContentMd5, contentType);
        if (ESign.DEBUG) {
            logger.info("----------------------------start------------------------");
            logger.info("fileContentMd5:{}", fileContentMd5);
            logger.info("contentType:{}", contentType);
        }
        ESignHttpResponse response = ESignHttpCfgHelper.sendHttp(reqType, uploadUrl, uploadHeader, param);
        if(response.getStatus() != 200) {
            throw new ServiceException("签章服务请求失败");
        }

        JSONObject jsonObject = JSON.parseObject(response.getBody());
        if(jsonObject.getIntValue("errCode") != 0) {
            throw new ServiceException(String.format(
                    "签章服务请求失败(%d, %s)",
                    jsonObject.getIntValue("code"),
                    jsonObject.getString("message")
            ));
        }
    }


    /**
     * 构建一个签名鉴权+json数据的esign请求头
     */
    public static Map<String, String> buildSignAndJsonHeader(String projectId, String contentMD5, String accept, String contentType, String authMode) {

        Map<String, String> header = new HashMap<>();
        header.put("X-Tsign-Open-App-Id", projectId);
        header.put("X-Tsign-Open-Version-Sdk", ESignCoreSdkInfo.SdkVersion);
        header.put("X-Tsign-Open-Ca-Timestamp", ESignEncryption.timeStamp());
        header.put("Accept", accept);
        header.put("Content-MD5", contentMD5);
        header.put("Content-Type", contentType);
        header.put("X-Tsign-Open-Auth-Mode", authMode);
        return header;
    }

    /**
     * 签名计算并且构建一个签名鉴权+json数据的esign请求头
     *
     * @param httpMethod *         The name of a supported {@linkplain java.nio.charset.Charset
     *                   *         charset}
     */
    public static Map<String, String> signAndBuildSignAndJsonHeader(
            String projectId,
            String secret,
            ESignRequestType httpMethod,
            String url,
            String paramStr
    ) throws ServiceException {
        String contentMD5;
        if (ESignRequestType.GET == httpMethod || ESignRequestType.DELETE == httpMethod) {
            paramStr = null;
            contentMD5 = "";
        } else if (ESignRequestType.PUT == httpMethod || ESignRequestType.POST == httpMethod) {
            //对body体做md5摘要
            contentMD5 = ESignEncryption.doContentMD5(paramStr);
        } else {
            throw new ServiceException(String.format("不支持的请求方法%s", httpMethod));
        }
        //构造一个初步的请求头
        Map<String, String> esignHeaderMap = buildSignAndJsonHeader(projectId, contentMD5, ESignHeaderConstant.ACCEPT.VALUE(), ESignHeaderConstant.CONTENT_TYPE_JSON.VALUE(), ESignHeaderConstant.AUTH_MODE.VALUE());
        //排序
        url = ESignEncryption.sortApiUrl(url);
        //传入生成的bodyMd5,加上其他请求头部信息拼接成字符串
        String message = ESignEncryption.appendSignDataString(httpMethod, esignHeaderMap.get("Content-MD5"), esignHeaderMap.get("Accept"), esignHeaderMap.get("Content-Type"), esignHeaderMap.get("Headers"), esignHeaderMap.get("Date"), url);
        //整体做sha256签名
        String reqSignature = ESignEncryption.doSignatureBase64(message, secret);
        //请求头添加签名值
        esignHeaderMap.put("X-Tsign-Open-Ca-Signature", reqSignature);
        if (ESign.DEBUG) {
            logger.info("----------------------------start------------------------");
            logger.info("待计算body值:{}", paramStr + "\n");
            logger.info("MD5值:{}", contentMD5 + "\n");
            logger.info("待签名字符串:{}", message + "\n");
            logger.info("签名值:{}", reqSignature + "\n");
        }
        return esignHeaderMap;
    }


    /**
     * 构建一个Token鉴权+jsons数据的esign请求头
     */
    public static Map<String, String> buildTokenAndJsonHeader(String appid, String token) {
        Map<String, String> esignHeader = new HashMap<>();
        esignHeader.put("X-Tsign-Open-Version-Sdk", ESignCoreSdkInfo.SdkVersion);
        esignHeader.put("Content-Type", ESignHeaderConstant.CONTENT_TYPE_JSON.VALUE());
        esignHeader.put("X-Tsign-Open-App-Id", appid);
        esignHeader.put("X-Tsign-Open-Token", token);
        return esignHeader;
    }

    /**
     * 构建一个form表单数据的esign请求头
     */
    public static Map<String, String> buildFormDataHeader(String appid) {
        Map<String, String> esignHeader = new HashMap<>();
        esignHeader.put("X-Tsign-Open-Version-Sdk", ESignCoreSdkInfo.SdkVersion);
        esignHeader.put("X-Tsign-Open-Authorization-Version", "v2");
        esignHeader.put("Content-Type", ESignHeaderConstant.CONTENT_TYPE_FORM_DATA.VALUE());
        esignHeader.put("X-Tsign-Open-App-Id", appid);
        return esignHeader;
    }

    /**
     * 创建文件流上传 请求头
     */
    public static Map<String, String> buildUploadHeader(String fileContentMd5, String contentType) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-MD5", fileContentMd5);
        header.put("Content-Type", contentType);

        return header;
    }
}
