<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.advertise.FlatAdvertiseMapper">

    <resultMap id="RespListResultMap" type="com.flat.logic.dto.resp.advertise.AdvertiseResp" extends="BaseResultMap">
        <result property="typeName" column="type_name" />
    </resultMap>


    <select id="selectRespList" parameterType="com.flat.logic.dto.req.advertise.AdvertiseQueryReq" resultMap="RespListResultMap">
        select a.*, t.name as type_name from flat_advertise a left join flat_advertise_type t on t.id = a.advertise_type_id
        <where>
            a.del_status = 0
            <if test="name != null and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="advertiseTypeId != null">and a.advertise_type_id = #{advertiseTypeId}</if>
        </where>
    </select>
</mapper>
