<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.account.MaiUserMapper">

    <resultMap id="ButlerContactRespResultMap" type="com.flat.logic.dto.resp.account.ButlerContactResp">
        <result property="username" column="username" />
        <result property="phoneNumber" column="phone_number" />
        <result property="address" column="address" />
        <result property="avatar" column="avatar" />
    </resultMap>

    <select id="selectButlerContactList" resultType="com.flat.logic.dto.resp.account.ButlerContactResp">
        select DISTINCT m.avatar, m.username, f.address, m.phone_number
        from flat_lease l
                 left join mai_user m on m.user_id = l.butler_id
                 left join flat f on f.id = m.flat_id
        where l.del_status = 0 and m.del_status = 0 and l.cus_id = #{cusId};
    </select>

</mapper>
