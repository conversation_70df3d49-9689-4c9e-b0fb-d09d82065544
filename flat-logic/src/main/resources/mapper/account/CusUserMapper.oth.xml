<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.account.CusUserMapper">

    <select id="selectBaseList" parameterType="com.flat.logic.dto.req.account.CusUserQueryReq" resultMap="BaseResultMap">
        select * from cus_user
        <where>
            del_status = 0
            <if test="keyword != null and keyword != ''">and (
                nick_name like concat('%', #{keyword}, '%')
                or phone_number like concat('%', #{keyword}, '%')
                or real_name like concat('%', #{keyword}, '%')
                or id_card like concat('%', #{keyword}, '%')
            )</if>
            <if test="realName != null and realName != ''">and real_name like concat('%', #{realName}, '%')</if>
            <if test="idCard != null and idCard != ''">and id_card like concat('%', #{idCard}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''">and phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="isReal != null">and is_real = #{isReal}</if>
        </where>
        order by create_time desc
    </select>

</mapper>
