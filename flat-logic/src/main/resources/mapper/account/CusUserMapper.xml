<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.account.CusUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.account.CusUser">
        <id property="userId" column="user_id" />
        <result property="wxOpenId" column="wx_open_id" />
        <result property="wxUnionId" column="wx_union_id" />
        <result property="nickName" column="nick_name" />
        <result property="phoneNumber" column="phone_number" />
        <result property="wxPublicOpenId" column="wx_public_open_id" />
        <result property="gender" column="gender" />
        <result property="avatar" column="avatar" />
        <result property="status" column="status" />
        <result property="loginIp" column="login_ip" />
        <result property="loginDate" column="login_date" />
        <result property="realName" column="real_name" />
        <result property="idCard" column="id_card" />
        <result property="frontUrl" column="front_url" />
        <result property="reverseUrl" column="reverse_url" />
        <result property="isReal" column="is_real" />
        <result property="eSignPsnId" column="e_sign_psn_id" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
