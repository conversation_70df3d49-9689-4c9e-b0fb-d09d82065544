<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.account.MaiUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.account.MaiUser">
        <id property="userId" column="user_id" />
        <result property="flatId" column="flat_id" />
        <result property="username" column="username" />
        <result property="nickName" column="nick_name" />
        <result property="userType" column="user_type" />
        <result property="wxUnionId" column="wx_union_id" />
        <result property="wxPublicOpenId" column="wx_public_open_id" />
        <result property="permission" column="permission" />
        <result property="phoneNumber" column="phone_number" />
        <result property="gender" column="gender" />
        <result property="avatar" column="avatar" />
        <result property="password" column="password" />
        <result property="status" column="status" />
        <result property="loginIp" column="login_ip" />
        <result property="loginDate" column="login_date" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
