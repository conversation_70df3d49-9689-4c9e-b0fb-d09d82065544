<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatPayPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.lease.FlatPayPlan">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="butlerId" column="butler_id" />
        <result property="leaseId" column="lease_id" />
        <result property="indexNo" column="index_no" />
        <result property="type" column="type" />
        <result property="name" column="name" />
        <result property="money" column="money" />
        <result property="contractId" column="contract_id" />
        <result property="beginTime" column="begin_time" />
        <result property="endTime" column="end_time" />
        <result property="payMode" column="pay_mode" />
        <result property="payStatus" column="pay_status" />
        <result property="certNo" column="cert_no" />
        <result property="certFileUrl" column="cert_file_url" />
        <result property="payTime" column="pay_time" />
        <result property="subletFlag" column="sublet_flag" />
        <result property="subletFromLeaseId" column="sublet_from_lease_id" />
        <result property="subletRentPlanId" column="sublet_rent_plan_id" />
        <result property="invalidFlag" column="invalid_flag" />
        <result property="invalidTime" column="invalid_time" />
        <result property="remark" column="remark" />
        <result property="unpaidNotifyFlag" column="unpaid_notify_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
