<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatSurrenderMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.lease.SurrenderListResp" extends="BaseResultMap">
        <result property="roomName" column="room_name"/>
    </resultMap>


    <select id="selectRespList" parameterType="com.flat.logic.dto.req.lease.SurrenderQueryReq" resultMap="RespResultMap">
        select s.*, r.name as room_name
        from flat_surrender s
            left join flat_room r on r.id = s.room_id
        <where>
            s.del_status=0
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="cusId != null">and s.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and s.cus_real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and s.cus_phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="butlerId != null">and s.butler_id = #{butlerId}</if>
            <if test="status != null">and s.status = #{status}</if>
        </where>
        order by s.create_time desc
    </select>

</mapper>
