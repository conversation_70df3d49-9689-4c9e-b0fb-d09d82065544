<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatLiveMapper">


    <resultMap id="LiveRespResultMap" type="com.flat.logic.dto.resp.lease.LiveResp" extends="BaseResultMap">
        <result column="room_name" property="roomName"/>
        <result column="live_cus_avatar" property="liveCusAvatar"/>
        <result column="live_cus_real_name" property="liveCusRealName"/>
        <result column="live_cus_phone_number" property="liveCusPhoneNumber"/>
        <result column="live_cus_id_card" property="liveCusIdCard"/>
        <result column="file_url" property="fileUrl"/>
    </resultMap>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.lease.LiveQueryReq" resultMap="LiveRespResultMap">
        select
            l.*, r.name as room_name,
            cus.avatar as live_cus_avatar, cus.real_name as live_cus_real_name, cus.phone_number as live_cus_phone_number,
            cus.id_card as live_cus_id_card, fc.file_url
        from flat_live l
            left join flat_room r on r.id = l.room_id
            left join cus_user cus on cus.user_id = l.live_cus_id
            left join flat_contract fc on fc.id = l.contract_id and fc.type = 2
        <where>
            l.del_status = 0
            <if test="flatId != null">and r.flat_id = #{flatId}</if>
            <if test="roomId != null">and l.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="companyId != null">and l.company_id = #{companyId}</if>
            <if test="leaseId != null">and l.lease_id = #{leaseId}</if>
            <if test="butlerId != null">and l.butler_id = #{butlerId}</if>
            <if test="propertyId != null">and l.property_id = #{propertyId}</if>
            <if test="hostCusId != null">and l.host_cus_id = #{hostCusId}</if>
            <if test="liveCusId != null">and l.live_cus_id = #{liveCusId}</if>
            <if test="liveCusRealName != null and liveCusRealName != ''">and cus.real_name like concat('%', #{liveCusRealName}, '%')</if>
            <if test="liveCusPhoneNumber != null and liveCusPhoneNumber != ''">and cus.phone_number like concat('%', #{liveCusPhoneNumber}, '%')</if>
            <if test="status != null">and l.status = #{status}</if>
        </where>
        order by l.create_time desc
    </select>
</mapper>
