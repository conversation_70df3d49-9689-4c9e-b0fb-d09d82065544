<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatLeaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.lease.FlatLease">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="butlerId" column="butler_id" />
        <result property="propertyId" column="property_id" />
        <result property="companyId" column="company_id" />
        <result property="contractId" column="contract_id" />
        <result property="contractVersion" column="contract_version" />
        <result property="contractCreateTime" column="contract_create_time" />
        <result property="contractBeginTime" column="contract_begin_time" />
        <result property="contractEndTime" column="contract_end_time" />
        <result property="contractMonthMoney" column="contract_month_money" />
        <result property="contractMonthCount" column="contract_month_count" />
        <result property="contractTotalMoney" column="contract_total_money" />
        <result property="contractDepositMoney" column="contract_deposit_money" />
        <result property="contractMonthPropertyMoney" column="contract_month_property_money" />
        <result property="payPeriod" column="pay_period" />
        <result property="status" column="status" />
        <result property="contractSignTime" column="contract_sign_time" />
        <result property="cancelTime" column="cancel_time" />
        <result property="settleChannel" column="settle_channel" />
        <result property="renewApplyTime" column="renew_apply_time" />
        <result property="renewFinishTime" column="renew_finish_time" />
        <result property="renewToLeaseId" column="renew_to_lease_id" />
        <result property="subletApplyTime" column="sublet_apply_time" />
        <result property="subletFinishTime" column="sublet_finish_time" />
        <result property="subletToKeepLive" column="sublet_to_keep_live" />
        <result property="subletToLeaseId" column="sublet_to_lease_id" />
        <result property="surrenderApplyTime" column="surrender_apply_time" />
        <result property="surrenderFinishTime" column="surrender_finish_time" />
        <result property="surrenderId" column="surrender_id" />
        <result property="renewFlag" column="renew_flag" />
        <result property="renewFromLeaseId" column="renew_from_lease_id" />
        <result property="subletFlag" column="sublet_flag" />
        <result property="subletFromLeaseId" column="sublet_from_lease_id" />
        <result property="checkInWaterValue" column="check_in_water_value" />
        <result property="checkInElectricValue" column="check_in_electric_value" />
        <result property="waterAccountBalance" column="water_account_balance" />
        <result property="electricAccountBalance" column="electric_account_balance" />
        <result property="invalidFlag" column="invalid_flag" />
        <result property="invalidTime" column="invalid_time" />
        <result property="invalidSysUserId" column="invalid_sys_user_id" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
