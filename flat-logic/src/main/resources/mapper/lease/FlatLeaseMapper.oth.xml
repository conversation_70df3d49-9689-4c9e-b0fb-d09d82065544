<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatLeaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="RespBaseResultMap" type="com.flat.logic.dto.resp.lease.LeaseListResp" extends="BaseResultMap">
        <result property="roomName" column="room_name" />
        <result property="roomThemeUrl" column="room_theme_url" />
        <result property="roomKind" column="room_kind" />
        <result property="waterAccountNo" column="water_account_no" />
        <result property="electricAccountNo" column="electric_account_no" />
        <result property="companyName" column="company_name" />
        <result property="cusAvatar" column="cus_avatar" />
        <result property="cusRealName" column="cus_real_name" />
        <result property="cusPhoneNumber" column="cus_phone_number" />
        <result property="cusIdCard" column="cus_id_card" />
        <result property="contractNumber" column="contract_number" />
        <result property="contractSignMode" column="contract_sign_mode" />
        <result property="contractStatus" column="contract_status" />
        <result property="contractFileUrl" column="contract_file_url" />
        <result property="contractType" column="contract_type" />
    </resultMap>

    <resultMap id="AccountBalanceRespMap" type="com.flat.logic.dto.resp.finance.BalanceListResp">
        <result property="leaseId" column="lease_id"/>
        <result property="roomId" column="room_id"/>
        <result property="roomName" column="room_name"/>
        <result property="roomKind" column="room_kind"/>
        <result property="cusId" column="cus_id"/>
        <result property="cusRealName" column="cus_real_name"/>
        <result property="cusPhoneNumber" column="cus_phone_number"/>
        <result property="type" column="type"/>
        <result property="accountNo" column="account_no"/>
        <result property="balance" column="balance"/>
    </resultMap>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.lease.LeaseQueryReq" resultMap="RespBaseResultMap">
        select
            l.*,
            r.name as room_name, r.theme_url as room_theme_url, r.kind as room_kind, r.water_account_no as water_account_no, r.electric_account_no as electric_account_no,
            co.company_name as company_name,
            cus.avatar as cus_avatar, cus.real_name as cus_real_name, cus.phone_number as cus_phone_number, cus.id_card as cus_id_card,
            c.number as contract_number, c.sign_mode as contract_sign_mode, c.status as contract_status,
            c.file_url as contract_file_url, c.type as contract_type
        from flat_lease l
            left join flat_room r on r.id = l.room_id
            left join flat_contract c on c.id = l.contract_id
            left join flat_company co on co.id = c.company_id
            left join cus_user cus on cus.user_id = c.cus_id
        <where>
            l.del_status = 0
            <if test="keyword != null and keyword != ''">and (r.name like concat('%', #{keyword}, '%') or cus.real_name like concat('%', #{keyword}, '%') or cus.phone_number like concat('%', #{keyword}, '%'))</if>
            <if test="contractId != null">and l.contract_id = #{contractId}</if>
            <if test="contractNumber != null and contractNumber != ''">and c.number like concat('%', #{contractNumber}, '%')</if>
            <if test="roomId != null">and l.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="roomKind != null">and r.kind = #{roomKind}</if>
            <if test="cusId != null">and (l.cus_id = #{cusId} or l.id in (select lease_id from flat_live where live_cus_id = #{cusId} and del_status = 0 and status = 2))</if>
            <if test="cusRealName != null and cusRealName != ''">and cus.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and cus.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="cusIdCard != null and cusIdCard != ''">and cus.id_card like concat('%', #{cusIdCard}, '%')</if>
            <if test="companyId != null">and l.company_id = #{companyId}</if>
            <if test="butlerId != null">and l.butler_id = #{butlerId}</if>
            <if test="status != null">and l.status = #{status}</if>
            <if test="dueDay != null and dueDay > 0">and l.contract_end_time <![CDATA[>=]]> now() and ((UNIX_TIMESTAMP(l.contract_end_time) - UNIX_TIMESTAMP(now())) / (60 * 60 * 24)) <![CDATA[<=]]> ${dueDay}</if>
            <if test="invalidFlag != null">and l.invalid_flag = #{invalidFlag}</if>
        </where>
        order by l.create_time desc
    </select>

    <select id="selectBalanceList" parameterType="com.flat.logic.dto.req.finance.BalanceQueryReq" resultMap="AccountBalanceRespMap">
        select acc.*, cus.real_name as cus_real_name, cus.phone_number as cus_phone_number from (
            (select l.id as lease_id, 1 as type, l.room_id, r.name as room_name, r.kind as room_kind, l.cus_id, r.water_account_no as account_no, l.water_account_balance as balance from flat_lease l left join flat_room r on r.id = l.room_id where l.del_status = 0 and l.`status` in (2, 3, 5, 7))
            union
            (select l.id as lease_id, 2 as type, l.room_id, r.name as room_name, r.kind as room_kind, l.cus_id, r.electric_account_no as account_no, l.electric_account_balance as balance from flat_lease l left join flat_room r on r.id = l.room_id where l.del_status = 0 and l.`status` in (2, 3, 5, 7))
        ) acc LEFT JOIN cus_user cus on cus.user_id = acc.cus_id
        <where>
            <if test="leaseId != null">and acc.lease_id = #{leaseId}</if>
            <if test="roomId != null">and acc.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and acc.room_name like concat('%', #{roomName}, '%')</if>
            <if test="roomKind != null">and acc.room_kind = #{roomKind}</if>
            <if test="cusId != null">and acc.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and cus.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and cus.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="type != null">and acc.type = #{type}</if>
            <if test="accountNo != null and accountNo != ''">and acc.account_no like concat('%', #{accountNo}, '%')</if>
        </where>
        ORDER BY acc.lease_id desc
    </select>
</mapper>
