<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatContractLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.lease.FlatContractLog">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="contractId" column="contract_id" />
        <result property="leaseId" column="lease_id" />
        <result property="type" column="type" />
        <result property="typeName" column="type_name" />
        <result property="operateId" column="operate_id" />
        <result property="operateName" column="operate_name" />
        <result property="refuse" column="refuse" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
