<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatLiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.lease.FlatLive">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="companyId" column="company_id" />
        <result property="leaseId" column="lease_id" />
        <result property="butlerId" column="butler_id" />
        <result property="propertyId" column="property_id" />
        <result property="hostCusId" column="host_cus_id" />
        <result property="liveCusId" column="live_cus_id" />
        <result property="contractVersion" column="contract_version" />
        <result property="contractId" column="contract_id" />
        <result property="contractCreateTime" column="contract_create_time" />
        <result property="contractBeginTime" column="contract_begin_time" />
        <result property="contractEndTime" column="contract_end_time" />
        <result property="status" column="status" />
        <result property="cancelTime" column="cancel_time" />
        <result property="contractSignTime" column="contract_sign_time" />
        <result property="finishTime" column="finish_time" />
        <result property="refuse" column="refuse" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
