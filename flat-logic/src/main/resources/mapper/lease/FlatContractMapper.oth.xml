<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatContractMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.lease.ContractListResp" extends="BaseResultMap">
        <result property="roomThemeUrl" column="room_theme_url" />
        <result property="roomKind" column="room_kind" />
        <result property="roomName" column="room_name" />
        <result property="cusAvatar" column="cus_avatar" />
        <result property="companyName" column="company_name" />
        <result property="cusIdCard" column="cus_id_card" />
    </resultMap>


    <select id="selectRespList" parameterType="com.flat.logic.dto.req.lease.ContractQueryReq" resultMap="RespResultMap">
        select
            fc.*, fr.theme_url as room_theme_url, fr.kind as room_kind, fr.name as room_name, u.avatar as cus_avatar, u.id_card as cus_id_card, cp.company_name
        from flat_contract fc
            left join flat_room fr on fc.room_id = fr.id
            left join cus_user u on fc.cus_id = u.user_id
            left join flat_company cp on fc.company_id = cp.id
        <where>
            fc.del_status = 0
            <if test="keyword != null and keyword != ''">and (fr.name like concat('%', #{keyword}, '%') or u.real_name like concat('%', #{keyword}, '%') or u.phone_number like concat('%', #{keyword}, '%'))</if>
            <if test="number != null and number != ''">and fc.number = #{number}</if>
            <if test="roomId != null">and fc.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and fr.name like concat('%', #{roomName}, '%')</if>
            <if test="roomKind != null">and fr.kind = #{roomKind}</if>
            <if test="cusId != null">and fc.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and u.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and u.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="cusIdCard != null and cusIdCard != ''">and u.id_card like concat('%', #{cusIdCard}, '%')</if>
            <if test="companyId != null">and fc.company_id = #{companyId}</if>
            <if test="butlerId != null">and fc.butler_id = #{butlerId}</if>
            <if test="type != null">and fc.type = #{type}</if>
            <if test="leaseId != null">and fc.lease_id = #{leaseId}</if>
            <if test="liveId != null">and fc.live_id = #{liveId}</if>
            <if test="status != null">and fc.status = #{status}</if>
            <if test="beginDateStart != null">and fc.begin_date <![CDATA[>=]]> #{beginDateStart}</if>
            <if test="beginDateEnd != null">and fc.begin_date <![CDATA[<=]]> #{beginDateEnd}</if>
            <if test="endDateStart != null">and fc.end_date <![CDATA[>=]]> #{endDateStart}</if>
            <if test="endDateEnd != null">and fc.end_date <![CDATA[<=]]> #{endDateEnd}</if>
            <if test="createTimeStart != null">and fc.create_time <![CDATA[>=]]> #{createTimeStart}</if>
            <if test="createTimeEnd != null">and fc.create_time <![CDATA[<=]]> #{createTimeEnd}</if>
            <if test="invalidFlag != null">and fc.invalid_flag = #{invalidFlag}</if>
        </where>
        order by fc.create_time desc
    </select>
</mapper>
