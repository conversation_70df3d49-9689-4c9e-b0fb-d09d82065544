<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatPayPlanMapper">

    <select id="selectBaseList" parameterType="com.flat.logic.dto.req.lease.PayPlanQueryReq" resultMap="BaseResultMap">
        select * from flat_pay_plan
        <where>
            del_status = 0
            <if test="leaseId != null">and lease_id = #{leaseId}</if>
            <if test="contractId != null">and contract_id = #{contractId}</if>
            <if test="cusId != null">and cus_id = #{cusId}</if>
            <if test="type != null">and type = #{type}</if>
        </where>
    </select>

</mapper>
