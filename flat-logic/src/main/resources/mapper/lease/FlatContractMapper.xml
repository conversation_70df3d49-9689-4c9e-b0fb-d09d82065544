<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.lease.FlatContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.lease.FlatContract">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="roomName" column="room_name" />
        <result property="cusId" column="cus_id" />
        <result property="cusRealName" column="cus_real_name" />
        <result property="cusPhoneNumber" column="cus_phone_number" />
        <result property="butlerId" column="butler_id" />
        <result property="companyId" column="company_id" />
        <result property="type" column="type" />
        <result property="leaseId" column="lease_id" />
        <result property="liveId" column="live_id" />
        <result property="number" column="number" />
        <result property="name" column="name" />
        <result property="beginTime" column="begin_time" />
        <result property="endTime" column="end_time" />
        <result property="deliveryTime" column="delivery_time" />
        <result property="payPeriod" column="pay_period" />
        <result property="monthMoney" column="month_money" />
        <result property="monthCount" column="month_count" />
        <result property="totalMoney" column="total_money" />
        <result property="depositMoney" column="deposit_money" />
        <result property="monthPropertyMoney" column="month_property_money" />
        <result property="signMode" column="sign_mode" />
        <result property="payMode" column="pay_mode" />
        <result property="durationType" column="duration_type" />
        <result property="contactName" column="contact_name" />
        <result property="contactPhoneNumber" column="contact_phone_number" />
        <result property="deliveryAddress" column="delivery_address" />
        <result property="version" column="version" />
        <result property="liveCount" column="live_count" />
        <result property="settleChannel" column="settle_channel" />
        <result property="status" column="status" />
        <result property="verifyUserClient" column="verify_user_client" />
        <result property="verifyUserId" column="verify_user_id" />
        <result property="verifyTime" column="verify_time" />
        <result property="refuse" column="refuse" />
        <result property="flowId" column="flow_id" />
        <result property="signFinishTime" column="sign_finish_time" />
        <result property="signUrl" column="sign_url" />
        <result property="fileUrl" column="file_url" />
        <result property="finishTime" column="finish_time" />
        <result property="invalidFlag" column="invalid_flag" />
        <result property="invalidTime" column="invalid_time" />
        <result property="invalidSysUserId" column="invalid_sys_user_id" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
