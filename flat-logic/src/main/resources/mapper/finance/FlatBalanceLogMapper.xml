<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatBalanceLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.finance.FlatBalanceLog">
        <id property="id" column="id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="leaseId" column="lease_id" />
        <result property="accountNo" column="account_no" />
        <result property="accountType" column="account_type" />
        <result property="changeType" column="change_type" />
        <result property="oldMoney" column="old_money" />
        <result property="newMoney" column="new_money" />
        <result property="amount" column="amount" />
        <result property="billId" column="bill_id" />
        <result property="meterLogId" column="meter_log_id" />
        <result property="meterBeginValue" column="meter_begin_value" />
        <result property="meterEndValue" column="meter_end_value" />
        <result property="meterAmount" column="meter_amount" />
        <result property="meterPrice" column="meter_price" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
