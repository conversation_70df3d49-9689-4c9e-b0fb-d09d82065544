<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatInvoiceMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.finance.InvoiceListResp" extends="BaseResultMap">
        <result column="room_name" property="roomName"/>
        <result column="cus_real_name" property="cusRealName"/>
        <result column="cus_phone_number" property="cusPhoneNumber"/>
        <result column="company_name" property="companyName"/>
    </resultMap>

    <sql id="selectRespSQL">
    SELECT 
            i.*,
            r.name as room_name,
            u.real_name as cus_real_name,
            u.phone_number as cus_phone_number,
            c.company_name
        FROM flat_invoice i
            LEFT JOIN flat_room r ON r.id = i.room_id
            LEFT JOIN cus_user u ON u.user_id = i.cus_id
            LEFT JOIN flat_company c ON c.id = i.company_id
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.finance.InvoiceQueryReq" resultMap="RespResultMap">
        <include refid="selectRespSQL" />
        <where>
            i.del_status = 0
            <if test="flatId != null">AND i.flat_id = #{flatId}</if>
            <if test="roomId != null">AND i.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">AND r.name LIKE CONCAT('%', #{roomName}, '%')</if>
            <if test="cusId != null">AND i.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">AND u.real_name LIKE CONCAT('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">AND u.phone_number LIKE CONCAT('%', #{cusPhoneNumber}, '%')</if>
            <if test="companyId != null">AND i.company_id = #{companyId}</if>
            <if test="companyName != null and companyName != ''">AND c.company_name LIKE CONCAT('%', #{companyName}, '%')</if>
            <if test="invoiceType != null">AND i.invoice_type = #{invoiceType}</if>
            <if test="riseType != null">AND i.rise_type = #{riseType}</if>
            <if test="riseName != null and riseName != ''">AND i.rise_name LIKE CONCAT('%', #{riseName}, '%')</if>
            <if test="dutyNo != null and dutyNo != ''">AND i.duty_no LIKE CONCAT('%', #{dutyNo}, '%')</if>
            <if test="registerAddress != null and registerAddress != ''">AND i.register_address LIKE CONCAT('%', #{registerAddress}, '%')</if>
            <if test="registerPhoneNumber != null and registerPhoneNumber != ''">AND i.register_phone_number LIKE CONCAT('%', #{registerPhoneNumber}, '%')</if>
            <if test="openBank != null and openBank != ''">AND i.open_bank LIKE CONCAT('%', #{openBank}, '%')</if>
            <if test="bankAccount != null and bankAccount != ''">AND i.bank_account LIKE CONCAT('%', #{bankAccount}, '%')</if>
            <if test="contactPhoneNumber != null and contactPhoneNumber != ''">AND i.contact_phone_number LIKE CONCAT('%', #{contactPhoneNumber}, '%')</if>
            <if test="email != null and email != ''">AND i.email LIKE CONCAT('%', #{email}, '%')</if>
            <if test="address != null and address != ''">AND i.address LIKE CONCAT('%', #{address}, '%')</if>
            <if test="status != null">AND i.status = #{status}</if>
        </where>
        ORDER BY i.create_time DESC
    </select>

    <!-- selectRespById --> 

    <select id="selectRespById" resultMap="RespResultMap">
        <include refid="selectRespSQL" />
        where i.id = #{id} and i.del_status = 0
    </select>
</mapper>
