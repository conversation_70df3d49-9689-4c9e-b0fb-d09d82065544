<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatBalanceLogMapper">

    <resultMap id="BalanceLogRespMap" type="com.flat.logic.dto.resp.finance.BalanceLogResp">
        <result column="room_name" property="roomName"/>
        <result column="cus_real_name" property="cusRealName"/>
        <result column="cus_phone_number" property="cusPhoneNumber"/>
    </resultMap>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.finance.BalanceLogQueryReq" resultMap="BalanceLogRespMap">
        select 
            l.*, 
            r.name as room_name, 
            cus.real_name as cus_real_name, cus.phone_number as cus_phone_number
        from flat_balance_log l
            left join flat_room r on r.id = l.room_id
            left join cus_user cus on cus.user_id = l.cus_id
        <where>
            l.del_status = 0
            <if test="roomId != null">and l.room_id = #{roomId}</if>
            <if test="cusId != null">and l.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and cus.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and cus.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="leaseId != null">and l.lease_id = #{leaseId}</if>
            <if test="accountNo != null and accountNo != ''">and l.account_no = #{accountNo}</if>
            <if test="accountType != null">and l.account_type = #{accountType}</if>
            <if test="changeType != null">and l.change_type = #{changeType}</if>
            <if test="billId != null">and l.bill_id = #{billId}</if>
            <if test="meterLogId != null">and l.meter_log_id = #{meterLogId}</if>
        </where>
        order by l.create_time desc
    </select>

    <select id="selectWaterFeeByDate" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM flat_balance_log
        WHERE del_status = 0
          AND account_type = 1
          AND DATE(create_time) = #{date}
    </select>

    <select id="selectElectricityFeeByDate" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM flat_balance_log
        WHERE del_status = 0
          AND account_type = 2
          AND DATE(create_time) = #{date}
    </select>

</mapper>
