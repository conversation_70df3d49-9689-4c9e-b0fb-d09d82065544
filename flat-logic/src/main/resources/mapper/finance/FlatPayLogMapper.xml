<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatPayLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.finance.FlatPayLog">
        <id property="id" column="id" />
        <result property="billId" column="bill_id" />
        <result property="channel" column="channel" />
        <result property="outTradeNo" column="out_trade_no" />
        <result property="payStatus" column="pay_status" />
        <result property="payTime" column="pay_time" />
        <result property="transactionNo" column="transaction_no" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
