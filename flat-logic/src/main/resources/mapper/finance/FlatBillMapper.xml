<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.finance.FlatBill">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="leaseId" column="lease_id" />
        <result property="receiveCompanyId" column="receive_company_id" />
        <result property="name" column="name" />
        <result property="orderNo" column="order_no" />
        <result property="transactionNo" column="transaction_no" />
        <result property="money" column="money" />
        <result property="payChannel" column="pay_channel" />
        <result property="payTarget" column="pay_target" />
        <result property="readStatus" column="read_status" />
        <result property="payMode" column="pay_mode" />
        <result property="payStatus" column="pay_status" />
        <result property="payTime" column="pay_time" />
        <result property="payPlanId" column="pay_plan_id" />
        <result property="payPlanType" column="pay_plan_type" />
        <result property="cleanMakeId" column="clean_make_id" />
        <result property="costLogId" column="cost_log_id" />
        <result property="publicMakeId" column="public_make_id" />
        <result property="activityUserId" column="activity_user_id" />
        <result property="useCouponFlag" column="use_coupon_flag" />
        <result property="couponId" column="coupon_id" />
        <result property="refundStatus" column="refund_status" />
        <result property="refundTime" column="refund_time" />
        <result property="refundOrderNo" column="refund_order_no" />
        <result property="refundMoney" column="refund_money" />
        <result property="invoiceStatus" column="invoice_status" />
        <result property="invoiceId" column="invoice_id" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
