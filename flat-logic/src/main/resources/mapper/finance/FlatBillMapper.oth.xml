<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatBillMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.finance.BillResp" extends="BaseResultMap">
        <result property="roomName" column="room_name" />
        <result property="roomBlockNo" column="room_block_no" />
        <result property="cusRealName" column="cus_real_name" />
        <result property="cusPhoneNumber" column="cus_phone_number" />
        <result property="couponName" column="coupon_name" />
        <result property="couponAmount" column="coupon_amount" />
    </resultMap>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.finance.BillQueryReq" resultMap="RespResultMap">
        select
            b.*, r.name as room_name, cu.real_name as cus_real_name, cu.phone_number as cus_phone_number,
            fc.company_name, r.block_no as room_block_no,
            cp.name as coupon_name, cp.amount as coupon_amount
        from flat_bill b
            left join cus_user cu on cu.user_id = b.cus_id
            left join flat_room r on r.id = b.room_id
            left join flat_company fc on fc.id = b.receive_company_id
            left join coupon cp on cp.id = b.coupon_id
        <where>
            b.del_status = 0
            <!--基于BillQueryReq进行条件查询-->
            <if test="orderNo != null and orderNo != ''">and b.order_no like concat('%', #{orderNo}, '%')</if>
            <if test="flatId != null">and b.flat_id = #{flatId}</if>
            <if test="roomId != null">and b.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="cusId != null">and b.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and cu.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and cu.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="payStatus != null">and b.pay_status = #{payStatus}</if>
            <if test="payTarget != null and payTarget != ''">and b.pay_target = #{payTarget}</if>
            <if test="payPlanType != null">and b.pay_plan_type = #{payPlanType}</if>
            <if test="invoiceStatus != null and invoiceStatus != ''">and b.invoice_status = #{invoiceStatus}</if>
            <if test="createTimeStart != null">AND b.create_time <![CDATA[>=]]> #{createTimeStart}</if>
            <if test="createTimeEnd != null">AND b.create_time <![CDATA[<=]]> #{createTimeEnd}</if>
            <if test="payTimeStart != null">AND b.pay_time <![CDATA[>=]]> #{payTimeStart}</if>
            <if test="payTimeEnd != null">AND b.pay_time <![CDATA[<=]]> #{payTimeEnd}</if>
        </where>
        order by b.pay_time desc, b.create_time desc
    </select>


    <select id="selectCleanFeeByDate" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(money), 0)
        FROM flat_bill
        WHERE del_status = 0
          AND pay_target = 'clean'
          AND pay_status = 1
          AND DATE(create_time) = #{date}
    </select>

    <select id="statTotalMoney" resultType="java.math.BigDecimal">
        select (
                (
                    select ifnull(sum(money), 0) from flat_bill where del_status = 0
                        <if test="leaseId != null">and lease_id = #{leaseId}</if>
                )
                -
                (
                    select ifnull(sum(refund_money), 0) from flat_bill where del_status = 0 and refund_status = 1
                        <if test="leaseId != null">and lease_id = #{leaseId}</if>
                )
        ) as money
    </select>

    <select id="statInvoicedMoney" resultType="java.math.BigDecimal">
        select IFNULL(sum(money), 0) as money from flat_bill where invoice_status = 1 and del_status = 0
            <if test="leaseId != null">and lease_id = #{leaseId}</if>
    </select>


</mapper>
