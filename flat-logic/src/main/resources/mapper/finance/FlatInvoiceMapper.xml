<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.finance.FlatInvoiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.finance.FlatInvoice">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="leaseId" column="lease_id" />
        <result property="companyId" column="company_id" />
        <result property="invoiceType" column="invoice_type" />
        <result property="riseType" column="rise_type" />
        <result property="riseName" column="rise_name" />
        <result property="dutyNo" column="duty_no" />
        <result property="registerAddress" column="register_address" />
        <result property="registerPhoneNumber" column="register_phone_number" />
        <result property="openBank" column="open_bank" />
        <result property="bankAccount" column="bank_account" />
        <result property="contactPhoneNumber" column="contact_phone_number" />
        <result property="email" column="email" />
        <result property="address" column="address" />
        <result property="status" column="status" />
        <result property="amount" column="amount" />
        <result property="openTime" column="open_time" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
