<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.flat.FlatRoomMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.flat.FlatRoom">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="butlerId" column="butler_id" />
        <result property="propertyId" column="property_id" />
        <result property="kind" column="kind" />
        <result property="name" column="name" />
        <result property="blockNo" column="block_no" />
        <result property="totalFloorNo" column="total_floor_no" />
        <result property="floorNo" column="floor_no" />
        <result property="roomNo" column="room_no" />
        <result property="houseType" column="house_type" />
        <result property="acreage" column="acreage" />
        <result property="orientation" column="orientation" />
        <result property="price" column="price" />
        <result property="themeUrl" column="theme_url" />
        <result property="videoUrl" column="video_url" />
        <result property="imgUrl" column="img_url" />
        <result property="lightspot" column="lightspot" />
        <result property="traffic" column="traffic" />
        <result property="address" column="address" />
        <result property="status" column="status" />
        <result property="label" column="label" />
        <result property="roomType" column="room_type" />
        <result property="cusId" column="cus_id" />
        <result property="cusTmpId" column="cus_tmp_id" />
        <result property="gnote" column="gnote" />
        <result property="waterAccountNo" column="water_account_no" />
        <result property="waterMeterValue" column="water_meter_value" />
        <result property="waterMeterLastTime" column="water_meter_last_time" />
        <result property="electricAccountNo" column="electric_account_no" />
        <result property="electricMeterValue" column="electric_meter_value" />
        <result property="electricMeterLastTime" column="electric_meter_last_time" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
