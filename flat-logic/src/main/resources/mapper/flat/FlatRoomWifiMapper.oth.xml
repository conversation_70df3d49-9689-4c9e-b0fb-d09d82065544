<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.flat.FlatRoomWifiMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.flat.RoomWifiResp" extends="BaseResultMap">
        <result column="room_name" property="roomName"/>
    </resultMap>

    <select id="queryRoomWifiWithRoomName" resultMap="RespResultMap">
        SELECT 
            w.*,
            r.name
        FROM 
            flat_room_wifi w
        LEFT JOIN 
            flat_room r ON w.room_id = r.id
        WHERE 
            w.del_status = 0
    </select>

    <select id="selectResp" parameterType="com.flat.logic.dto.req.flat.RoomWifiQueryReq" resultMap="RespResultMap">
        SELECT 
            w.*,
            r.room_name
        FROM 
            flat_room_wifi w
        LEFT JOIN 
            flat_room r ON w.room_id = r.id
        WHERE 
            w.del_status = 0
        <if test="flatId != null">AND w.flat_id = #{flatId}</if>
        <if test="roomId != null">AND w.room_id = #{roomId}</if>
        <if test="account != null and account != ''">AND w.account LIKE CONCAT('%', #{account}, '%')</if>
    </select>
</mapper>
