<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.flat.FlatMapper">

    <select id="selectBaseList" parameterType="com.flat.logic.dto.req.flat.FlatQueryReq" resultMap="BaseResultMap">
        select * from flat
        <where>
            del_status = 0
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''">and phone_number like concat('%', #{phoneNumber}, '%')</if>
        </where>
    </select>

</mapper>
