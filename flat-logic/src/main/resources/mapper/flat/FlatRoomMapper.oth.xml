<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.flat.FlatRoomMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.flat.RoomResp" extends="BaseResultMap">
        <result property="flatName" column="flat_name" />
        <result property="flatPhoneNumber" column="flat_phone_number" />
        <result property="butlerName" column="butler_name" />
        <result property="companyId" column="company_id" />
        <result property="companyName" column="company_name" />
        <result property="cusId" column="cus_id" />
        <result property="cusRealName" column="cus_real_name" />
        <result property="cusPhoneNumber" column="cus_phone_number" />
        <result property="contractStatus" column="contract_status" />
    </resultMap>

    <resultMap id="DetailResultMap" type="com.flat.logic.dto.resp.flat.RoomDetailResp" extends="RespResultMap">
        <result property="rentArrears" column="rent_arrears" />
        <result property="energyArrears" column="energy_arrears" />
        <result property="propertyArrears" column="property_arrears" />
        <result property="waterArrears" column="water_arrears" />
        <result property="electricArrears" column="electric_arrears" />
        <result property="rentArrearsAmount" column="rent_arrears_amount" />
        <result property="energyArrearsAmount" column="energy_arrears_amount" />
        <result property="propertyArrearsAmount" column="property_arrears_amount" />
        <result property="waterArrearsAmount" column="water_arrears_amount" />
        <result property="electricArrearsAmount" column="electric_arrears_amount" />
        <result property="contractEndTime" column="contract_end_time" />
        <result property="remainingDays" column="remaining_days" />
        <result property="contractStatus" column="contract_status" />
    </resultMap>

    <resultMap id="RoomStateModelResultMap" type="com.flat.logic.model.RoomStateModel">
        <id property="roomKind" column="room_kind" />
        <result property="roomId" column="room_id" />
        <result property="roomName" column="room_name" />
        <result property="blockNo" column="block_no" />
        <result property="floorNo" column="floor_no" />
        <result property="roomNo" column="room_no" />
        <result property="orientation" column="orientation" />
        <result property="roomStatus" column="room_status" />
        <result property="leaseId" column="lease_id" />
        <result property="leaseStatus" column="lease_status" />
        <result property="cusId" column="cus_id" />
        <result property="cusRealName" column="cus_real_name" />
        <result property="companyId" column="company_id" />
        <result property="companyName" column="company_name" />
        <result property="rentArrears" column="rent_arrears" />
        <result property="energyArrears" column="energy_arrears" />
        <result property="propertyArrears" column="property_arrears" />
        <result property="waterArrears" column="water_arrears" />
        <result property="electricArrears" column="electric_arrears" />
        <result property="contractEndTime" column="contract_end_time" />
    </resultMap>

    <sql id="RespSelectSQL">
        select
            r.*,
            f.name as flat_name, f.phone_number as flat_phone_number,
            butler.username as butler_name,
            fc.id as company_id, fc.company_name,
            cus.user_id as cus_id, cus.real_name as cus_real_name, cus.phone_number as cus_phone_number,
            ct.status as contract_status
        from flat_room r
                 left join flat f on f.id = r.flat_id
                 left join mai_user butler on butler.user_id = r.butler_id and butler.user_type='BUTLER'
                 left join (select * from flat_lease where status in (0, 2, 3, 5, 7) and del_status = 0) l on l.room_id = r.id
                 left join cus_user cus on cus.user_id = l.cus_id and cus.del_status = 0
                 left join flat_company fc on fc.id = l.company_id
                left join flat_contract ct on ct.lease_id = l.id and ct.type = 1 and ct.del_status = 0
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.flat.RoomQueryReq" resultMap="RespResultMap">
        <include refid="RespSelectSQL" />
        <where>
            r.del_status = 0
            <if test="companyId != null">and fc.id = #{companyId}</if>
            <if test="cusId != null">and l.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and cus.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and cus.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="companyId != null">and l.company_id = #{companyId}</if>
            <if test="flatId != null">and r.flat_id = #{flatId}</if>
            <if test="butlerId != null">and r.butler_id = #{butlerId}</if>
            <if test="propertyId != null">and r.property_id = #{propertyId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="blockNo != null and blockNo != ''">and r.block_no = #{blockNo}</if>
            <if test="houseType != null and houseType != ''">and r.house_type = #{houseType}</if>
            <if test="status != null">and r.status = #{status}</if>
            <if test="roomType != null">and r.room_type = #{roomType}</if>
        </where>
    </select>

    <select id="selectRespById" resultMap="RespResultMap">
        <include refid="RespSelectSQL" />
        where r.id = #{id} and r.del_status = 0
    </select>

    <select id="selectDetailById" resultMap="DetailResultMap">
        select
            r.*,
            f.name as flat_name, f.phone_number as flat_phone_number,
            butler.username as butler_name,
            fc.id as company_id, fc.company_name,
            cus.user_id as cus_id, cus.real_name as cus_real_name, cus.phone_number as cus_phone_number,
            ct.status as contract_status,
            fl.contract_end_time,
            -- 租期剩余天数
            CASE
                WHEN r.status = 1 AND fl.contract_end_time IS NOT NULL AND fl.contract_end_time <![CDATA[ > ]]> NOW()
                THEN DATEDIFF(fl.contract_end_time, NOW())
                WHEN r.status = 1 AND fl.contract_end_time IS NOT NULL AND fl.contract_end_time <![CDATA[ <= ]]> NOW()
                THEN 0
                ELSE NULL
            END as remaining_days,
            -- 租金欠费状态
            CASE WHEN EXISTS (
                SELECT 1 FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 1 -- 租金计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ) THEN 1 ELSE 0 END as rent_arrears,
            -- 能耗费欠费状态
            CASE WHEN EXISTS (
                SELECT 1 FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 2 -- 能耗费计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ) THEN 1 ELSE 0 END as energy_arrears,
            -- 物业费欠费状态
            CASE WHEN EXISTS (
                SELECT 1 FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 3 -- 物业费计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ) THEN 1 ELSE 0 END as property_arrears,
            -- 水费欠费状态
            CASE WHEN fl.water_account_balance <![CDATA[ < ]]> 0 THEN 1 ELSE 0 END as water_arrears,
            -- 电费欠费状态
            CASE WHEN fl.electric_account_balance <![CDATA[ < ]]> 0 THEN 1 ELSE 0 END as electric_arrears,
            -- 租金欠费金额
            IFNULL((
                SELECT SUM(pp.money)
                FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 1 -- 租金计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ), 0) as rent_arrears_amount,
            -- 能耗费欠费金额
            IFNULL((
                SELECT SUM(pp.money)
                FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 2 -- 能耗费计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ), 0) as energy_arrears_amount,
            -- 物业费欠费金额
            IFNULL((
                SELECT SUM(pp.money)
                FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 3 -- 物业费计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ), 0) as property_arrears_amount,
            -- 水费欠费金额
            CASE WHEN fl.water_account_balance <![CDATA[ < ]]> 0 THEN ABS(fl.water_account_balance) ELSE 0 END as water_arrears_amount,
            -- 电费欠费金额
            CASE WHEN fl.electric_account_balance <![CDATA[ < ]]> 0 THEN ABS(fl.electric_account_balance) ELSE 0 END as electric_arrears_amount
        from flat_room r
            left join flat f on f.id = r.flat_id
            left join mai_user butler on butler.user_id = r.butler_id and butler.user_type='BUTLER'
            left join (select * from flat_lease where status in (0, 2, 3, 5, 7) and del_status = 0) fl on fl.room_id = r.id
            left join cus_user cus on cus.user_id = fl.cus_id and cus.del_status = 0
            left join flat_company fc on fc.id = fl.company_id
            left join flat_contract ct on ct.lease_id = fl.id and ct.type = 1 and ct.del_status = 0
        where r.id = #{id} and r.del_status = 0
    </select>

    <select id="selectNotFreeList" resultMap="RespResultMap">
        <include refid="RespSelectSQL" />
        <where>
            r.del_status = 0 and r.status in (1,2,3,4)
            <if test="content != null and content != ''">and r.name like concat('%', #{content}, '%')</if>
        </where>
    </select>

    <select id="selectLeasedList" resultType="com.flat.logic.entity.flat.FlatRoom">
        select r.* from flat_room r left join flat_lease l on l.room_id = r.id
        <where>
            r.del_status = 0
            <if test="cusId != null">l.cus_id = #{cusId}</if>
        </where>
    </select>

    <select id="selectStates" parameterType="com.flat.logic.dto.req.flat.RoomStateQueryReq" resultMap="RoomStateModelResultMap">
        select
            fr.kind as room_kind, fr.id as room_id, fr.`name` as room_name, fr.block_no, fr.floor_no, fr.room_no, fr.orientation, fr.`status` as room_status,
            fl.id as lease_id, fl.`status` as lease_status, fl.cus_id as cus_id, cu.real_name as cus_real_name,
            fl.company_id, fc.company_name,
            -- 租金欠费状态
            CASE WHEN EXISTS (
                SELECT 1 FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 1 -- 租金计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ) THEN 1 ELSE 0 END as rent_arrears,
            -- 能耗费欠费状态
            CASE WHEN EXISTS (
                SELECT 1 FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 2 -- 能耗费计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ) THEN 1 ELSE 0 END as energy_arrears,
            -- 物业费欠费状态
            CASE WHEN EXISTS (
                SELECT 1 FROM flat_pay_plan pp
                WHERE pp.lease_id = fl.id
                AND pp.type = 3 -- 物业费计划
                AND pp.pay_status = 0 -- 未支付
                AND pp.invalid_flag = 0 -- 未作废
                AND pp.del_status = 0 -- 未删除
                AND pp.end_time <![CDATA[ < ]]> NOW() -- 已过期
            ) THEN 1 ELSE 0 END as property_arrears,
            -- 水费欠费状态
            CASE WHEN fl.water_account_balance <![CDATA[ < ]]> 0 THEN 1 ELSE 0 END as water_arrears,
            -- 电费欠费状态
            CASE WHEN fl.electric_account_balance <![CDATA[ < ]]> 0 THEN 1 ELSE 0 END as electric_arrears,
            -- 合同结束时间
            fl.contract_end_time
        from flat_room fr
            left join flat_lease fl on fl.room_id = fr.id and fl.`status` in (0, 2, 7) and fl.invalid_flag = 0 and fl.del_status = 0
            left join cus_user cu on cu.user_id = fl.cus_id and cu.del_status = 0
            left join flat_company fc on fc.id = fl.company_id and fc.del_status = 0
        <where>
            fr.del_status = 0
            <if test="kind != null">and fr.kind = #{kind}</if>
            <if test="blockNo != null and blockNo != ''">and fr.block_no = #{blockNo}</if>
            <if test="floorNo != null">and fr.floor_no = #{floorNo}</if>
            <if test="roomNo != null and roomNo != ''">and fr.room_no = #{roomNo}</if>
            <if test="orientation != null">and fr.orientation = #{orientation}</if>
            <if test="roomStatus != null">and fr.status = #{roomStatus}</if>
        </where>
        order by block_no ASC, floor_no ASC
    </select>

    <select id="selectDistinctBlockNos" resultType="java.lang.String">
        SELECT DISTINCT block_no
        FROM flat_room
        WHERE del_status = 0
    </select>

    <select id="selectDistinctFloorsByBlock" resultType="java.lang.Integer">
        SELECT DISTINCT floor_no
        FROM flat_room
        WHERE del_status = 0
        AND block_no = #{blockNo}
        ORDER BY floor_no
    </select>

    <resultMap id="FinanceRespResultMap" type="com.flat.logic.dto.resp.flat.RoomFinanceResp" extends="BaseResultMap">
        <result property="paidBillAmount" column="paid_bill_amount" />
        <result property="invoicedAmount" column="invoiced_amount" />
        <result property="uninvoicedAmount" column="uninvoiced_amount" />
    </resultMap>

    <select id="selectFinanceRespList" parameterType="com.flat.logic.dto.req.flat.RoomQueryReq" resultMap="FinanceRespResultMap">
        select
            r.*,
            -- 已支付账单总额
            IFNULL((
                SELECT SUM(b.money) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                FROM flat_bill b
                WHERE b.room_id = r.id
                AND b.pay_status = 1
                AND b.del_status = 0
            ), 0) as paid_bill_amount,
            -- 已开票总额
            IFNULL((
                SELECT SUM(b.money)
                FROM flat_bill b
                WHERE b.room_id = r.id
                AND b.invoice_status = 1
                AND b.pay_status = 1
                AND b.del_status = 0
            ), 0) as invoiced_amount,
            -- 未开票总额
            IFNULL((
                SELECT SUM(b.money) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                FROM flat_bill b
                WHERE b.room_id = r.id
                AND b.pay_status = 1
                AND b.invoice_status = 0
                AND b.del_status = 0
            ), 0) as uninvoiced_amount
        from flat_room r
        <where>
            r.del_status = 0
            <if test="flatId != null">and r.flat_id = #{flatId}</if>
            <if test="butlerId != null">and r.butler_id = #{butlerId}</if>
            <if test="propertyId != null">and r.property_id = #{propertyId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="blockNo != null and blockNo != ''">and r.block_no = #{blockNo}</if>
            <if test="houseType != null and houseType != ''">and r.house_type = #{houseType}</if>
            <if test="status != null">and r.status = #{status}</if>
            <if test="roomType != null">and r.room_type = #{roomType}</if>
        </where>
        order by r.create_time desc
    </select>

    <select id="selectFinanceExportList" parameterType="com.flat.logic.dto.req.flat.RoomQueryReq" resultMap="FinanceRespResultMap">
        select
            r.*,
            -- 已支付账单总额
            IFNULL((
                SELECT SUM(b.money) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                FROM flat_bill b
                WHERE b.room_id = r.id
                AND b.pay_status = 1
                AND b.del_status = 0
            ), 0) as paid_bill_amount,
            -- 已开票总额
            IFNULL((
                SELECT SUM(b.money)
                FROM flat_bill b
                WHERE b.room_id = r.id
                AND b.invoice_status = 1
                AND b.pay_status = 1
                AND b.del_status = 0
            ), 0) as invoiced_amount,
            -- 未开票总额
            IFNULL((
                SELECT SUM(b.money) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                FROM flat_bill b
                WHERE b.room_id = r.id
                AND b.pay_status = 1
                AND b.invoice_status = 0
                AND b.del_status = 0
            ), 0) as uninvoiced_amount
        from flat_room r
        <where>
            r.del_status = 0
            <if test="flatId != null">and r.flat_id = #{flatId}</if>
            <if test="butlerId != null">and r.butler_id = #{butlerId}</if>
            <if test="propertyId != null">and r.property_id = #{propertyId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="blockNo != null and blockNo != ''">and r.block_no = #{blockNo}</if>
            <if test="houseType != null and houseType != ''">and r.house_type = #{houseType}</if>
            <if test="status != null">and r.status = #{status}</if>
            <if test="roomType != null">and r.room_type = #{roomType}</if>
        </where>
        order by r.create_time desc
    </select>

</mapper>
