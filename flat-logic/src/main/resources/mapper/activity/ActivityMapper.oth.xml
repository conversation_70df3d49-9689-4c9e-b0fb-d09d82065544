<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.activity.ActivityMapper">

    <resultMap id="ActivityRespMap" type="com.flat.logic.dto.resp.activity.ActivityResp" extends="BaseResultMap">
        <result column="join_count" property="joinCount"/>
        <result column="paid_count" property="paidCount"/>
        <result column="process_status" property="processStatus"/>
    </resultMap>

    <sql id="selectActivityVo">
        SELECT a.*,
               COUNT(au.id) AS join_count,
               SUM(CASE WHEN au.pay_status = 1 THEN 1 ELSE 0 END) AS paid_count,
               CASE 
                   WHEN NOW() <![CDATA[ < ]]> a.begin_time THEN 0
                   WHEN NOW() <![CDATA[ > ]]> a.end_time THEN 2
                   ELSE 1
               END AS process_status
        FROM activity a
        LEFT JOIN activity_user au ON a.id = au.activity_id AND au.del_status = 0
    </sql>

    <select id="selectActivityList" parameterType="com.flat.logic.dto.req.activity.ActivityQueryReq" resultMap="ActivityRespMap">
        <include refid="selectActivityVo"/>
        <where>
            a.del_status = 0
            <if test="req.name != null and req.name != ''">
                AND a.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.beginTimeStart != null">
                AND a.begin_time <![CDATA[ >= ]]> #{req.beginTimeStart}
            </if>
            <if test="req.beginTimeEnd != null">
                AND a.begin_time <![CDATA[ <= ]]> #{req.beginTimeEnd}
            </if>
            <if test="req.endTimeStart != null">
                AND a.end_time <![CDATA[ >= ]]> #{req.endTimeStart}
            </if>
            <if test="req.endTimeEnd != null">
                AND a.end_time <![CDATA[ <= ]]> #{req.endTimeEnd}
            </if>
            <if test="req.entryFeeFlag != null">
                AND a.entry_fee_flag = #{req.entryFeeFlag}
            </if>
            <if test="!req.includeExpired">
                AND a.end_time <![CDATA[ > ]]> NOW()
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.create_time DESC
    </select>

    <select id="selectActivityDetail" parameterType="Long" resultMap="ActivityRespMap">
        <include refid="selectActivityVo"/>
        <where>
            a.id = #{id}
            AND a.del_status = 0
        </where>
        GROUP BY a.id
    </select>

</mapper> 