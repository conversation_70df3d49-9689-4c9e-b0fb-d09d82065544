<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.activity.ActivityUserMapper">

    <resultMap id="ActivityUserRespMap" type="com.flat.logic.dto.resp.activity.ActivityUserResp" extends="BaseResultMap">
        <result column="activity_name" property="activityName"/>
        <result column="activity_begin_time" property="activityBeginTime"/>
        <result column="activity_end_time" property="activityEndTime"/>
        <result column="process_status" property="processStatus"/>
    </resultMap>

    <sql id="selectActivityUserVo">
        SELECT au.*,
               a.name AS activity_name,
               a.begin_time AS activity_begin_time,
               a.end_time AS activity_end_time,
               CASE 
                   WHEN NOW() <![CDATA[ < ]]> a.begin_time THEN 0
                   WHEN NOW() <![CDATA[ > ]]> a.end_time THEN 2
                   ELSE 1
               END AS process_status
        FROM activity_user au
        LEFT JOIN activity a ON au.activity_id = a.id AND a.del_status = 0
    </sql>

    <select id="selectUserActivityDetail" parameterType="Long" resultMap="ActivityUserRespMap">
        <include refid="selectActivityUserVo"/>
        <where>
            au.id = #{userActivityId}
            AND au.del_status = 0
        </where>
    </select>

    <select id="selectUserJoinActivity" parameterType="Long" resultMap="ActivityUserRespMap">
        <include refid="selectActivityUserVo"/>
        <where>
            au.cus_id = #{cusId}
            AND au.activity_id = #{activityId}
            AND au.del_status = 0
        </where>
        LIMIT 1
    </select>
    
    <select id="selectActivityUserList" parameterType="com.flat.logic.dto.req.activity.ActivityUserQueryReq" resultMap="ActivityUserRespMap">
        <include refid="selectActivityUserVo"/>
        <where>
            au.del_status = 0
            <if test="req.activityId != null">
                AND au.activity_id = #{req.activityId}
            </if>
            <if test="req.cusId != null">
                AND au.cus_id = #{req.cusId}
            </if>
            <if test="req.realName != null and req.realName != ''">
                AND au.real_name LIKE CONCAT('%', #{req.realName}, '%')
            </if>
            <if test="req.phoneNumber != null and req.phoneNumber != ''">
                AND au.phone_number LIKE CONCAT('%', #{req.phoneNumber}, '%')
            </if>
            <if test="req.entryFeeFlag != null">
                AND au.entry_fee_flag = #{req.entryFeeFlag}
            </if>
            <if test="req.payStatus != null">
                AND au.pay_status = #{req.payStatus}
            </if>
            <if test="req.payMode != null">
                AND au.pay_mode = #{req.payMode}
            </if>
            <if test="req.beginCreateTime != null">
                AND au.create_time <![CDATA[ >= ]]> #{req.beginCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND au.create_time <![CDATA[ <= ]]> #{req.endCreateTime}
            </if>
        </where>
        ORDER BY au.create_time DESC
    </select>

</mapper> 