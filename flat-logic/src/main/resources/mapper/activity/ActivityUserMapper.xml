<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.activity.ActivityUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.activity.ActivityUser">
        <id property="id" column="id" />
        <result property="activityId" column="activity_id" />
        <result property="cusId" column="cus_id" />
        <result property="realName" column="real_name" />
        <result property="phoneNumber" column="phone_number" />
        <result property="entryFeeFlag" column="entry_fee_flag" />
        <result property="entryFee" column="entry_fee" />
        <result property="payStatus" column="pay_status" />
        <result property="payMode" column="pay_mode" />
        <result property="payTime" column="pay_time" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
