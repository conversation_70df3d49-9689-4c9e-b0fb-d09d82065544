<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.statistics.StatisticsMapper">

    <select id="selectRoomInvoiceStatistics" resultType="com.flat.logic.dto.resp.statistics.RoomInvoiceStatisticsResp">
        SELECT
            r.id AS roomId,
            r.name AS roomName,
            r.room_no AS roomNo,
            r.block_no AS blockNo,
            r.floor_no AS floorNo,
            IFNULL(
                (
                    SELECT IFNULL(SUM(b.money), 0) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                    FROM flat_bill b
                    WHERE b.room_id = r.id
                    AND b.pay_status = 1
                    AND b.del_status = 0
                ), 0
            ) AS totalMoney,
            IFNULL(
                (
                    SELECT IFNULL(SUM(b.money), 0)
                    FROM flat_bill b
                    WHERE b.room_id = r.id
                    AND b.invoice_status = 1
                    AND b.pay_status = 1
                    AND b.del_status = 0
                ), 0
            ) AS invoicedMoney,
            IFNULL(
                (
                    SELECT IFNULL(SUM(b.money), 0) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                    FROM flat_bill b
                    WHERE b.room_id = r.id
                    AND b.pay_status = 1
                    AND b.invoice_status = 0
                    AND b.del_status = 0
                ), 0
            ) AS uninvoicedMoney
        FROM
            flat_room r
        WHERE
            r.del_status = 0
            <if test="roomId != null">AND r.id = #{roomId}</if>
        ORDER BY
            r.block_no ASC,
            r.floor_no ASC,
            r.room_no ASC
    </select>

</mapper>
