<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.statistics.StatisticsMapper">

    <select id="selectRoomInvoiceStatistics" resultType="com.flat.logic.dto.resp.statistics.RoomInvoiceStatisticsResp">
        SELECT
            r.id AS roomId,
            r.name AS roomName,
            r.room_no AS roomNo,
            r.block_no AS blockNo,
            r.floor_no AS floorNo,
            IFNULL(
                (
                    SELECT IFNULL(SUM(b.money), 0) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                    FROM flat_bill b
                    WHERE b.room_id = r.id
                    AND b.pay_status = 1
                    AND b.del_status = 0
                ), 0
            ) AS totalMoney,
            IFNULL(
                (
                    SELECT IFNULL(SUM(b.money), 0)
                    FROM flat_bill b
                    WHERE b.room_id = r.id
                    AND b.invoice_status = 1
                    AND b.pay_status = 1
                    AND b.del_status = 0
                ), 0
            ) AS invoicedMoney,
            IFNULL(
                (
                    SELECT IFNULL(SUM(b.money), 0) - IFNULL(SUM(b.refund_money * (b.refund_status = 1)), 0)
                    FROM flat_bill b
                    WHERE b.room_id = r.id
                    AND b.pay_status = 1
                    AND b.invoice_status = 0
                    AND b.del_status = 0
                ), 0
            ) AS uninvoicedMoney
        FROM
            flat_room r
        WHERE
            r.del_status = 0
            <if test="req.roomId != null">AND r.id = #{req.roomId}</if>
            <if test="req.blockNo != null and req.blockNo != ''">AND r.block_no = #{req.blockNo}</if>
            <if test="req.floorNo != null">AND r.floor_no = #{req.floorNo}</if>
            <if test="req.roomNo != null and req.roomNo != ''">AND r.room_no = #{req.roomNo}</if>
            <if test="req.roomName != null and req.roomName != ''">AND r.name LIKE CONCAT('%', #{req.roomName}, '%')</if>
        ORDER BY
            r.block_no ASC,
            r.floor_no ASC,
            r.room_no ASC
    </select>

    <select id="selectActivityStatistics" resultType="com.flat.logic.dto.resp.statistics.ActivityStatisticsResp">
        SELECT
            -- 当年活动总数
            (SELECT COUNT(*) FROM activity WHERE YEAR(create_time) = YEAR(CURDATE()) AND del_status = 0) AS activityCount,

            -- 当年报名总人次
            (SELECT COUNT(*) FROM activity_user au
             JOIN activity a ON au.activity_id = a.id
             WHERE YEAR(a.create_time) = YEAR(CURDATE())
             AND au.del_status = 0
             AND a.del_status = 0) AS registrationCount,

            -- 当年已付费报名人次
            (SELECT COUNT(*) FROM activity_user au
             JOIN activity a ON au.activity_id = a.id
             WHERE YEAR(a.create_time) = YEAR(CURDATE())
             AND au.entry_fee_flag = 1
             AND au.pay_status = 1
             AND au.del_status = 0
             AND a.del_status = 0) AS paidRegistrationCount,

            -- 当年未付费报名人次
            (SELECT COUNT(*) FROM activity_user au
             JOIN activity a ON au.activity_id = a.id
             WHERE YEAR(a.create_time) = YEAR(CURDATE())
             AND ((au.entry_fee_flag = 1 AND au.pay_status = 0) OR au.entry_fee_flag = 0)
             AND au.del_status = 0
             AND a.del_status = 0) AS unpaidRegistrationCount
    </select>

</mapper>
