<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.sundry.SundryProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.sundry.SundryProduct">
        <id property="id" column="id" />
        <result property="categoryId" column="category_id" />
        <result property="number" column="number" />
        <result property="name" column="name" />
        <result property="unit" column="unit" />
        <result property="unitPrice" column="unit_price" />
        <result property="openingInventory" column="opening_inventory" />
        <result property="dayConsumeAmount" column="day_consume_amount" />
        <result property="totalInventory" column="total_inventory" />
        <result property="alarmThreshold" column="alarm_threshold" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
