<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.sundry.SundryProductMapper">

    <resultMap id="SundryProductListRespMap" type="com.flat.logic.dto.resp.sundry.SundryProductListResp" extends="BaseResultMap">
        <result property="categoryName" column="category_name" />
        <result property="availableDays" column="available_days" />
        <result property="alarmStatus" column="alarm_status" />
    </resultMap>


    <!-- selectRespList -->
    <select id="selectRespList" parameterType="com.flat.logic.dto.req.sundry.SundryProductQueryReq" resultMap="SundryProductListRespMap">
        SELECT
            p.*,
            c.name AS category_name,
            <!-- 库存可用天数，总库存量除以日消耗量 -->
            floor(p.total_inventory / p.day_consume_amount) AS available_days,
            <!-- 告警状态，当总库存小于等于告警阈值时为1，否则为0 -->
            CASE WHEN p.total_inventory <![CDATA[ <= ]]> p.alarm_threshold THEN 1 ELSE 0 END AS alarm_status
        FROM
            sundry_product p
            LEFT JOIN sundry_category c ON p.category_id = c.id
        WHERE
            p.del_status = 0
            <if test="req.categoryId != null">AND p.category_id = #{req.categoryId}</if>
            <if test="req.name != null">AND p.name LIKE CONCAT('%', #{req.name}, '%')</if>
            <if test="req.number != null">AND p.number LIKE CONCAT('%', #{req.number}, '%')</if>
            <if test="req.beginCreateTime != null">AND p.create_time <![CDATA[ >= ]]> #{req.beginCreateTime}</if>
            <if test="req.endCreateTime != null">AND p.create_time <![CDATA[ <= ]]> #{req.endCreateTime}</if>
        ORDER BY
            p.create_time DESC
    </select>
</mapper>
