<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.sundry.SundryInventoryLogMapper">

    <resultMap id="SundryInventoryLogListRespMap" type="com.flat.logic.dto.resp.sundry.SundryInventoryLogListResp" extends="BaseResultMap">
        <result property="categoryName" column="category_name" />
        <result property="productName" column="product_name" />
    </resultMap>


    <!-- selectRespList -->

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.sundry.SundryInventoryLogQueryReq" resultMap="SundryInventoryLogListRespMap">
        SELECT
            l.*,
            c.name AS category_name,
            p.name AS product_name
        FROM sundry_inventory_log l
        LEFT JOIN sundry_product p ON l.product_id = p.id
        LEFT JOIN sundry_category c ON p.category_id = c.id
        WHERE l.del_status = 0
        <if test="req.categoryId != null">AND c.id = #{req.categoryId}</if>
        <if test="req.productId != null">AND l.product_id = #{req.productId}</if>
        <if test="req.productName != null">AND p.name LIKE CONCAT('%', #{req.productName}, '%')</if>
        <if test="req.type != null">AND l.type = #{req.type}</if>
        <if test="req.beginCreateTime != null">AND l.create_time <![CDATA[ >= ]]> #{req.beginCreateTime}</if>
        <if test="req.endCreateTime != null">AND l.create_time <![CDATA[ <= ]]> #{req.endCreateTime}</if>
        <if test="req.beginActionTime != null">AND l.action_time <![CDATA[ >= ]]> #{req.beginActionTime}</if>
        <if test="req.endActionTime != null">AND l.action_time <![CDATA[ <= ]]> #{req.endActionTime}</if>
        ORDER BY l.create_time DESC
    </select>
</mapper>
