<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.cost.FlatCostLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.cost.FlatCostLog">
        <id property="id" column="id" />
        <result property="cusId" column="cus_id" />
        <result property="costTypeId" column="cost_type_id" />
        <result property="money" column="money" />
        <result property="price" column="price" />
        <result property="count" column="count" />
        <result property="billId" column="bill_id" />
        <result property="payStatus" column="pay_status" />
        <result property="payMode" column="pay_mode" />
        <result property="payTime" column="pay_time" />
        <result property="deliveryId" column="delivery_id" />
        <result property="description" column="description" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
