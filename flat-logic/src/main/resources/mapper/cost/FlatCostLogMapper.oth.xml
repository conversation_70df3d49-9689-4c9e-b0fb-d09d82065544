<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.cost.FlatCostLogMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.cost.CostLogResp" extends="BaseResultMap">
        <result property="costTypeName" column="cost_type_name"/>
        <result property="cusRealName" column="cus_real_name"/>
        <result property="cusPhoneNumber" column="cus_phone_number"/>
    </resultMap>

    <sql id="RespSelectSQL">
        select 
            l.*, 
            t.name as cost_type_name, 
            cus.real_name as cus_real_name, cus.phone_number as cus_phone_number
        from flat_cost_log l
            left join flat_cost_type t on t.id = l.cost_type_id
            left join cus_user cus on cus.user_id = l.cus_id
    </sql>

    

    <!-- selectRespList --> 
    <select id="selectRespList" parameterType="com.flat.logic.dto.req.cost.CostLogQueryReq" resultMap="RespResultMap">
        <include refid="RespSelectSQL" />
        <where>
            l.del_status = 0
            <if test="deliveryId != null">and l.delivery_id = #{deliveryId}</if>
            <if test="cusId != null">and l.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and cus.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and cus.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="costTypeId != null">and l.cost_type_id = #{costTypeId}</if>
            <if test="minMoney != null">and l.money <![CDATA[>=]]> #{minMoney}</if>
            <if test="maxMoney != null">and l.money <![CDATA[<=]]> #{maxMoney}</if>
            <if test="billId != null">and l.bill_id = #{billId}</if>
            <if test="payStatus != null">and l.pay_status = #{payStatus}</if>
            <if test="payMode != null">and l.pay_mode = #{payMode}</if>
            <if test="beginPayTime != null">and l.pay_time <![CDATA[>=]]> #{beginPayTime}</if>
            <if test="endPayTime != null">and l.pay_time <![CDATA[<=]]> #{endPayTime}</if>
            <if test="beginCreateTime != null">and l.create_time <![CDATA[>=]]> #{beginCreateTime}</if>
            <if test="endCreateTime != null">and l.create_time <![CDATA[<=]]> #{endCreateTime}</if>
            order by l.create_time desc
        </where>
    </select>

    <!-- selectRespById -->
    <select id="selectRespById" resultMap="RespResultMap">
        <include refid="RespSelectSQL" />
        where l.id = #{id} and l.del_status = 0
    </select>
</mapper>
