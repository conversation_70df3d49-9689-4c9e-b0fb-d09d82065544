<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.cost.FlatCostTypeMapper">

    <!-- 查询费用类型列表 -->
    <select id="selectCostTypeList" resultMap="BaseResultMap">
        SELECT 
            t.*
        FROM 
            flat_cost_type t
        <where>
            t.del_status = 0
            <if test="req.name != null and req.name != ''">
                AND t.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.minPrice != null">
                AND t.price <![CDATA[ >= ]]> #{req.minPrice}
            </if>
            <if test="req.maxPrice != null">
                AND t.price <![CDATA[ <= ]]> #{req.maxPrice}
            </if>
            <if test="req.beginCreateTime != null">
                AND t.create_time <![CDATA[ >= ]]> #{req.beginCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND t.create_time <![CDATA[ <= ]]> #{req.endCreateTime}
            </if>
            <if test="req.useDeliveryFlag != null">
                AND t.use_delivery_flag = #{req.useDeliveryFlag}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper>
