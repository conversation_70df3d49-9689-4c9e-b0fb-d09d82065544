<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.cost.FlatCostDeliveryMapper">

    <select id="selectDeliveryList" resultType="com.flat.logic.entity.cost.FlatCostDelivery">
        SELECT d.*
        FROM flat_cost_delivery d
        <if test="costTypeId != null">
        LEFT JOIN flat_cost_type_delivery td ON d.id = td.delivery_id AND td.del_status = 0
        </if>
        <where>
            d.del_status = 0
            <if test="costTypeId != null">
                AND td.cost_type_id = #{costTypeId}
            </if>
            <if test="req.deliveryAddress != null and req.deliveryAddress != ''">
                AND d.delivery_address LIKE CONCAT('%', #{req.deliveryAddress}, '%')
            </if>
            <if test="req.beginDeliveryTime != null">
                AND d.delivery_time <![CDATA[ >= ]]> #{req.beginDeliveryTime}
            </if>
            <if test="req.endDeliveryTime != null">
                AND d.delivery_time <![CDATA[ <= ]]> #{req.endDeliveryTime}
            </if>
            <if test="req.beginCreateTime != null">
                AND d.create_time <![CDATA[ >= ]]> #{req.beginCreateTime}
            </if>
            <if test="req.endCreateTime != null">
                AND d.create_time <![CDATA[ <= ]]> #{req.endCreateTime}
            </if>
        </where>
        ORDER BY d.create_time DESC
    </select>


</mapper>
