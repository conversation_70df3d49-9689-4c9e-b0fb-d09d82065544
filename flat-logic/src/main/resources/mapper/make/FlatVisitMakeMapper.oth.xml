<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatVisitMakeMapper">

    <resultMap id="ListRespResultMap" type="com.flat.logic.dto.resp.make.VisitMakeListResp" extends="BaseResultMap">
        <result property="roomName" column="room_name"/>
        <result property="cusRealName" column="cus_real_name"/>
        <result property="cusPhoneNumber" column="cus_phone_number"/>
        <result property="butlerUsername" column="butler_username"/>
        <result property="propertyUsername" column="property_username"/>
    </resultMap>

    <sql id="selectRespListSQL">
        select
            v.*,
            r.name as room_name, c.real_name as cus_real_name, c.phone_number as cus_phone_number,
            bu.username as butler_username, pu.username as property_username
        from flat_visit_make v
            left join cus_user c on c.user_id = v.cus_id
            left join flat_room r on r.id = v.room_id
            LEFT JOIN mai_user bu ON bu.user_id = v.butler_id
            LEFT JOIN mai_user pu ON pu.user_id = v.property_id
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.make.VisitMakeQueryReq" resultMap="ListRespResultMap">
        <include refid="selectRespListSQL"/>
        <where>
            v.del_status = 0
            <if test="flatId != null">and v.flat_id = #{flatId}</if>
            <if test="roomId != null">and v.room_id = #{roomId}</if>
            <if test="cusId != null">and v.cus_id = #{cusId}</if>
            <if test="leaseId != null">and v.lease_id = #{leaseId}</if>
            <if test="visitorName != null  and visitorName != ''">and v.visitor_name like concat('%', #{visitorName}, '%')</if>
            <if test="visitorIdCard != null and visitorIdCard != ''">and v.visitor_id_card = #{visitorIdCard}</if>
            <if test="visitorPhoneNumber != null and visitorPhoneNumber != ''">and v.visitor_phone_number = #{visitorPhoneNumber}</if>
            <if test="status != null">and v.status = #{status}</if>
            <if test="butlerId != null ">and v.butler_id = #{butlerId}</if>
            <if test="propertyId != null ">and v.property_id = #{propertyId}</if>
        </where>
        order by v.create_time desc
    </select>

</mapper>
