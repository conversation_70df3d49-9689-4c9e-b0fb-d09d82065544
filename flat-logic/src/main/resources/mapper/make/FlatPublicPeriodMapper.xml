<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatPublicPeriodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.make.FlatPublicPeriod">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="publicId" column="public_id" />
        <result property="beginTime" column="begin_time" />
        <result property="endTime" column="end_time" />
        <result property="peopleCount" column="people_count" />
        <result property="description" column="description" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
