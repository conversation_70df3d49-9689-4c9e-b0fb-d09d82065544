<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatPublicMakeMapper">

    <resultMap id="ListRespResultMap" type="com.flat.logic.dto.resp.make.PublicMakeListResp" extends="BaseResultMap">
        <result property="flatName" column="flat_name" />
        <result property="roomName" column="room_name" />
        <result property="cusRealName" column="cus_real_name" />
        <result property="butlerUsername" column="butler_username" />
        <result property="propertyUsername" column="property_username" />
        <result property="publicName" column="public_name" />
        <result property="verifyButterUsername" column="verify_butter_username" />
    </resultMap>

    <sql id="SelectRespListSQL">
        select
            m.*, p.name as public_name, u.real_name as cus_real_name, f.name as flat_name,
            r.name as room_name, bu.username as butler_username, pu.username as property_username,
            vbu.username as verify_butter_username
        from flat_public_make m
            left join flat_public p on p.id = m.public_id
            left join cus_user u on u.user_id = m.cus_id
            left join flat f on f.id = m.flat_id
            left join flat_room r on r.id = m.room_id
            left join mai_user bu on bu.user_id = m.butler_id and bu.user_type='BUTLER'
            left join mai_user pu on pu.user_id = m.property_id and pu.user_type='PROPERTY'
            left join mai_user vbu on vbu.user_id = m.verify_butter_id and vbu.user_type='BUTLER'
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.make.PublicMakeQueryReq" resultMap="ListRespResultMap">
        <include refid="SelectRespListSQL" />
        <where>
            m.del_status = 0
            <if test="keyword != null and keyword != ''">and (
                u.real_name like concat('%', #{keyword}, '%')
                or u.phone_number like concat('%', #{keyword}, '%')
                or r.name like concat('%', #{keyword}, '%'))</if>
            <if test="flatId != null ">and m.flat_id = #{flatId}</if>
            <if test="roomId != null ">and m.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and r.name like concat('%', #{roomName}, '%')</if>
            <if test="cusId != null ">and m.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">and u.real_name like concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and u.phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="publicId != null ">and m.public_id = #{publicId}</if>
            <if test="publicPeriodId != null ">and m.public_period_id = #{publicPeriodId}</if>
            <if test="butlerId != null ">and m.butler_id = #{butlerId}</if>
            <if test="propertyId != null ">and m.property_id = #{propertyId}</if>
            <if test="status != null ">and m.status = #{status}</if>
        </where>
        order by m.create_time desc
    </select>

    <select id="selectRespById" resultMap="ListRespResultMap">
        <include refid="SelectRespListSQL" />
        where m.del_status = 0 and m.id = #{id}
    </select>

    <select id="selectPeopleCount" resultType="java.lang.Integer">
        select COALESCE(sum(people_count), 0)
        from flat_public_make
        where del_status = 0
          and public_id = #{publicId}
          and public_period_id = #{publicPeriodId}
          and make_date = #{makeDate}
          and status in (0, 3, 5)
    </select>
</mapper>
