<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatPublicPeriodMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.make.PublicPeriodResp">
        <id property="id" column="id" />
        <result property="flatName" column="flat_name" />
        <result property="publicName" column="public_name" />
        <result property="makeCount" column="make_count" />
    </resultMap>

    <sql id="SelectRespListSQL">
        select 
        * 
        from
            (select
                    p.*,
                    f.name as flat_name,
                    pub.name as public_name,
                    (select count(*) from flat_public_make pm where pm.public_period_id = p.id and pm.del_status = 0 and pm.make_date = #{makeDate} and pm.`status` in (0, 3, 5)) as make_count
            from flat_public_period p
                    left join flat f on f.id = p.flat_id
                    left join flat_public pub on pub.id = p.public_id
            ) as period
    </sql>

    <select id="selectRespList" resultMap="RespResultMap">
        <include refid="SelectRespListSQL" />
        <where>
            period.del_status = 0
            <if test="flatId != null">and period.flat_id = #{flatId}</if>
            <if test="publicId != null">and period.public_id = #{publicId}</if>
        </where>
        order by period.create_time desc
    </select>

    <select id="selectRespById" resultMap="RespResultMap">
        <include refid="SelectRespListSQL" />
        where period.del_status = 0 and period.id = #{id}
    </select>

    <select id="selectOverlapCount" resultType="java.lang.Integer">
        select count(*)
        from flat_public_period
        where del_status = 0
          and public_id = #{publicId}
          <if test="excludeId != null">
          and id != #{excludeId}
          </if>
          and (
            (begin_time <![CDATA[<=]]> #{beginTime} and end_time <![CDATA[>]]> #{beginTime})
            or (begin_time <![CDATA[<]]> #{endTime} and end_time <![CDATA[>=]]> #{endTime})
            or (begin_time <![CDATA[>=]]> #{beginTime} and end_time <![CDATA[<=]]> #{endTime})
          )
    </select>

</mapper>
