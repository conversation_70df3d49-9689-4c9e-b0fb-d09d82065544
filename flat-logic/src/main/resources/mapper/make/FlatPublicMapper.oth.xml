<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatPublicMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.make.PublicResp" extends="BaseResultMap">
        <result property="periodCount" column="period_count" />
    </resultMap>

    <sql id="SelectRespListSQL">
        select
            pub.*
        from (
            select
                p.*,
                (select count(*) from flat_public_period period where period.public_id = p.id and period.del_status = 0) as period_count
            from flat_public p
        ) as pub
    </sql>

    <select id="selectRespList" resultMap="RespResultMap">
        <include refid="SelectRespListSQL" />
        <where>
            pub.del_status = 0
            <if test="name != null and name != ''">and pub.name like concat('%', #{name}, '%')</if>
            <if test="needPayFlag != null">and pub.need_pay_flag = #{needPayFlag}</if>
        </where>
    </select>

    <select id="selectRespById" resultMap="RespResultMap">
        <include refid="SelectRespListSQL" />
        where pub.id = #{id} and pub.del_status = 0
    </select>

</mapper>
