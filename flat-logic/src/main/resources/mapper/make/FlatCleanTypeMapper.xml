<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatCleanTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.make.FlatCleanType">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="content" column="content" />
        <result property="price" column="price" />
        <result property="needPayFlag" column="need_pay_flag" />
        <result property="services" column="services" />
        <result property="cleaning" column="cleaning" />
        <result property="label" column="label" />
        <result property="themeUrl" column="theme_url" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
