<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatRoomMakeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.make.FlatRoomMake">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="cusName" column="cus_name" />
        <result property="cusPhoneNumber" column="cus_phone_number" />
        <result property="makeTime" column="make_time" />
        <result property="butlerId" column="butler_id" />
        <result property="status" column="status" />
        <result property="cancelTime" column="cancel_time" />
        <result property="refuse" column="refuse" />
        <result property="butlerVerifyTime" column="butler_verify_time" />
        <result property="description" column="description" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
