<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatCleanEvaluateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.make.FlatCleanEvaluate">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="cleanMakeId" column="clean_make_id" />
        <result property="leaseId" column="lease_id" />
        <result property="butlerId" column="butler_id" />
        <result property="propertyId" column="property_id" />
        <result property="staffId" column="staff_id" />
        <result property="manner" column="manner" />
        <result property="timeliness" column="timeliness" />
        <result property="tidiness" column="tidiness" />
        <result property="evaluateTime" column="evaluate_time" />
        <result property="imgUrl" column="img_url" />
        <result property="average" column="average" />
        <result property="description" column="description" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
