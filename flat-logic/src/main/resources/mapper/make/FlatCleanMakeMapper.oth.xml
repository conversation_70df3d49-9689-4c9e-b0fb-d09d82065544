<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatCleanMakeMapper">

    <resultMap type="com.flat.logic.dto.resp.make.CleanMakeListResp" id="RespResultMap" extends="BaseResultMap">
        <result property="cleanTypeName" column="clean_type_name"/>
        <result property="icon" column="icon"/>
        <result property="staffName" column="staff_name"/>
        <result property="cusRealName" column="cus_real_name"/>
        <result property="cusPhoneNumber" column="cus_phone_number"/>
        <result property="flatName" column="flat_name"/>
        <result property="roomName" column="room_name"/>
        <result property="butlerUsername" column="butler_username"/>
        <result property="propertyUsername" column="property_username"/>
    </resultMap>

    <sql id="SelectRespList">
        SELECT
            fc.*,
            fr.name as room_name,
            f.name as flat_name,
            cu.real_name as cus_real_name,
            cu.phone_number as cus_phone_number,
            bu.username as butler_username,
            fs.name as staff_name,
            pu.username as property_username,
            ct.name as clean_type_name
        FROM flat_clean_make fc
                 LEFT JOIN flat_room fr ON fr.id = fc.room_id
                 LEFT JOIN flat f on f.id = fc.flat_id
                 LEFT JOIN cus_user cu ON cu.user_id = fc.cus_id
                 LEFT JOIN mai_user bu ON bu.user_id = fc.butler_id
                 LEFT JOIN flat_staff fs ON fs.id = fc.staff_id
                 LEFT JOIN mai_user pu ON pu.user_id = fc.property_id
                 LEFT JOIN flat_clean_type ct ON ct.id = fc.clean_type_id
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.make.CleanMakeQueryReq" resultMap="RespResultMap">
        <include refid="SelectRespList" />
        <where>
            fc.del_status = 0
            <if test="keyword != null and keyword != ''">AND (fr.name like concat('%', #{keyword}, '%') OR cu.real_name like concat('%', #{keyword}, '%') OR cu.phone_number like concat('%', #{keyword}, '%'))</if>
            <if test="roomId != null">AND fc.room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">AND r.name = concat('%', #{roomName}, '%')</if>
            <if test="cusId != null">AND fc.cus_id = #{cusId}</if>
            <if test="cusRealName != null and cusRealName != ''">AND cu.real_name = concat('%', #{cusRealName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">AND cu.phone_number = concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="butlerId != null">AND fc.butler_id = #{butlerId}</if>
            <if test="propertyId != null">AND fc.property_id = #{propertyId}</if>
            <if test="cleanTypeId != null">AND fc.clean_type_id = #{cleanTypeId}</if>
            <if test="status != null">AND fc.status = #{status}</if>
            <if test="makeTimeStart != null">AND fc.make_time <![CDATA[>=]]> #{makeTimeStart}</if>
            <if test="makeTimeEnd != null">AND fc.make_time <![CDATA[<=]]> #{makeTimeEnd}</if>
            <if test="createTimeStart != null">AND fc.create_time <![CDATA[>=]]> #{createTimeStart}</if>
            <if test="createTimeEnd != null">AND fc.create_time <![CDATA[<=]]> #{createTimeEnd}</if>
            <if test="finishTimeStart != null">AND fc.finish_time <![CDATA[>=]]> #{finishTimeStart}</if>
            <if test="finishTimeEnd != null">AND fc.finish_time <![CDATA[<=]]> #{finishTimeEnd}</if>
        </where>
        ORDER BY fc.create_time DESC
    </select>

    <select id="selectRespById" parameterType="Long" resultMap="RespResultMap">
        <include refid="SelectRespList" />
        WHERE fc.del_status = 0 AND fc.id = #{id}
    </select>

</mapper>
