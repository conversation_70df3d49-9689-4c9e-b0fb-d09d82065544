<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatRoomMakeMapper">

    <resultMap id="ListRespResultMap" type="com.flat.logic.dto.resp.make.RoomMakeListResp" extends="BaseResultMap">
        <result property="flatName" column="flat_name"/>
        <result property="roomName" column="room_name"/>
        <result property="cusAvatar" column="cus_avatar"/>
        <result property="butlerUsername" column="butler_username"/>
    </resultMap>

    <sql id="selectRespListSQL">
        select rm.*, f.name as flat_name, r.name as room_name, c.avatar as cus_avatar, bu.username as butler_username
        from flat_room_make rm
            left join flat f on f.id = rm.flat_id
            left join flat_room r on r.id = rm.room_id
            left join cus_user c on c.user_id = rm.cus_id
            left join mai_user bu on bu.user_id = rm.butler_id and bu.user_type='BUTLER'
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.make.RoomMakeQueryReq" resultMap="ListRespResultMap">
        <include refid="selectRespListSQL"/>
        <where>
            rm.del_status = 0
            <if test="flatId != null ">and rm.flat_id = #{flatId}</if>
            <if test="roomId != null ">and rm.room_id = #{roomId}</if>
            <if test="cusId != null ">and rm.cus_id = #{cusId}</if>
            <if test="cusName != null and cusName != ''">and rm.cus_name like concat('%', #{cusName}, '%')</if>
            <if test="cusPhoneNumber != null and cusPhoneNumber != ''">and rm.cus_phone_number like concat('%', #{cusPhoneNumber}, '%')</if>
            <if test="butlerId != null ">and rm.butler_id = #{butlerId}</if>
            <if test="status != null ">and rm.status = #{status}</if>
        </where>
        order by rm.create_time desc
    </select>

    <select id="selectRespById" parameterType="Long" resultMap="ListRespResultMap">
        <include refid="selectRespListSQL"/>
        where rm.id = #{id} and rm.del_status = 0
    </select>

    <!--
    <select id="selectFlatRoomMakeToday" resultMap="BaseResultMap">
        <include refid="selectFlatRoomMakeVo"/>
        <where>
            del_status=0
            <if test="begin != null  and begin != '' and end != null  and end != '' ">
                and DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') &gt;= #{begin} and DATE_FORMAT(create_time,
                '%Y-%m-%d %H:%i:%s') &lt;= #{end}
            </if>
        </where>
    </select>
-->

</mapper>
