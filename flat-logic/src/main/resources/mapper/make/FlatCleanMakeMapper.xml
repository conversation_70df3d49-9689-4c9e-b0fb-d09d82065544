<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatCleanMakeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.make.FlatCleanMake">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="applyCusType" column="apply_cus_type" />
        <result property="cusId" column="cus_id" />
        <result property="cleanTypeId" column="clean_type_id" />
        <result property="leaseId" column="lease_id" />
        <result property="butlerId" column="butler_id" />
        <result property="propertyId" column="property_id" />
        <result property="makeTime" column="make_time" />
        <result property="money" column="money" />
        <result property="imgUrl" column="img_url" />
        <result property="videoUrl" column="video_url" />
        <result property="needPayFlag" column="need_pay_flag" />
        <result property="payMode" column="pay_mode" />
        <result property="payStatus" column="pay_status" />
        <result property="makeClient" column="make_client" />
        <result property="status" column="status" />
        <result property="cancelTime" column="cancel_time" />
        <result property="butlerVerifyTime" column="butler_verify_time" />
        <result property="propertyVerifyTime" column="property_verify_time" />
        <result property="refuse" column="refuse" />
        <result property="finishTime" column="finish_time" />
        <result property="staffId" column="staff_id" />
        <result property="finishUrl" column="finish_url" />
        <result property="description" column="description" />
        <result property="evaluateFlag" column="evaluate_flag" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
