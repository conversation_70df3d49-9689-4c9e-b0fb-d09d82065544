<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.make.FlatRepairMakeMapper">

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.make.RepairMakeListResp" extends="BaseResultMap">
        <result property="repairTypeName" column="repair_type_name"/>
        <result property="staffName" column="staff_name"/>
        <result property="cusRealName" column="cus_real_name"/>
        <result property="cusPhoneNumber" column="cus_phone_number"/>
        <result property="flatName" column="flat_name"/>
        <result property="roomName" column="room_name"/>
        <result property="butlerUsername" column="butler_username"/>
        <result property="propertyUsername" column="property_username"/>
    </resultMap>

    <sql id="selectRespListSQL">
        select r.*, t.name as repair_type_name, s.name as staff_name, u.real_name as cus_real_name, u.phone_number as cus_phone_number,
               f.name as flat_name,
               fr.name as room_name, bu.username as butler_username, pu.username as property_username
        from flat_repair_make r
                 left join flat_repair_type t on t.id = r.repair_type_id
                 left join flat_staff s on s.id=r.staff_id and s.type=3
                 left join cus_user u on u.user_id=r.cus_id
                 left join flat f on f.id = r.flat_id
                 left join flat_room fr on fr.id=r.room_id
                 left join mai_user bu on bu.user_id=r.butler_id and bu.user_type='BUTLER'
                 left join mai_user pu on pu.user_id=r.property_id and pu.user_type='PROPERTY'
    </sql>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.make.RepairMakeQueryReq" resultMap="RespResultMap">
        <include refid="selectRespListSQL"/>
        <where>
            r.del_status = 0
            <if test="keyword != null and keyword != ''">and (
                u.real_name like concat('%', #{keyword}, '%')
                or u.phone_number like concat('%', #{keyword}, '%')
                or fr.name like concat('%', #{keyword}, '%')
                )
            </if>
            <if test="flatId != null">and r.flat_id = #{flatId}</if>
            <if test="roomId != null">and r.room_id = #{roomId}</if>
            <if test="cusId != null">and r.cus_id = #{cusId}</if>
            <if test="leaseId != null">and r.lease_id = #{leaseId}</if>
            <if test="butlerId != null">and r.butler_id = #{butlerId}</if>
            <if test="propertyId != null">and r.property_id = #{propertyId}</if>
            <if test="repairTypeId != null">and r.repair_type_id = #{repairTypeId}</if>
            <if test="staffId != null">and r.staff_id = #{staffId}</if>
            <if test="evaluateFlag != null">and r.evaluate_flag = #{evaluateFlag}</if>
            <if test="status != null">and r.status = #{status}</if>
        </where>
        order by r.create_time desc
    </select>

    <select id="selectRespById" parameterType="Long" resultMap="RespResultMap">
        <include refid="selectRespListSQL"/>
        where r.id = #{id} and r.del_status = 0
    </select>

    <!--
    <select id="selectFlatRepairsToday" resultMap="BaseResultMap">
        <include refid="selectFlatRepairsVo"/>
        <where>
            del_status=0
            <if test="begin != null  and begin != '' and end != null  and end != '' ">
                and DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') &gt;= #{begin} and DATE_FORMAT(create_time,
                '%Y-%m-%d %H:%i:%s') &lt;= #{end}
            </if>
            <if test="staffId != null ">and staff_id = #{staffId}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>
    -->

</mapper>
