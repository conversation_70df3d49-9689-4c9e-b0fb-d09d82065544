<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.meter.FlatMeterLogMapper">


    <select id="selectLastWaterListGroupByRoom" resultMap="BaseResultMap">
        select
            t1.*
        from (select * from flat_meter_log where type = 1 and del_status = 0) t1
                 INNER JOIN (select max(id) as id from flat_meter_log where type = 1 and del_status = 0 GROUP BY room_id) t2 on t2.id = t1.id;
    </select>

    <select id="selectLastElectricListGroupByRoom" resultMap="BaseResultMap">
        select
            t1.*
        from (select * from flat_meter_log where type = 2 and del_status = 0) t1
                 INNER JOIN (select max(id) as id from flat_meter_log where type = 2 and del_status = 0 GROUP BY room_id) t2 on t2.id = t1.id;
    </select>
    
    <select id="selectBaseList" parameterType="com.flat.logic.dto.req.meter.MeterLogQueryReq" resultMap="BaseResultMap">
        select * from flat_meter_log
        <where>
            del_status = 0
            <if test="accountNo != null and accountNo != ''">and account_no like concat('%', #{accountNo}, '%')</if>
            <if test="roomId != null">and room_id = #{roomId}</if>
            <if test="roomName != null and roomName != ''">and room_name like concat('%', #{roomName}, '%')</if>
            <if test="type != null">and type = #{type}</if>
            <if test="batchNo != null">and batch_no = #{batchNo}</if>
            <if test="startRecordTime != null">AND record_time <![CDATA[>=]]> #{startRecordTime}</if>
            <if test="endRecordTime != null">AND record_time <![CDATA[<=]]> #{endRecordTime}</if>
        </where>
        order by create_time desc
    </select>
</mapper>
