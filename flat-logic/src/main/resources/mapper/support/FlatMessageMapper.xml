<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.support.FlatMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.support.FlatMessage">
        <id property="id" column="id" />
        <result property="type" column="type" />
        <result property="receiveId" column="receive_id" />
        <result property="senderId" column="sender_id" />
        <result property="readStatus" column="read_status" />
        <result property="receiveType" column="receive_type" />
        <result property="senderType" column="sender_type" />
        <result property="skipUrl" column="skip_url" />
        <result property="content" column="content" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
