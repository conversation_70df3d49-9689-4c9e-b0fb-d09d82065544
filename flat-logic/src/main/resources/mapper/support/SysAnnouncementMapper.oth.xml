<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.support.SysAnnouncementMapper">

    <select id="selectBaseList" parameterType="com.flat.logic.dto.req.support.AnnouncementQueryReq" resultMap="BaseResultMap">
        select * from sys_announcement
        <where>
            del_status = 0
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%')</if>
        </where>
        order by create_time desc
    </select>

</mapper>
