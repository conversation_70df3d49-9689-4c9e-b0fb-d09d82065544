<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.support.FlatMessageMapper">

    <select id="selectBaseList" parameterType="com.flat.logic.dto.req.support.MessageQueryReq" resultMap="BaseResultMap">
        select * from flat_message
        <where>
            del_status = 0
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="receiveId != null">and receive_id = #{receiveId}</if>
            <if test="sourceId != null">and source_id = #{sourceId}</if>
            <if test="readStatus != null">and read_status = #{readStatus}</if>
            <if test="receiveType != null and receiveType != ''">and receive_type = #{receiveType}</if>
        </where>
        order by create_time desc
    </select>

    <resultMap id="RespResultMap" type="com.flat.logic.dto.resp.support.FlatMessageResp" extends="BaseResultMap">
        <result property="name" column="name" />
    </resultMap>

    <select id="selectRespList" parameterType="com.flat.logic.dto.req.support.MessageQueryReq" resultMap="RespResultMap">
        select m.*, CASE m.receive_type WHEN 'customer' THEN u.nick_name WHEN 'butler' THEN m1.nick_name WHEN 'property' THEN m2.nick_name ELSE '' END 'name'
        from flat_message  m
        left join cus_user u on u.user_id=m.receive_id and m.receive_type='customer'
        left join mai_user m1 on m1.user_id=m.receive_id and m1.user_type='BUTLER'  and m.receive_type='butler'
        left join mai_user m2 on m2.user_id=m.receive_id and m2.user_type='PROPERTY' and m.receive_type='property'
        <where>
            m.del_status = 0
            <if test="type != null and type != ''">and m.type = #{type}</if>
            <if test="receiveId != null">and m.receive_id = #{receiveId}</if>
            <if test="sourceId != null">and m.source_id = #{sourceId}</if>
            <if test="readStatus != null">and m.read_status = #{readStatus}</if>
            <if test="receiveType != null and receiveType != ''">and m.receive_type = #{receiveType}</if>
        </where>
        order by m.create_time desc
    </select>

</mapper>
