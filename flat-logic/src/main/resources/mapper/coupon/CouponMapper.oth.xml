<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.coupon.CouponMapper">

    <!-- 优惠券响应对象映射 -->
    <resultMap id="CouponRespMap" type="com.flat.logic.dto.resp.coupon.CouponResp" extends="BaseResultMap">
        <result column="cus_real_name" property="cusRealName"/>
        <result column="cus_phone_number" property="cusPhoneNumber"/>
        <result column="expire_status" property="expireStatus"/>
        <result column="owner_verify_name" property="ownerVerifyName"/>
    </resultMap>

    <!-- 查询优惠券列表 -->
    <select id="selectCouponList" parameterType="com.flat.logic.dto.req.coupon.CouponQueryReq" resultMap="CouponRespMap">
        SELECT
            c.*,
            u.real_name AS cus_real_name,
            u.phone_number AS cus_phone_number,
            owner.real_name AS owner_verify_name,
            CASE
                WHEN c.valid_begin_time <![CDATA[>]]> NOW() THEN 0
                WHEN c.valid_end_time <![CDATA[<]]> NOW() THEN 2
                ELSE 1
            END AS expire_status
        FROM
            coupon c
        LEFT JOIN cus_user u ON c.cus_id = u.user_id AND u.del_status = 0
        LEFT JOIN cus_user owner ON c.owner_verify_id = owner.user_id AND owner.del_status = 0
        <where>
            c.del_status = 0
            <if test="cusId != null">
                AND c.cus_id = #{cusId}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND u.phone_number LIKE CONCAT('%', #{phoneNumber}, '%')
            </if>
            <if test="target != null">
                AND c.target = #{target}
            </if>
            <if test="costTypeId != null">
                AND c.cost_type_id = #{costTypeId}
            </if>
            <if test="name != null and name != ''">
                AND c.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="beginValidBeginTime != null">
                AND c.valid_begin_time <![CDATA[ >= ]]> #{beginValidBeginTime}
            </if>
            <if test="endValidBeginTime != null">
                AND c.valid_begin_time <![CDATA[ <= ]]> #{endValidBeginTime}
            </if>
            <if test="beginValidEndTime != null">
                AND c.valid_end_time <![CDATA[ >= ]]> #{beginValidEndTime}
            </if>
            <if test="endValidEndTime != null">
                AND c.valid_end_time <![CDATA[ <= ]]> #{endValidEndTime}
            </if>
            <if test="minAmount != null">
                AND c.amount <![CDATA[ >= ]]> #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND c.amount <![CDATA[ <= ]]> #{maxAmount}
            </if>
            <if test="useStatus != null">
                AND c.use_status = #{useStatus}
            </if>
            <if test="beginUseTime != null">
                AND c.use_time <![CDATA[ >= ]]> #{beginUseTime}
            </if>
            <if test="endUseTime != null">
                AND c.use_time <![CDATA[ <= ]]> #{endUseTime}
            </if>
            <if test="useBillId != null">
                AND c.use_bill_id = #{useBillId}
            </if>
            <if test="verifyStatus != null">
                AND c.verify_status = #{verifyStatus}
            </if>
            <if test="ownerVerifyId != null">
                AND c.owner_verify_id = #{ownerVerifyId}
            </if>
            <if test="beginCreateTime != null">
                AND c.create_time <![CDATA[ >= ]]> #{beginCreateTime}
            </if>
            <if test="endCreateTime != null">
                AND c.create_time <![CDATA[ <= ]]> #{endCreateTime}
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 查询优惠券详情 -->
    <select id="selectCouponDetail" resultMap="CouponRespMap">
        SELECT
            c.*,
            u.real_name AS cus_real_name,
            u.phone_number AS cus_phone_number,
            owner.real_name AS owner_verify_name,
            CASE
                WHEN c.valid_begin_time <![CDATA[>]]> NOW() THEN 0
                WHEN c.valid_end_time <![CDATA[<]]> NOW() THEN 2
                ELSE 1
            END AS expire_status
        FROM
            coupon c
        LEFT JOIN cus_user u ON c.cus_id = u.user_id AND u.del_status = 0
        LEFT JOIN cus_user owner ON c.owner_verify_id = owner.user_id AND owner.del_status = 0
        WHERE
            c.id = #{id}
            AND c.del_status = 0
    </select>

</mapper>
