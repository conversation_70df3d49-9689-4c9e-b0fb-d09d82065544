<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.coupon.CouponTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.coupon.CouponTemplate">
        <id property="id" column="id" />
        <result property="target" column="target" />
        <result property="costTypeId" column="cost_type_id" />
        <result property="name" column="name" />
        <result property="validBeginTime" column="valid_begin_time" />
        <result property="validEndTime" column="valid_end_time" />
        <result property="amount" column="amount" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
