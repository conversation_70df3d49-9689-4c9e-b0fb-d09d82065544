<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.coupon.CouponTemplateMapper">

    <!-- 优惠券模板列表查询结果映射 -->
    <resultMap id="CouponTemplateRespMap" type="com.flat.logic.dto.resp.coupon.CouponTemplateResp" extends="BaseResultMap">
        <result property="totalCount" column="total_count" />
        <result property="usedCount" column="used_count" />
        <result property="unusedCount" column="unused_count" />
        <result property="expiredCount" column="expired_count" />
        <result property="costTypeName" column="cost_type_name" />
    </resultMap>

    <!-- 查询优惠券模板列表并统计相关数量 -->
    <select id="selectTemplateRespList" parameterType="com.flat.logic.dto.req.coupon.CouponTemplateQueryReq" resultMap="CouponTemplateRespMap">
        SELECT 
            t.*,
            IFNULL(coupon_stats.total_count, 0) AS total_count,
            IFNULL(coupon_stats.used_count, 0) AS used_count,
            IFNULL(coupon_stats.unused_count, 0) AS unused_count,
            IFNULL(coupon_stats.expired_count, 0) AS expired_count,
            ct.name AS cost_type_name
        FROM 
            coupon_template t
        LEFT JOIN flat_cost_type ct ON t.cost_type_id = ct.id AND ct.del_status = 0
        LEFT JOIN (
            SELECT 
                template_id,
                COUNT(1) AS total_count,
                SUM(CASE WHEN use_status = 1 THEN 1 ELSE 0 END) AS used_count,
                SUM(CASE WHEN use_status = 0 THEN 1 ELSE 0 END) AS unused_count,
                SUM(CASE WHEN use_status = 0 AND valid_end_time <![CDATA[ < ]]> NOW() THEN 1 ELSE 0 END) AS expired_count
            FROM 
                coupon
            WHERE 
                del_status = 0
            GROUP BY 
                template_id
        ) coupon_stats ON t.id = coupon_stats.template_id
        <where>
            t.del_status = 0
            <if test="name != null and name != ''">
                AND t.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="target != null">
                AND t.target = #{target}
            </if>
            <if test="costTypeId != null">
                AND t.cost_type_id = #{costTypeId}
            </if>
            <if test="beginValidBeginTime != null">
                AND t.valid_begin_time <![CDATA[ >= ]]> #{beginValidBeginTime}
            </if>
            <if test="endValidBeginTime != null">
                AND t.valid_begin_time <![CDATA[ <= ]]> #{endValidBeginTime}
            </if>
            <if test="beginValidEndTime != null">
                AND t.valid_end_time <![CDATA[ >= ]]> #{beginValidEndTime}
            </if>
            <if test="endValidEndTime != null">
                AND t.valid_end_time <![CDATA[ <= ]]> #{endValidEndTime}
            </if>
            <if test="minAmount != null">
                AND t.amount <![CDATA[ >= ]]> #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND t.amount <![CDATA[ <= ]]> #{maxAmount}
            </if>
            <if test="beginCreateTime != null">
                AND t.create_time <![CDATA[ >= ]]> #{beginCreateTime}
            </if>
            <if test="endCreateTime != null">
                AND t.create_time <![CDATA[ <= ]]> #{endCreateTime}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper>
