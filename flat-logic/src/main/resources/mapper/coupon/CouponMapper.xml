<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.coupon.CouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.coupon.Coupon">
        <id property="id" column="id" />
        <result property="templateId" column="template_id" />
        <result property="cusId" column="cus_id" />
        <result property="target" column="target" />
        <result property="costTypeId" column="cost_type_id" />
        <result property="name" column="name" />
        <result property="validBeginTime" column="valid_begin_time" />
        <result property="validEndTime" column="valid_end_time" />
        <result property="amount" column="amount" />
        <result property="verifyStatus" column="verify_status" />
        <result property="ownerVerifyId" column="owner_verify_id" />
        <result property="ownerVerifyTime" column="owner_verify_time" />
        <result property="verifyRefuse" column="verify_refuse" />
        <result property="useStatus" column="use_status" />
        <result property="useTime" column="use_time" />
        <result property="useBillId" column="use_bill_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
