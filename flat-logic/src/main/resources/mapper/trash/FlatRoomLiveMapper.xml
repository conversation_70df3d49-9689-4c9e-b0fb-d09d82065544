<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.trash.FlatRoomLiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.trash.FlatRoomLive">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusId" column="cus_id" />
        <result property="butlerId" column="butler_id" />
        <result property="contractId" column="contract_id" />
        <result property="beginTime" column="begin_time" />
        <result property="endTime" column="end_time" />
        <result property="propertyId" column="property_id" />
        <result property="imgUrl" column="img_url" />
        <result property="name" column="name" />
        <result property="idCard" column="id_card" />
        <result property="state" column="state" />
        <result property="cusTime" column="cus_time" />
        <result property="butlerTime" column="butler_time" />
        <result property="refuse" column="refuse" />
        <result property="reverseUrl" column="reverse_url" />
        <result property="frontUrl" column="front_url" />
        <result property="phonenumber" column="phonenumber" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
