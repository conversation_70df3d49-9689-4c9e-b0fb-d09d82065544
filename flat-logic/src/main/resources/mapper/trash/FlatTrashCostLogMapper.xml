<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.trash.FlatTrashCostLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.trash.FlatTrashCostLog">
        <id property="id" column="id" />
        <result property="iaddr" column="iaddr" />
        <result property="readTime" column="read_time" />
        <result property="showType" column="show_type" />
        <result property="beginNum" column="begin_num" />
        <result property="endNum" column="end_num" />
        <result property="gainTime" column="gain_time" />
        <result property="balance" column="balance" />
        <result property="cost" column="cost" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
