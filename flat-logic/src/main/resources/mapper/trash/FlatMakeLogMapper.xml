<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.trash.FlatMakeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.trash.FlatMakeLog">
        <id property="id" column="id" />
        <result property="delStatus" column="del_status" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <result property="flatId" column="flat_id" />
        <result property="sourceId" column="source_id" />
        <result property="type" column="type" />
        <result property="operateId" column="operate_id" />
        <result property="operateName" column="operate_name" />
        <result property="refuse" column="refuse" />
    </resultMap>

</mapper>
