<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.trash.CusOauthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.trash.CusOauth">
        <id property="id" column="id" />
        <result property="userId" column="user_id" />
        <result property="type" column="type" />
        <result property="wxOpenId" column="wx_open_id" />
        <result property="wxUnionId" column="wx_union_id" />
        <result property="name" column="name" />
        <result property="nickName" column="nick_name" />
        <result property="head" column="head" />
        <result property="gender" column="gender" />
        <result property="updateTime" column="update_time" />
        <result property="createTime" column="create_time" />
    </resultMap>

</mapper>
