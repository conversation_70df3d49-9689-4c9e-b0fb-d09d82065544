<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.trash.FlatTransactionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.trash.FlatTransaction">
        <id property="id" column="id" />
        <result property="flatId" column="flat_id" />
        <result property="roomId" column="room_id" />
        <result property="cusUserId" column="cus_user_id" />
        <result property="billId" column="bill_id" />
        <result property="billTitle" column="bill_title" />
        <result property="moneyFlow" column="money_flow" />
        <result property="billOrderNo" column="bill_order_no" />
        <result property="billTransactionNo" column="bill_transaction_no" />
        <result property="money" column="money" />
        <result property="status" column="status" />
        <result property="payChannel" column="pay_channel" />
        <result property="payTarget" column="pay_target" />
        <result property="payTime" column="pay_time" />
        <result property="accountId" column="account_id" />
        <result property="contractPayId" column="contract_pay_id" />
        <result property="cleanMakeId" column="clean_make_id" />
        <result property="receiveCompanyId" column="receive_company_id" />
        <result property="invoiceStatus" column="invoice_status" />
        <result property="invoiceId" column="invoice_id" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
