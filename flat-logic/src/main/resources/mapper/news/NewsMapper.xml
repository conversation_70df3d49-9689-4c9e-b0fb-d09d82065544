<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flat.logic.mapper.news.NewsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flat.logic.entity.news.News">
        <id property="id" column="id" />
        <result property="categoryId" column="category_id" />
        <result property="bannerPath" column="banner_path" />
        <result property="title" column="title" />
        <result property="author" column="author" />
        <result property="contentType" column="content_type" />
        <result property="content" column="content" />
        <result property="activityId" column="activity_id" />
        <result property="outUrl" column="out_url" />
        <result property="showFlag" column="show_flag" />
        <result property="videoAccountFlag" column="video_account_flag" />
        <result property="videoAccountId" column="video_account_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delStatus" column="del_status" />
    </resultMap>

</mapper>
