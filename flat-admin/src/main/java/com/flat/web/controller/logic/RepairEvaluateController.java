package com.flat.web.controller.logic;

import com.flat.common.annotation.Log;
import com.flat.common.annotation.RepeatSubmit;
import com.flat.common.core.domain.R;
import com.flat.common.core.dto.IdReq;
import com.flat.common.core.page.TablePage;
import com.flat.common.enums.BusinessType;
import com.flat.logic.dto.req.make.RepairEvaluateQueryReq;
import com.flat.logic.entity.make.FlatRepairEvaluate;
import com.flat.logic.service.make.RepairEvaluateService;
import com.flat.security.utils.SecurityUtils;
import com.flat.system.entity.SysUser;
import com.flat.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/logic/repairEvaluate")
@Tag(name = "报修评价管理")
public class RepairEvaluateController extends BaseController {

    @Resource
    private RepairEvaluateService repairEvaluateService;

    @Operation(summary = "查询报修评价列表")
    @PreAuthorize("@ss.hasPermi('flat:repairEvaluate:listPage')")
    @GetMapping("/listPage")
    public R<TablePage<FlatRepairEvaluate>> listPage(RepairEvaluateQueryReq req) {
        return R.successByData(repairEvaluateService.queryPage(req));
    }

    @Operation(summary = "获取报修评价详细信息")
    @PreAuthorize("@ss.hasPermi('flat:repairEvaluate:detail')")
    @GetMapping(value = "/{id}")
    public R<FlatRepairEvaluate> detail(@PathVariable("id") Long id) {
        return R.successByData(repairEvaluateService.queryDetail(id));
    }

    @RepeatSubmit
    @Operation(summary = "删除报修评价")
    @PreAuthorize("@ss.hasPermi('flat:repairEvaluate:remove')")
    @Log(title = "报修评价：删除报修评价", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public R<Object> remove(@Validated @RequestBody IdReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        repairEvaluateService.remove(req.getId(), user);
        return R.success();
    }
}
