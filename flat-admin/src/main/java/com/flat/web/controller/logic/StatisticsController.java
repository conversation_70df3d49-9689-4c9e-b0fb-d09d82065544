package com.flat.web.controller.logic;

import java.util.List;

import com.flat.logic.dto.req.statistics.RoomInvoiceStatisticsQueryReq;
import com.flat.logic.dto.resp.statistics.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import com.flat.common.core.domain.R;
import com.flat.logic.dto.req.statistics.DateRangeStatisticsReq;
import com.flat.logic.dto.req.statistics.YearlyStatisticsReq;
import com.flat.logic.service.statistics.StatisticsService;
import com.flat.web.controller.BaseController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

@RestController
@RequestMapping("/logic/statistics")
@Tag(name = "统计管理")
public class StatisticsController extends BaseController {

    @Resource
    private StatisticsService statisticsService;

    @Operation(summary = "获取今日数据统计")
    @GetMapping("/today")
    public R<TodayStatisticsResp> getTodayStatistics() {
        return R.successByData(statisticsService.getTodayStatistics());
    }

    @Operation(summary = "获取过去20天（包括今天）的每日费用统计")
    @GetMapping("/fee/last20days")
    public R<List<DailyFeeStatisticsResp>> getLast20DaysFeeStatistics() {
        return R.successByData(statisticsService.getLast20DaysFeeStatistics());
    }

    @Operation(summary = "获取发票金额统计")
    @GetMapping("/invoice")
    public R<InvoiceStatisticsResp> getInvoiceStatistics() {
        return R.successByData(statisticsService.getInvoiceStatistics());
    }
    
    @Operation(summary = "获取整体统计数据")
    @GetMapping("/overall")
    public R<OverallStatisticsResp> getOverallStatistics() {
        return R.successByData(statisticsService.getOverallStatistics());
    }

    @Operation(summary = "获取日期范围内每日收入统计")
    @GetMapping("/daterange")
    public R<DateRangeStatisticsResp> getStatisticsByDateRange(@Validated DateRangeStatisticsReq req) {
        return R.successByData(statisticsService.getStatisticsByDateRange(req));
    }

    @Operation(summary = "获取指定年份每月收入统计")
    @GetMapping("/yearly")
    public R<YearlyStatisticsResp> getStatisticsByYear(@Validated YearlyStatisticsReq req) {
        return R.successByData(statisticsService.getStatisticsByYear(req));
    }

    @Operation(summary = "获取房间账单发票统计信息")
    @GetMapping("/room/invoice")
    public R<List<RoomInvoiceStatisticsResp>> getRoomInvoiceStatistics(@Validated RoomInvoiceStatisticsQueryReq req) {
        return R.successByData(statisticsService.getRoomInvoiceStatistics(req));
    }
}
