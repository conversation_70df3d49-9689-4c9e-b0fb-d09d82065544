package com.flat.web.controller.logic;

import java.util.List;

import com.flat.logic.dto.resp.flat.RoomDetailResp;
import com.flat.logic.dto.resp.flat.RoomFinanceResp;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.flat.common.annotation.Log;
import com.flat.common.annotation.RepeatSubmit;
import com.flat.common.core.domain.R;
import com.flat.common.core.dto.IdReq;
import com.flat.common.core.page.TablePage;
import com.flat.common.enums.BusinessType;
import com.flat.logic.dto.req.flat.BlockFloorQueryReq;
import com.flat.logic.dto.req.flat.RoomAccountNoEditReq;
import com.flat.logic.dto.req.flat.RoomAddReq;
import com.flat.logic.dto.req.flat.RoomEditReq;
import com.flat.logic.dto.req.flat.RoomQueryReq;
import com.flat.logic.dto.req.flat.RoomStateQueryReq;
import com.flat.logic.dto.req.flat.RoomStatusChangeReq;
import com.flat.logic.dto.resp.flat.RoomResp;
import com.flat.logic.dto.resp.flat.RoomStateFloorResp;
import com.flat.logic.service.flat.RoomService;
import com.flat.security.utils.SecurityUtils;
import com.flat.system.entity.SysUser;
import com.flat.web.controller.BaseController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;


@RestController
@RequestMapping("/logic/room")
@Tag(name = "房源管理")
public class RoomController extends BaseController {

    @Resource
    private RoomService roomService;

    
    @Operation(summary = "获取所有楼栋号")
    @GetMapping("/blocks")
    public R<List<String>> getAllBlockNos() {
        return R.successByData(roomService.getAllBlockNos());
    }

    @Operation(summary = "根据楼栋号获取楼层列表")
    @GetMapping("/floors")
    public R<List<Integer>> getFloorsByBlock(BlockFloorQueryReq req) {
        return R.successByData(roomService.getFloorsByBlock(req));
    }

    @Operation(summary = "查询房源列表（不分页）")
    @PreAuthorize("@ss.hasPermi('flat:room:list')")
    @GetMapping("/list")
    public R<List<RoomResp>> list(RoomQueryReq req) {
        return R.successByData(roomService.queryRespList(req));
    }

    @Operation(summary = "查询房源列表（分页）")
    @PreAuthorize("@ss.hasPermi('flat:room:listPage')")
    @GetMapping("/listPage")
    public R<TablePage<RoomResp>> listPage(RoomQueryReq req) {
        return R.successByData(roomService.queryRespPage(req));
    }

    @Operation(summary = "获取房源详细信息")
    @PreAuthorize("@ss.hasPermi('flat:room:detail')")
    @GetMapping(value = "/{id}")
    public R<RoomDetailResp> detail(@PathVariable("id") Long id) {
        return R.successByData(roomService.queryRespDetail(id));
    }

    @RepeatSubmit
    @Operation(summary = "新增房源")
    @PreAuthorize("@ss.hasPermi('flat:room:add')")
    @Log(title = "房源：新增房源", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Object> add(@Validated @RequestBody RoomAddReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        roomService.add(req, user);
        return R.success();
    }

    @RepeatSubmit
    @Operation(summary = "修改房源")
    @PreAuthorize("@ss.hasPermi('flat:room:edit')")
    @Log(title = "房源：修改房源", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Object> edit(@Validated @RequestBody RoomEditReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        roomService.edit(req, user);
        return R.success();
    }

    @RepeatSubmit
    @Operation(summary = "修改状态")
    @PreAuthorize("@ss.hasPermi('flat:room:changeStatus')")
    @Log(title = "房源：修改状态", businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus")
    public R<Object> changeStatus(@Validated @RequestBody RoomStatusChangeReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        roomService.changeStatus(req, user);
        return R.success();
    }

    @RepeatSubmit
    @Operation(summary = "修改水电表户号")
    @PreAuthorize("@ss.hasPermi('flat:room:editAccountNo')")
    @Log(title = "房源：修改水电表户号", businessType = BusinessType.UPDATE)
    @PostMapping("/editAccountNo")
    public R<Object> editAccountNo(@Validated @RequestBody RoomAccountNoEditReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        roomService.editAccountNo(req, user);
        return R.success();
    }

    @RepeatSubmit
    @Operation(summary = "删除房源")
    @PreAuthorize("@ss.hasPermi('flat:room:remove')")
    @Log(title = "房源：删除房源", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public R<Object> remove(@Validated @RequestBody IdReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        roomService.remove(req.getId(), user);
        return R.success();
    }

    @Operation(summary = "查询房源状态列表")
    @PreAuthorize("@ss.hasPermi('flat:room:states')")
    @GetMapping("/states")
    public R<List<RoomStateFloorResp>> states(RoomStateQueryReq req) {
        return R.successByData(roomService.queryStates(req));
    }

    @Operation(summary = "查询房源财务统计信息列表")
    @PreAuthorize("@ss.hasPermi('flat:room:financePage')")
    @GetMapping("/financePage")
    public R<TablePage<RoomFinanceResp>> financePage(RoomQueryReq req) {
        return R.successByData(roomService.queryFinanceRespPage(req));
    }

}
