package com.flat.web.controller.logic;

import com.flat.common.core.domain.R;
import com.flat.common.core.dto.IdReq;
import com.flat.common.core.page.TablePage;
import com.flat.logic.dto.req.lease.LeaseQueryReq;
import com.flat.logic.dto.resp.lease.LeaseDetailResp;
import com.flat.logic.dto.resp.lease.LeaseListResp;
import com.flat.logic.service.lease.LeaseService;
import com.flat.security.utils.SecurityUtils;
import com.flat.system.entity.SysUser;
import com.flat.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 租赁管理
 */
@Tag(name = "租赁管理")
@RestController
@RequestMapping("/logic/lease")
public class LeaseController extends BaseController {

    @Resource
    private LeaseService leaseService;

    /**
     * 查询租赁列表
     */
    @Operation(summary = "查询租赁列表")
    @PreAuthorize("@ss.hasPermi('flat:lease:listPage')")
    @GetMapping("/listPage")
    public R<TablePage<LeaseListResp>> listPage(LeaseQueryReq req) {
        return R.successByData(leaseService.queryRespPage(req));
    }

    /**
     * 获取租赁详细信息
     */
    @Operation(summary = "获取租赁详细信息")
    @PreAuthorize("@ss.hasPermi('flat:lease:detail')")
    @GetMapping(value = "/{id}")
    public R<LeaseDetailResp> detail(@PathVariable("id") Long id) {
        return R.successByData(leaseService.queryRespDetail(id));
    }

    // 删除
    @Operation(summary = "删除租赁, 只能删除未签署合同的租赁记录或已取消的租赁记录")
    @PreAuthorize("@ss.hasPermi('flat:lease:delete')")
    @PostMapping(value = "/remove")
    public R<Object> delete(@Validated @RequestBody IdReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        leaseService.remove(req.getId(), user);
        return R.success();
    }

    @Operation(summary = "作废租赁")
    @PreAuthorize("@ss.hasPermi('flat:lease:invalid')")
    @PostMapping(value = "/invalid")
    public R<Object> invalid(@Validated @RequestBody IdReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        leaseService.invalid(req.getId(), user);
        return R.success();
    }

}
